package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.PullDownMenuIntSon;
import com.ruoyi.entity.problem.PullDownMenuStringSon;
import com.ruoyi.entity.problem.PullDownMenuTree;
import com.ruoyi.entity.problem.bo.ProblemBo;
import com.ruoyi.entity.problem.vo.NearProblemVo;
import com.ruoyi.entity.problem.vo.ProblemPullDownMenu;
import com.ruoyi.entity.problem.vo.ProblemStatistics;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.service.problem.IProblemService;
import com.ruoyi.trans.core.TranslateResponse;
import com.ruoyi.trans.utils.TransUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;

/**
 * 故障问题表
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/problem/information")
@SaIgnore
public class ProblemController extends BaseController {

    private final IProblemService iProblemService;

    /**
     * 查询故障列表
     */
    @TranslateResponse(extractPropertiesToTile = true)
    @SaCheckPermission("problem:information:list")
    @PostMapping("/list")
    public TableDataInfo<ProblemVo> list(@RequestBody ProblemBo bo) throws ParseException {
        return iProblemService.queryPageList(bo);
    }

    /**
     * 导出故障列表
     */
    @SaCheckPermission("problem:information:export")
    @Log(title = "故障问题表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestBody ProblemBo bo, HttpServletResponse response) throws ParseException {
        List<ProblemVo> list = iProblemService.queryList(bo);
        TransUtil.doTranslate(list);
        ExcelUtil.exportExcel(list, "故障", ProblemVo.class, response);

    }

    /**
     * 获取故障详细信息
     *
     * @param problemId 主键
     */
    @SaCheckPermission("problem:information:query")
    @GetMapping("/byProblemId/{problemId}")
    @TranslateResponse(extractPropertiesToTile = true)
    public R<ProblemVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long problemId) {
        return R.ok(iProblemService.queryById(problemId));
    }

    /**
     * 新增故障
     */
    @SaCheckPermission("problem:information:add")
    @Log(title = "故障", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProblemBo bo) {
        return toAjax(iProblemService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改故障
     */
    @SaCheckPermission("problem:information:edit")
    @Log(title = "故障", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProblemBo bo) throws ParseException {
        return toAjax(iProblemService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除故障
     *
     * @param problemIds 主键串
     */
    @SaCheckPermission("problem:information:remove")
    @Log(title = "故障", businessType = BusinessType.DELETE)
    @DeleteMapping("/{problemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] problemIds) {
        return toAjax(iProblemService.deleteWithValidByIds(Arrays.asList(problemIds), true) ? 1 : 0);
    }


    /**
     * 根据坐标，已经半径参数，查询范围内所有的故障设备信息
     *
     * @param
     * @param radius 半径
     */
    @SaCheckPermission("problem:information:byDevices")
    @GetMapping("/byDevices/{problemId}/{radius}")
    public R<List<NearProblemVo>> byDevices(
            @PathVariable Long problemId,
            @PathVariable Integer radius) throws JsonProcessingException {
        List<NearProblemVo> list = iProblemService.byDevices(problemId,  radius);
        if (list == null) {
            return R.fail("查询失败，此问题下的设备id不存在");
        }
        return R.ok("操作成功", list);
    }

    /**
     * 查询故障列表的所有下拉菜单
     */
    @SaCheckPermission("problem:information:pullDownMenu")
    @GetMapping("/pullDownMenu")
    public R<ProblemPullDownMenu> pullDownMenu() throws ParseException {
        return R.ok(iProblemService.pullDownMenu());
    }

    /**
     * 查询故障列表的数据来源下拉菜单
     */
    @GetMapping("/pullDownMenuDateSource")
    public R<List<PullDownMenuIntSon>> pullDownMenuDateSource() {
        return R.ok(iProblemService.pullDownMenuDateSource());
    }

    /**
     * 查询故障列表的问题分类树状下拉菜单
     */
    @GetMapping("/pullDownMenuAttrNameTree")
    public R<List<PullDownMenuTree>> pullDownMenuAttrNameTree() {
        return R.ok(iProblemService.pullDownMenuAttrNameTree());
    }

    /**
     * 查询故障列表的问题分类下拉菜单
     */
    @GetMapping("/pullDownMenuAttrName")
    public R<List<PullDownMenuIntSon>> pullDownMenuAttrName() {
        return R.ok(iProblemService.pullDownMenuAttrName());
    }
    /**
     * 查询故障列表的问题一级下拉菜单
     */
    @GetMapping("/pullDownMenuLevel1/{pid}")
    public R<List<PullDownMenuIntSon>> pullDownMenuLevel1(@PathVariable Integer pid) {
        return R.ok(iProblemService.pullDownMenuLevel1(pid));
    }

    /**
     * 查询故障列表的二级分类下拉菜单
     */
    @GetMapping("/pullDownMenuLevel2/{pid}")
    public R<List<PullDownMenuIntSon>> pullDownMenuLevel2(@PathVariable Integer pid) {
        return R.ok(iProblemService.pullDownMenuLevel2(pid));
    }

    /**
     * 查询故障列表的地市下拉菜单
     */
    @GetMapping("/pullDownMenuCity")
    public R<List<PullDownMenuStringSon>> pullDownMenuCity() {
        return R.ok(iProblemService.pullDownMenuCity());
    }

    /**
     * 查询故障列表的区县下拉菜单
     */
    @GetMapping("/pullDownMenuCounty")
    public R<List<PullDownMenuStringSon>> pullDownMenuCounty() {
        return R.ok(iProblemService.pullDownMenuCounty());
    }



    /**
     * 查询故障列表的问题状态下拉菜单
     */
    @GetMapping("/pullDownMenuProblemStatus")
    public R<List<PullDownMenuIntSon>> pullDownMenuProblemStatus() {
        return R.ok(iProblemService.pullDownMenuProblemStatus());
    }

    /**
     * 查询故障列表的严重等级下拉菜单
     */
    @GetMapping("/pullDownMenuGradeName")
    public R<List<PullDownMenuIntSon>> pullDownMenuGradeName() {
        return R.ok(iProblemService.pullDownMenuGradeName());
    }



    /**
     * 统计故障列表的各类型数量
     */
    @SaCheckPermission("problem:information:statistics")
    @GetMapping("/statistics")
    public R<ProblemStatistics> statistics() {
        return R.ok(iProblemService.statistics());
    }

    /**
     * 查找附近线的相关问题
     */
    @TranslateResponse(extractPropertiesToTile = true)
    @GetMapping("/nearFeederProblem/{feederId}/{radius}")
    public R<List<ProblemVo>> nearFeederProblem(@PathVariable String feederId,@PathVariable Double radius) {
        return R.ok(iProblemService.nearFeederProblem(feederId,radius));
    }


}
