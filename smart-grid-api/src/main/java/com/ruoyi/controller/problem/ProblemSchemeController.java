package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.problem.ProblemSchemeMapper;
import com.ruoyi.service.znap.IZnapTopologyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 网架结构变更控制器
 */
@RestController
@RequestMapping("/problem/scheme")
@SaIgnore
public class ProblemSchemeController {

    @Autowired
    private IZnapTopologyService znapTopologyService;

    @Autowired
    private ProblemSchemeMapper problemSchemeMapper;

    /**
     * 获取网架拓扑分析结果
     */
    @GetMapping("/handleTopologyChange")
    public R<SingAnalysis> handleTopologyChange(@RequestParam String feederId) throws JsonProcessingException {
        // 获取拓扑结构
        String jsonStr = problemSchemeMapper.selectById(31707252L).getOperateData();
        ZnapTopology topology = znapTopologyService.generateNode(feederId);
        SingAnalysis singAnalysis = NodeUtils.generateNewTopology(jsonStr, topology);
        // 将网架结构转为军哥算法的拓扑结构入库

        return R.ok();
    }


}