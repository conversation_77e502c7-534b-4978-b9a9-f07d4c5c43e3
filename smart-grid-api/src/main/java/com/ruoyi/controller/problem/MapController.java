package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.mapper.problem.ProblemSchemeMapper;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.znap.IZnapTopologyService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网架结构变更控制器
 */
@RestController
@RequestMapping("/handleTopologyChange")
@SaIgnore
public class MapController {

    @Autowired
    private ISingMapService singMapService;

    @Autowired
    private IZnapTopologyService znapTopologyService;

    @Autowired
    private ProblemSchemeMapper problemSchemeMapper;

    /**
     * 获取网架拓扑分析结果
     */
    @GetMapping("/handleTopologyChange")
    public R<SingAnalysis> handleTopologyChange(@RequestParam String feederId) {
        // 获取拓扑结构
        String jsonStr = problemSchemeMapper.selectById(31707252L).getOperateData();
        generateNodes(jsonStr,feederId);
        return R.ok();
    }


    public List<Node> generateNodes(String jsonStr,String pmsFeederId) {
        ZnapTopology topology = znapTopologyService.generateNode(pmsFeederId);
        ArrayList<Node> nodeList = new ArrayList<>();
        Map<String, Node> nodeMap = new HashMap<>();
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonStr);
            JsonNode devicesNode = rootNode.get("devices");
            for (JsonNode device : devicesNode) {
                String id = device.get("id").asText();
                String psrId = device.has("psrId") ? device.get("psrId").asText() : null;
                String psrType = device.get("psrType").asText();
                String name = device.has("name") ? device.get("name").asText() : null;
                Node node = NodeFactory.createNode(id, psrId, psrType);
                node.setPsrName(name);
                node.setEdge(false);
                nodeList.add(node);
                nodeMap.put(id, node);
            }
            JsonNode edgesNode = rootNode.get("edges");
            for (JsonNode edge : edgesNode) {
                String id = edge.get("id").asText();
                String sourceId = edge.get("sourceId").asText();
                String targetId = edge.get("targetId").asText();
                String psrId = edge.has("psrId") ? edge.get("psrId").asText() : null;
                String psrType = edge.has("psrType") ? edge.get("psrType").asText() : null;
                Node edgeNode = NodeFactory.createNode(id, psrId, psrType);
                edgeNode.setEdge(true);
                // 获取源节点和目标节点
                Node sourceNode = nodeMap.get(sourceId);
                Node targetNode = nodeMap.get(targetId);
                // 建立连接关系
                if (sourceNode != null) {
                    sourceNode.addEdge(edgeNode, true);
                }
                if (targetNode != null) {
                    targetNode.addEdge(edgeNode, false);
                }
                nodeList.add(edgeNode);
                nodeMap.put(id, edgeNode);
            }
            return nodeList;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }


    /**
     * 处理网架变更
     * @param pmsFeederId 馈线ID
     * @param changeJson 变更JSON
     * @return 变更后的拓扑结构
     */
    public ZnapTopology handleTopologyChange(String pmsFeederId, String changeJson) throws JsonProcessingException {
        // 获取原有拓扑结构
        ZnapTopology topology = znapTopologyService.generateNode(pmsFeederId);

        ObjectMapper mapper = new ObjectMapper();
        TopologyModification modification = mapper.readValue(changeJson, TopologyModification.class);

        // 首先处理新增设备
        Map<String, Device> newDevices = new HashMap<>();
        for (Device device : modification.devices) {
            if ("typeSelf".equals(device.type)) {
                newDevices.put(device.id, device);
            }
        }


        // 处理新增连接线
        Map<String, Edge> newEdges = new HashMap<>();
        for (Edge edge : modification.edges) {
            if ("typeSelf".equals(edge.type) && "linkLine".equals(edge.shapeKey)) {
                newEdges.put(edge.id, edge);
                // 将新线ID添加到关联设备中
                if (newDevices.containsKey(edge.sourceId)) {
                    // TODO 在当前节点之后新增一条线，添加设备，添加的新设备和原有的线关联
                    topology.getNodeList().stream().filter(node -> node.getPsrId().equals(edge.sourceId)).forEach(node -> {
                        Node edge1 = new Node(edge.id, edge.id, edge.id, edge.id, true, edge.getType(), edge.getLineType(), edge.getShapeKey());
                        // 设置线的source和target
                        edge1.setSource(node);
                        // target是新增的节点，新增节点之后的线source是
                        node.addEdge(edge1, true);
                    });
                }
            }
        }
        return topology;
    }

    @Data
    public static class Device {
        public String id;
        public String name;
        public String psrType;
        public String psrId;
        public String type;
        public String shapeKey;
        public List<String> edgeIds = new ArrayList<>();

    }

    @Data
    public static class Edge {
        public String id;
        public String sourceId;
        public String targetId;
        public String type;
        public String shapeKey;
        public String lineType;
    }

    @Data
    public static class TopologyModification {
        public List<Device> devices = new ArrayList<>();
        public List<Edge> edges = new ArrayList<>();
    }
}