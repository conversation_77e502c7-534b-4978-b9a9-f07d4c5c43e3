package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.problem.ProblemSchemeMapper;
import com.ruoyi.service.znap.IZnapTopologyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 网架结构变更控制器
 */
@RestController
@RequestMapping("/handleTopologyChange")
@SaIgnore
public class TestHandleTopologyChangeController {

    @Autowired
    private IZnapTopologyService znapTopologyService;

    @Autowired
    private ProblemSchemeMapper problemSchemeMapper;

    /**
     * 获取网架拓扑分析结果
     */
    @GetMapping("/handleTopologyChange")
    public R<SingAnalysis> handleTopologyChange(@RequestParam String feederId) throws JsonProcessingException {
        // 获取拓扑结构
        String jsonStr = problemSchemeMapper.selectById(31707252L).getOperateData();
        SingAnalysis singAnalysis = generateNewTopology(jsonStr, feederId);
        // 将网架结构转为军哥算法的拓扑结构入库

        return R.ok();
    }

    /**
     * 处理网架变更
     * @param jsonStr 变更JSON数据
     * @param pmsFeederId 馈线ID
     * @return 变更后的拓扑结构节点列表
     * @throws JsonProcessingException JSON解析异常
     */
    public SingAnalysis generateNewTopology(String jsonStr, String pmsFeederId) throws JsonProcessingException {
        ZnapTopology topology = znapTopologyService.generateNode(pmsFeederId);
        List<Node> nodes = NodeUtils.planOperateJsonToNodes(jsonStr);

        Map<String, Node> nodeMap = topology.getNodeMap();
        ArrayList<Node> nodeList = topology.getNodeList();

        // 第一步：处理非边节点（设备节点）
        processDeviceNodes(nodes, nodeMap, nodeList);

        // 第二步：处理新增连接线
        processEdgeNodes(nodes, nodeMap, nodeList);
        Node startNode = topology.getStartNode();
        List<Node> kgContactNodes = topology.getKgContactNodes();

        // 节点路径分析
        NodePath nodePath = new NodePath();
        nodePath.analysisPath(startNode, kgContactNodes);
        nodePath.setNodeList(nodeList);
        nodePath.setNodeMap(nodeMap);
        return new SingAnalysis(topology, nodePath);
    }

    /**
     * 处理设备节点
     * @param nodes 所有节点
     * @param nodeMap 节点映射表
     * @param nodeList 节点列表
     */
    private void processDeviceNodes(List<Node> nodes, Map<String, Node> nodeMap, ArrayList<Node> nodeList) {
        for (Node node : nodes) {
            if (!node.isEdge()) {
                if (!nodeMap.containsKey(node.getPsrId())) {
                    nodeList.add(node);
                    nodeMap.put(node.getId(), node);
                }
            }
        }
    }

    /**
     * 处理边节点
     * @param nodes 所有节点
     * @param nodeMap 节点映射表
     * @param nodeList 节点列表
     */
    private void processEdgeNodes(List<Node> nodes, Map<String, Node> nodeMap, ArrayList<Node> nodeList) {
        for (Node node : nodes) {
            // 自定义新增的设备
            //  1、添加到集合里面 nodeList和nodeMap

            // 2、连接关系处理 目前只有新增联络线和住上开关
            if (node.isEdge()) {
                Node source = node.getSource();
                Node target = node.getTarget();
                // 如果链接的是电网设备  需要和整体基础的网价更改
                if (!node.isPsrNode()) {
                    processCustomEdgeNode(node, source, target, nodeMap, nodeList);
                }
            } else {
                // 节点形式后续有可能有其它特殊的 目前暂时没有  先不管
            }
        }
    }

    /**
     * 处理自定义边节点
     * @param node 边节点
     * @param source 源节点
     * @param target 目标节点
     * @param nodeMap 节点映射表
     * @param nodeList 节点列表
     */
    private void processCustomEdgeNode(Node node, Node source, Node target, Map<String, Node> nodeMap, ArrayList<Node> nodeList) {
        // 处理源节点连接
        if (source != null) {
            // 这里的source和基础网架里面的source实际是同一个设备  但不是同一个对象
            Node baseSource = nodeMap.get(source.getPsrId());
            if (baseSource != null) {
                // 删除原有连接
                removeExistingEdgesFromBaseSource(baseSource);
                // 新建连接关系
                baseSource.addEdge(node, true);
            }
        }

        // 处理目标节点连接
        if (target != null) {
            // 这里的target和基础网架里面的target实际是同一个设备  但不是同一个对象
            Node baseTarget = nodeMap.get(target.getPsrId());
            if (baseTarget != null) {
                // 删除原有连接
                removeExistingEdgesFromBaseTarget(baseTarget);
                // 新建连接关系
                baseTarget.addEdge(node, false);
            }
        }

        // 添加边节点到集合
        if (!nodeMap.containsKey(node.getPsrId())) {
            nodeList.add(node);
            nodeMap.put(node.getPsrId(), node);
        }
    }

    /**
     * 删除基础源节点的原有连接
     * @param baseSource 基础源节点
     */
    private void removeExistingEdgesFromBaseSource(Node baseSource) {
        if (baseSource != null) {
            // 先收集要删除的边
            List<Node> edgesToRemove = new ArrayList<>();
            for (Node e : baseSource.getEdges()) {
                if (e.getSource() != null && e.getSource().getPsrId().equals(baseSource.getPsrId())) {
                    edgesToRemove.add(e);
                }
            }
            for (Node e : edgesToRemove) {
                baseSource.removeEdge(e, true);
            }
        }
    }

    /**
     * 删除基础目标节点的原有连接（保持原有逻辑）
     * @param baseTarget 基础目标节点
     */
    private void removeExistingEdgesFromBaseTarget(Node baseTarget) {
        if (baseTarget != null) {
            // 先收集要删除的边
            List<Node> edgesToRemove = new ArrayList<>();
            for (Node e : baseTarget.getEdges()) {
                if (e.getTarget() != null && e.getTarget().getPsrId().equals(baseTarget.getPsrId())) {
                    edgesToRemove.add(e);
                }
            }
            for (Node e : edgesToRemove) {
                baseTarget.removeEdge(e, false);
            }
        }
    }

}