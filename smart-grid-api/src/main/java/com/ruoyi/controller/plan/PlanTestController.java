package com.ruoyi.controller.plan;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.device.bo.QueryDevBo;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.service.plan.TestService;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.SegBetweenVo;
import com.ruoyi.service.plan.IPlanTestService;
import com.ruoyi.service.text.TrendTextCalcServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 方案
 */
@RestController
@RequestMapping("/planTest")
@SaIgnore
public class PlanTestController {

    @Autowired
    IPlanTestService iPlanTestService;


    @Autowired
    TrendTextCalcServiceImpl textTrendCalcService;

    @Autowired
    TestService testService;

    /**
     * 用于测试线段
     */
    @GetMapping("/getSegList")
    public R<ArrayList<SegBetweenVo>> getSegList(@RequestParam String feederId, @RequestParam(required = false) String deviceId) {

        ArrayList<SegBetween> segBetweenList = iPlanTestService.getSegBetweenList(feederId, deviceId);
        return R.ok(NodeUtils.toSegBetweenVoList(segBetweenList));
    }

    /**
     * 用于测试线段
     */
    @PostMapping("/generatePlan")
    public R<List<Plan>> getSegList(@RequestBody GeneratePlanBo generatePlanBo) {

        List<Plan> plans = iPlanTestService.generateGridPlan(generatePlanBo);
        return R.ok(plans);
    }

    /**
     * 用于测试线段
     */
    @GetMapping("/getQueryDevs")
    public R<List<QueryDevBo>> getSegList() {
        return R.ok(testService.query());
    }

    /**
     * 获取联络开关
     */
    @GetMapping("/getContactKgs")
    public R<ArrayList<NodeVo>> getContactKgs(@RequestParam String feederId) {

        ArrayList<Node> result = iPlanTestService.getContactKgs(feederId);
        return R.ok(NodeUtils.toNodeVos(result));
    }

    /**
     * 获取联络开关对应各个主干路径都主干开关的各个分段
     */
    @GetMapping("/getAllContactKgSegList")
    public R<List<HashMap<String, Object>>> getAllContactKgSegList(@RequestParam String feederId) {
        return R.ok(iPlanTestService.getAllContactKgSegList(feederId));
    }

    /**
     * 获取主干路径
     */
    @GetMapping("/getMainPath")
    public R<List<NodeVo>> getMainPath(@RequestParam String feederId) {
        return R.ok(NodeUtils.toNodeVos(iPlanTestService.getMainPath(feederId)));
    }

}
