package com.ruoyi.util.map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.graphhopper.util.shapes.GHPoint;
import com.ruoyi.entity.map.vo.RouteVo;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;

public class MapAPICall {
    // 信任所有证书的TrustManager（仅用于测试环境）
    private static TrustManager[] trustAllCerts = new TrustManager[]{
            new X509TrustManager() {
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                public void checkClientTrusted(X509Certificate[] certs, String authType) throws CertificateException {}
                public void checkServerTrusted(X509Certificate[] certs, String authType) throws CertificateException {}
            }
    };

    // 信任所有主机名（仅用于测试环境）
    private static HostnameVerifier trustAllHosts = new HostnameVerifier() {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    };

    public static RouteVo extractPolylines(String jsonStr) {
        List<GHPoint> polylines = new ArrayList<>();
        List<GHPoint> gtlines = new ArrayList<>();

        try {
            // 解析JSON根对象
            JSONObject root = JSON.parseObject(jsonStr);

            // 安全获取route字段
            Object routeObj = root.get("route");
            if (routeObj == null) {
                System.err.println("JSON中缺少'route'字段");
                return null;
            }

            // 处理route可能是对象或数组的情况
            if (routeObj instanceof JSONArray) {
                // 情况1: route是数组
                JSONArray routes = (JSONArray) routeObj;
                processRoutes(routes, polylines,gtlines);
            } else if (routeObj instanceof JSONObject) {
                // 情况2: route是单个对象
                JSONArray routes = new JSONArray();
                routes.add(routeObj);
                processRoutes(routes, polylines,gtlines);
            } else {
                System.err.println("'route'字段类型不支持: " + routeObj.getClass().getName());
            }

        } catch (Exception e) {
            System.err.println("JSON解析错误: " + e.getMessage());
            e.printStackTrace();
        }

        return new RouteVo(polylines,gtlines);
    }

    public static RouteVo qxPolylines(String jsonStr) {
        List<GHPoint> polylines = new ArrayList<>();
        List<GHPoint> gtlines = new ArrayList<>();

        try {
            // 解析JSON根对象
            JSONObject root = JSON.parseObject(jsonStr);

            // 安全获取route字段
            Object routeObj = root.get("data");
            if (routeObj == null) {
                System.err.println("JSON中缺少'route'字段");
                return null;
            }

            // 处理route可能是对象或数组的情况
            if (routeObj instanceof JSONArray) {
                // 情况1: route是数组
                JSONArray routes = (JSONArray) routeObj;
                processRoutes(routes, polylines,gtlines);
            } else if (routeObj instanceof JSONObject) {
                // 情况2: route是单个对象
                JSONArray routes = new JSONArray();
                routes.add(routeObj);
                processRoutes(routes, polylines,gtlines);
            } else {
                System.err.println("'route'字段类型不支持: " + routeObj.getClass().getName());
            }

        } catch (Exception e) {
            System.err.println("JSON解析错误: " + e.getMessage());
            e.printStackTrace();
        }

        return new RouteVo(polylines,gtlines);
    }
    // 解析分号分隔的坐标字符串
    private static List<GHPoint> parseCoordinateString(String coordinateStr) {
        List<GHPoint> points = new ArrayList<>();

        if (coordinateStr == null || coordinateStr.isEmpty()) {
            return points;
        }

        // 使用分号分割多个坐标点
        String[] coordinatePairs = coordinateStr.split(";");
        for (String pair : coordinatePairs) {
            // 使用逗号分割经度和纬度
            String[] coords = pair.split(",");
            if (coords.length == 2) {
                try {
                    double longitude = Double.parseDouble(coords[0].trim());
                    double latitude = Double.parseDouble(coords[1].trim());
                    points.add(new GHPoint(latitude,longitude));
                } catch (NumberFormatException e) {
//                    System.err.println("坐标格式错误: " + pair);
                    e.printStackTrace();
                }
            } else {
//                System.err.println("坐标对格式错误: " + pair);
            }
        }

        return points;
    }
    // 处理route数组的辅助方法
    private static void processRoutes(JSONArray routes, List<GHPoint> polylines,List<GHPoint> cflines) {
        List<GHPoint> gtlines = new ArrayList<>();
        for (int i = 0; i < routes.size(); i++) {
            Object routeItem = routes.get(i);
            if (!(routeItem instanceof JSONObject)) {
                System.err.println("route元素不是JSONObject类型: " + i);
                continue;
            }

            JSONObject route = (JSONObject) routeItem;

            // 安全获取paths数组
            Object pathsObj = route.get("paths");
            if (pathsObj instanceof JSONArray) {
                JSONArray paths = (JSONArray) pathsObj;

                // 遍历每个路径段
                for (int j = 0; j < paths.size(); j++) {
                    Object pathItem = paths.get(j);
                    if (pathItem instanceof JSONObject) {
                        JSONObject path = (JSONObject) pathItem;

                        // 安全获取steps数组
                        Object stepsObj = path.get("steps");
                        if (stepsObj instanceof JSONArray) {
                            JSONArray steps = (JSONArray) stepsObj;

                            // 遍历每个步骤
                            for (int k = 0; k < steps.size(); k++) {
                                Object stepItem = steps.get(k);
                                if (stepItem instanceof JSONObject) {
                                    JSONObject step = (JSONObject) stepItem;

                                    // 提取当前步骤的polyline
                                    String polyline = step.getString("polyline");
                                    if (polyline != null && !polyline.isEmpty()) {
                                        List<GHPoint> points = parseCoordinateString(polyline);
                                        gtlines.addAll(points);
                                        if(polylines.size()>0){
                                            if(points.get(0).getLon()==polylines.get(polylines.size()-1).getLon()&&points.get(0).getLat()==polylines.get(polylines.size()-1).getLat()){
                                                points.remove(0);
                                            }
                                        }

                                        polylines.addAll(points);

                                    }

                                    // 某些步骤可能包含tmcs数组
                                    Object tmcsObj = step.get("tmcs");
                                    if (tmcsObj instanceof JSONArray) {
                                        JSONArray tmcs = (JSONArray) tmcsObj;
                                        for (int m = 0; m < tmcs.size(); m++) {
                                            Object tmcItem = tmcs.get(m);
                                            if (tmcItem instanceof JSONObject) {
                                                JSONObject tmc = (JSONObject) tmcItem;
                                                String tmcPolyline = tmc.getString("polyline");
                                                if (tmcPolyline != null && !tmcPolyline.isEmpty()) {
                                                    List<GHPoint> points = parseCoordinateString(tmcPolyline);
                                                    if(gtlines.size()>0){
                                                        if(points.get(0).getLon()==gtlines.get(gtlines.size()-1).getLon()&&points.get(0).getLat()==gtlines.get(gtlines.size()-1).getLat()){
                                                            cflines.add(points.get(0));
                                                        }
                                                    }
                                                    gtlines.addAll(points);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public static String sendGetRequest(String urlStr, String token) throws Exception {
        // 生产环境使用（验证证书）
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 如果是HTTPS请求且需要忽略证书验证（测试环境）
        if (connection instanceof HttpsURLConnection) {
            // 取消注释下面三行代码可禁用证书验证（不推荐用于生产环境）

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            ((HttpsURLConnection) connection).setSSLSocketFactory(sc.getSocketFactory());
            ((HttpsURLConnection) connection).setHostnameVerifier(trustAllHosts);

        }

        connection.setRequestMethod("GET");
        connection.setRequestProperty("Authorization", token);

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();
            return response.toString();
        } else {
            throw new Exception("HTTP request failed with response code: " + responseCode);
        }
    }



}
