package com.ruoyi.util.map;

import com.graphhopper.util.shapes.GHPoint;
import com.ruoyi.entity.map.PathSegment;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class PathSegmentationUtil {
    private static final double EARTH_RADIUS = 6371000; // 地球半径（米）

    /**
     * 计算路径总长度并根据杆塔位置分割路径（使用经纬度）
     */
    public  List<PathSegment> segmentRouteByTowers(List<GHPoint> routeList, List<GHPoint> GTList) {
        if (routeList == null || routeList.size() < 2 || GTList == null || GTList.isEmpty()) {
            return Collections.emptyList();
        }

        double[] cumulativeDistances = calculateCumulativeDistances(routeList);
        double totalLength = cumulativeDistances[cumulativeDistances.length - 1];

        List<TowerPosition> towerPositions = new ArrayList<>();
        for (GHPoint tower : GTList) {
            double position = findPositionOnRoute(tower, routeList, cumulativeDistances);
            towerPositions.add(new TowerPosition(tower, position));
        }

        towerPositions.sort(Comparator.comparingDouble(tp -> tp.position));

        List<PathSegment> segments = new ArrayList<>();
        for (int i = 0; i < towerPositions.size() - 1; i++) {
            TowerPosition startTower = towerPositions.get(i);
            TowerPosition endTower = towerPositions.get(i + 1);
            double segmentLength = endTower.position - startTower.position;
            segments.add(new PathSegment(startTower.point, endTower.point, segmentLength));
        }

        return segments;
    }

    /**
     * 计算路径上每个点的累计距离
     */
    public double[] calculateCumulativeDistances(List<GHPoint> routeList) {
        double[] distances = new double[routeList.size()];
        distances[0] = 0.0;

        int a = 0;
        for (int i = 1; i < routeList.size(); i++) {
            a= i;
            distances[i] = distances[i - 1] + calculateDistance(routeList.get(i - 1), routeList.get(i));
        }
        return distances;
    }

    /**
     * 使用Haversine公式计算两点间的球面距离（经纬度）
     */
    private  double calculateDistance(GHPoint p1, GHPoint p2) {
        double lon1 = Math.toRadians(p1.getLon());
        double lat1 = Math.toRadians(p1.getLat());
        double lon2 = Math.toRadians(p2.getLon());
        double lat2 = Math.toRadians(p2.getLat());

        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;

        double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                   Math.cos(lat1) * Math.cos(lat2) *
                   Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));


        return EARTH_RADIUS * c; // 返回距离（米）
    }

    /**
     * 查找杆塔在路径上的位置（经纬度版本）
     */
    private  double findPositionOnRoute(GHPoint tower, List<GHPoint> routeList, double[] cumulativeDistances) {
        for (int i = 0; i < routeList.size() - 1; i++) {
            GHPoint p1 = routeList.get(i);
            GHPoint p2 = routeList.get(i + 1);

            // 计算点到线段的最小距离
            double minDistance = calculateMinDistanceToSegment(tower, p1, p2);

            // 如果距离足够近（阈值可调整），认为杆塔在线段上
            if (minDistance < 10) { // 阈值10米，可根据实际情况调整
                double segmentLength = calculateDistance(p1, p2);
                double ratio = calculateProjectionRatio(tower, p1, p2);
                return cumulativeDistances[i] + ratio * segmentLength;
            }
        }

        return 0.0; // 未找到匹配线段
    }

    /**
     * 计算点到线段的最小距离（经纬度版本）
     */
    private  double calculateMinDistanceToSegment(GHPoint p, GHPoint p1, GHPoint p2) {
        // 计算线段向量
        double dx = Math.toRadians(p2.getLon() - p1.getLon());
        double dy = Math.toRadians(p2.getLat() - p1.getLat());

        // 计算点到线段起点的向量
        double pdx = Math.toRadians(p.getLon() - p1.getLon());
        double pdy = Math.toRadians(p.getLat() - p1.getLat());

        // 计算线段长度的平方
        double segmentLengthSq = dx*dx + dy*dy;

        // 计算投影比例t
        double t = Math.max(0, Math.min(1, (pdx * dx + pdy * dy) / segmentLengthSq));

        // 计算投影点
        double projectionLon = Math.toDegrees(Math.toRadians(p1.getLon()) + t * dx);
        double projectionLat = Math.toDegrees(Math.toRadians(p1.getLat()) + t * dy);

        // 计算点到投影点的距离
        return calculateDistance(p, new GHPoint(projectionLon, projectionLat));
    }

    /**
     * 计算点在直线上的投影比例（经纬度版本）
     */
    private  double calculateProjectionRatio(GHPoint p, GHPoint p1, GHPoint p2) {
        // 简化版本：使用平面投影比例，适用于短距离
        double dx = Math.toRadians(p2.getLon() - p1.getLon());
        double dy = Math.toRadians(p2.getLat() - p1.getLat());

        double pdx = Math.toRadians(p.getLon() - p1.getLon());
        double pdy = Math.toRadians(p.getLat() - p1.getLat());

        double segmentLengthSq = dx*dx + dy*dy;

        if (segmentLengthSq == 0) return 0.0;

        return Math.max(0.0, Math.min(1.0, (pdx * dx + pdy * dy) / segmentLengthSq));
    }

    /**
     * 路径段数据结构
     */


    /**
     * 杆塔位置数据结构
     */
    private  class TowerPosition {
        private final GHPoint point;
        private final double position;

        public TowerPosition(GHPoint point, double position) {
            this.point = point;
            this.position = position;
        }
    }



    /**
     * 示例使用方法
     */
//    public static void main(String[] args) {
//        // 创建示例路径
//        List<GHPoint> routeList = new ArrayList<>();
//        routeList.add(new GHPoint(116.397128, 39.916527)); // 北京
//        routeList.add(new GHPoint(117.200983, 39.084158)); // 天津
//        routeList.add(new GHPoint(118.767413, 32.041544)); // 南京
//
//        // 创建示例杆塔
//        List<GHPoint> GTList = new ArrayList<>();
//        // 假设杆塔位于路径上的某些点
//        GTList.add(new GHPoint(116.397128, 39.916527)); // 北京
//        GTList.add(new GHPoint(117.200983, 39.084158)); // 天津
//
//        // 分割路径
//        List<PathSegment> segments = segmentRouteByTowers(routeList, GTList);
//
//        // 输出结果
//        System.out.println("路径总长度: " + calculateCumulativeDistances(routeList)[routeList.size()-1] + " 米");
//        System.out.println("分割后的路径段:");
//        for (PathSegment segment : segments) {
//            System.out.printf("起点: (%.6f, %.6f), 终点: (%.6f, %.6f), 长度: %.2f 米%n",
//                segment.getStartTower().getLon(), segment.getStartTower().getLat(),
//                segment.getEndTower().getLon(), segment.getEndTower().getLat(),
//                segment.getLength());
//        }
//    }
}
