package com.ruoyi.util.coordinates;

import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.utils.StringUtils;
import org.locationtech.jts.geom.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CoordinateConverter {

    static GeometryFactory geometryFactory = new GeometryFactory();

    /**
     * 将经纬度字符串转换为double数组 [lon, lat]
     *
     * @param lon 经度字符串
     * @param lat 纬度字符串
     * @return double数组 [lon, lat]，如果解析失败则返回null
     */
    public static double[] convert(String lon, String lat) {
        if (lon == null || lat == null) {
            return null;
        }

        try {
            double longitude = parseCoordinate(lon);
            double latitude = parseCoordinate(lat);

            // 验证经纬度范围
            if (!isValidLongitude(longitude) || !isValidLatitude(latitude)) {
                throw new IllegalArgumentException("Coordinate out of range");
            }

            return new double[]{longitude, latitude};
        } catch (IllegalArgumentException e) {
            System.err.println("Failed to convert coordinates: " + e.getMessage());
            return null;
        }
    }

    /**
     * 解析单个坐标字符串
     */
    private static double parseCoordinate(String coord) {
        // 去除空格并替换逗号为小数点
        String cleaned = coord.trim().replace(',', '.');

        // 验证是否为合法数字格式
        if (!cleaned.matches("[-+]?[0-9]*\\.?[0-9]+")) {
            throw new NumberFormatException("Invalid coordinate format: " + cleaned);
        }

        return Double.parseDouble(cleaned);
    }


    /**
     * 将字符串(118.75496647568387 32.019883614089096 118.75496739045269 32.01988352622447)坐标转成二维数组
     *
     * @param coordinateStr
     * @return
     */
    public static List<List<Double>> parseCoordinates(String coordinateStr) {
        if (coordinateStr == null || coordinateStr.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // 按空格分割字符串
        String[] tokens = coordinateStr.trim().split("\\s+");

        // 检查是否有偶数个数字（确保可以两两分组）
        if (tokens.length % 2 != 0) {
            throw new IllegalArgumentException("坐标点数量必须为偶数（经度和纬度成对）");
        }

        List<List<Double>> result = new ArrayList<>();
        for (int i = 0; i < tokens.length; i += 2) {
            // 提取经度和纬度
            double longitude = Double.parseDouble(tokens[i]);
            double latitude = Double.parseDouble(tokens[i + 1]);

            // 添加到结果列表
            result.add(Arrays.asList(longitude, latitude));
        }

        return result;
    }

    /**
     * 将字符串(118.75496647568387,32.019883614089096)坐标转成二维数组
     *
     * @param coordinateStr
     * @return
     */
    public static List<Double> parseCommaCoordinates(String coordinateStr, String split) {
        if (coordinateStr == null || coordinateStr.trim().isEmpty()) {
            return new ArrayList<>(); // 或返回空列表，根据业务需求
        }
        if (StringUtils.isBlank(split)) {
            split = ",";
        }

        // 按逗号分割字符串
        String[] parts = coordinateStr.split(split);

        // 转换为 Double 列表
        return Arrays.stream(parts)
                .map(String::trim) // 去除可能的空格
                .map(Double::parseDouble)
                .collect(Collectors.toList());
    }

    /**
     * 将二维数组的坐标coords转为LineString
     *
     * @param coords [[110,39],[]]
     * @return
     */
    public static LineString toLineString(List<List<Double>> coords) {

        Coordinate[] coordsList = coords.stream().map(lngLat -> new Coordinate(lngLat.get(0), lngLat.get(1))).toArray(Coordinate[]::new);

        return geometryFactory.createLineString(coordsList);
    }

    /**
     * 将二维坐标数组转换为LineString
     * @param coords 二维double数组 [[x1,y1], [x2,y2], ...]
     * @return LineString几何对象
     */
    public static LineString toLineString(double[][] coords) {
        Coordinate[] coordinates = Arrays.stream(coords)
                .map(arr -> new Coordinate(arr[0], arr[1]))
                .toArray(Coordinate[]::new);
        return geometryFactory.createLineString(coordinates);
    }

    /**
     * 将FastJSON的JSONArray转换为LineString
     * @param jsonArray 坐标JSON数组，支持格式：
     *                  - [[x1,y1], [x2,y2], ...]
     *                  - [x1,y1, x2,y2, ...] (平面数组)
     * @return LineString几何对象
     * @throws IllegalArgumentException 当输入格式无效时抛出
     */
    public static LineString jsonArrayToLineString(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) {
            throw new IllegalArgumentException("坐标数组不能为空");
        }

        try {
            // 情况1：嵌套数组格式 [[x1,y1], [x2,y2], ...]
            if (jsonArray.get(0) instanceof JSONArray) {
                Coordinate[] coordinates = new Coordinate[jsonArray.size()];
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONArray point = jsonArray.getJSONArray(i);
                    coordinates[i] = new Coordinate(
                            point.getDoubleValue(0),
                            point.getDoubleValue(1)
                    );
                }
                return geometryFactory.createLineString(coordinates);
            }
            // 情况2：平面数组格式 [x1,y1, x2,y2, ...]
            else if (jsonArray.get(0) instanceof Number) {
                if (jsonArray.size() % 2 != 0) {
                    throw new IllegalArgumentException("平面坐标数组长度必须是偶数");
                }
                Coordinate[] coordinates = new Coordinate[jsonArray.size() / 2];
                for (int i = 0; i < coordinates.length; i++) {
                    coordinates[i] = new Coordinate(
                            jsonArray.getDoubleValue(i * 2),
                            jsonArray.getDoubleValue(i * 2 + 1)
                    );
                }
                return geometryFactory.createLineString(coordinates);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("JSON坐标转换失败: " + e.getMessage(), e);
        }

        throw new IllegalArgumentException("不支持的JSON坐标格式");
    }

    /**
     * Point转为数组的坐标系
     */
    public static List<Double> pointToLngLat(Point point) {
        return Arrays.asList(point.getX(), point.getY());
    }

    /**
     * List<Double> 类型转为double类型
     */
    public static double[] listToArr(List<Double> doubles) {
        return doubles.stream().mapToDouble(Double::doubleValue).toArray();
    }


    /**
     * 将三维字符串坐标，解析成List<List<double[]>>的格式坐标，
     *
     * @param geoList 三维坐标字符串
     * @return
     */
    public static List<List<double[]>> split(String geoList) {
        // 1. 按逗号分割为子串
        String[] lineStrings = geoList.split(",");

        List<List<double[]>> groupedCoordinates = new ArrayList<>();

        for (String line : lineStrings) {
            String trimmedLine = line.trim();
            if (trimmedLine.isEmpty()) continue;

            // 2. 按空格分割为坐标对
            String[] points = trimmedLine.split(" ");
            List<double[]> coordinates = new ArrayList<>();

            for (int i = 0; i < points.length; i += 2) {
                if (i + 1 >= points.length) {
                    throw new IllegalArgumentException("坐标点数为奇数，无法配对: " + trimmedLine);
                }
                double x = Double.parseDouble(points[i]);
                double y = Double.parseDouble(points[i + 1]);
                coordinates.add(new double[]{x, y});
            }

            // 3. 将坐标按两个一组进行分组

            groupedCoordinates.add(coordinates);
        }
        return groupedCoordinates;
    }

    /**
     * 转为Point点坐标
     */
    public static Point toPoint(double lng, double lat) {
        return geometryFactory.createPoint(new Coordinate(lng, lat));
    }

    /**
     * 转为Point点坐标
     */
    public static Point toPoint(List<Double> coords) {
        return geometryFactory.createPoint(new Coordinate(coords.get(0), coords.get(1)));
    }


    /**
     * 计算两个经纬度坐标的中点
     *
     * @param lon1 第一个点经度
     * @param lat1 第一个点纬度
     * @param lon2 第二个点经度
     * @param lat2 第二个点纬度
     * @return 中点坐标 [经度, 纬度]
     */
    public static List<Double> getMidpoint(double lon1, double lat1, double lon2, double lat2) {
        // 1. 转换为弧度
        double lon1Rad = Math.toRadians(lon1);
        double lat1Rad = Math.toRadians(lat1);
        double lon2Rad = Math.toRadians(lon2);
        double lat2Rad = Math.toRadians(lat2);

        // 2. 计算半差
        double dLon = (lon2Rad - lon1Rad) / 2;
        double dLat = (lat2Rad - lat1Rad) / 2;

        // 3. 球面中点公式（使用半正矢公式）
        double a = Math.sin(dLat) * Math.sin(dLat) +
                Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                        Math.sin(dLon) * Math.sin(dLon);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double midLat = Math.atan2(
                Math.sin(lat1Rad) + Math.sin(lat2Rad) * Math.cos(c),
                Math.sqrt((Math.cos(lat1Rad) + Math.cos(lat2Rad) * Math.cos(c)) *
                        (Math.cos(lat1Rad) + Math.cos(lat2Rad) * Math.cos(c)) +
                        Math.sin(lat2Rad) * Math.sin(lat2Rad) * Math.sin(c) * Math.sin(c))
        );
        double midLon = lon1Rad + Math.atan2(
                Math.sin(dLon) * Math.cos(lat2Rad),
                Math.cos(dLat) * Math.cos(lat1Rad) - Math.sin(dLat) * Math.sin(lat2Rad) * Math.cos(dLon)
        );

        // 4. 转换为角度
        double midLonDeg = Math.toDegrees(midLon);
        double midLatDeg = Math.toDegrees(midLat);

        return Arrays.asList(midLonDeg, midLatDeg);
    }


    /**
     * 验证经度范围
     */
    private static boolean isValidLongitude(double lon) {
        return lon >= -180 && lon <= 180;
    }

    /**
     * 验证纬度范围
     */
    private static boolean isValidLatitude(double lat) {
        return lat >= -90 && lat <= 90;
    }


    /**
     * 将Geometry坐标转换成List<Double>，方法总入口下面包含，点，线，面
     * @param geometry
     * @return
     */
    public static List<Double> convertGeometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        if (geometry instanceof Point) {
            return pointToDoubleList((Point) geometry);
        } else if (geometry instanceof LineString) {
            return geometryToDoubleList(geometry);
        } else if (geometry.getNumGeometries() > 1) {
            return multiGeometryToDoubleList(geometry);
        }

        return Collections.emptyList();
    }
    /**
     * 将Geometry坐标转换成List<Double>，面转换
     * @param geometry
     * @return
     */
    public static List<Double> multiGeometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        List<Double> result = new ArrayList<>();
        for (int i = 0; i < geometry.getNumGeometries(); i++) {
            Geometry subGeom = geometry.getGeometryN(i);
            result.addAll(geometryToDoubleList(subGeom));
        }

        return result;
    }
    /**
     * 将Geometry坐标转换成List<Double>，线转换
     * @param geometry
     * @return
     */
    public static List<Double> geometryToDoubleList(Geometry geometry) {
        if (geometry == null || geometry.isEmpty()) {
            return Collections.emptyList();
        }

        Coordinate[] coordinates = geometry.getCoordinates();
        List<Double> result = new ArrayList<>(coordinates.length * 2);

        for (Coordinate coord : coordinates) {
            result.add(coord.x);
            result.add(coord.y);
        }

        return result;
    }

    /**
     * 将Point点坐标转换成List<Double>，点坐标转换转换
     * @param point
     * @return
     */
    public static List<Double> pointToDoubleList(Point point) {
        if (point == null || point.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(point.getX(), point.getY());
    }
}
