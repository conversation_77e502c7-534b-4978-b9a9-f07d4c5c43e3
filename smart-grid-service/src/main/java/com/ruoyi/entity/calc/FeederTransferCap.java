package com.ruoyi.entity.calc;

import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.List;

/**
 * 线路负荷转供
 */
@Data
public class FeederTransferCap {
    public FeederTransferCap() {
    }

    public FeederTransferCap(String sourcePsrId, String sourcePsrName, String transferPsrId, String transferPsrName) {
        this.sourcePsrId = sourcePsrId;
        this.sourcePsrName = sourcePsrName;
        this.transferPsrId = transferPsrId;
        this.transferPsrName = transferPsrName;
    }

    /**
     * 合位开关设备
     */
    String hePsrId, hePsrType, hePsrName;

    /**
     * 分位开关设备
     */
    String fenPsrId, fenPsrType, fenPsrName;

    /**
     * 原线路
     */
    String sourcePsrId, sourcePsrName;

    /**
     * 专供线路ID
     */
    String transferPsrId, transferPsrName;

    /**
     * 原线路负载率
     */
    double sourceLoad;

    /**
     * 专供之后原线路的负载率
     */
    double sourceChangeLoad;

    /**
     * 专供线路负载率
     */
    double transferLoad;

    /**
     * 专供之后专供线路的负载率
     */
    double transferChangeLoad;

    /**
     * 转供的路径
     */
    List<Node> paths;

}
