package com.ruoyi.entity.znap;

import com.ruoyi.graph.Node;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础设备类型
 */
@Data
public class ZnapNd {
    public ZnapNd(Long id, Long ind, Long jnd, String psrid) {
        this.id = id;
        this.ind = ind;
        this.jnd = jnd;
        this.psrid = psrid;
    }

    public ZnapNd(Long id, boolean isNd, Long nd, String psrid) {
        this.id = id;
        this.nd = nd;
        this.isNd = isNd;
        this.psrid = psrid;
    }

    private Long id;

    /**
     * 节点id
     */
    private Long ind;

    /**
     * 节点id
     */
    private Long jnd;

    /**
     * 节点id
     */
    private Long nd;

    private String psrid;

    private boolean isNd = false;

    private Node node;

    public boolean isNd() {
        return isNd;
    }

    public List<String> getIds() {
        List<String> result;
        if (isNd) {
            result = Arrays.asList(String.valueOf(nd));
        } else {
            result = Arrays.asList(String.valueOf(ind), String.valueOf(jnd));
        }
        Set<String> set = new HashSet<>(result.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        return new ArrayList<>(set);
    }

}
