package com.ruoyi.entity.znap;

import com.ruoyi.graph.Node;
import com.ruoyi.util.ListUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class ZnapTopology {
    /**
     * 节点（设备和线路也使用改节点类型）
     */
    private Node startNode;

    /**
     * 构建节点树
     */
    private Map<String, Node> nodeMap;

    /**
     * 我们的id和军哥算法中的id映射
     */
    private Map<String,Long> znapIdMap;

    /**
     * 节点list
     */
    private ArrayList<Node> nodeList;

//    /**
//     * 主干开关
//     */
//    private List<Node> kgMainNodes;

    /**
     * 联络开关
     */
    private List<Node> kgContactNodes;

    /**
     * 所有路径
     */
    private Map<String, ArrayList<Node>> paths;

    /**
     * 关联的联络联络线路开关和名称
     */
    private List<ContactFeederKg> contactFeederKgs;

    /**
     * 获取联络线Id集合
     */
    public List<String> getContactFeederIds() {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(contactFeederKgs)) {
            return result;
        }

        result = contactFeederKgs.stream().map(ContactFeederKg::getFeederPsrId).collect(Collectors.toList());

        // 去重
        return ListUtils.distinctByKey(result, n -> n);
    }

    /**
     * 获取所有的配变
     */
    public List<Node> getAllPb() {
        return nodeList.stream().filter(Node::isPb).collect(Collectors.toList());
    }
}
