package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.locationtech.jts.geom.Coordinate;

import java.util.Date;
import java.util.List;

/**
 * 电力线路资源信息表
 */


@Data
@TableName("device_feeder")
public class DeviceFeeder {

    /**
     * 资源ID，电网设备唯一标识
     */
    @TableId(value = "psr_id" ,type = IdType.ASSIGN_UUID)
    private String psrId;

    /**
     * 装机容量
     */
    private Double feederRateCapacity;

    /**
     * 资产ID
     */
    private String astId;

    /**
     * 线路名称
     */
    private String name;

    /**
     * 运行编号，调度系统唯一标识
     */
    private String runDevName;

    /**
     * 所属地市
     */
    private String city;

    /**
     * 运维单位
     */
    private String maintOrg;

    /**
     * 维护班组
     */
    private String maintGroup;

    /**
     * 设备主人
     */
    private String equipmentOwner;

    /**
     * 调度单位
     */
    private String dispatchOrg;

    /**
     * 营销运维单位
     */
    private String cmsMaintOrg;

    /**
     * 供电半径(km)
     */
    private Double supplyRadius;

    /**
     * 电压等级
     */
    private String voltageLevel;

    /**
     * 运行状态
     */
    private String psrState;

    /**
     * 架设方式
     */
    private String erectionMethod;

    /**
     * 架空接线方式
     */
    private String overheadMethod;

    /**
     * 电缆接线方式
     */
    private String cableMethod;

    /**
     * 线路总长度(km)
     */
    private Double length;

    /**
     * 架空段长度(km)
     */
    private Double overheadLength;

    /**
     * 电缆段长度(km)
     */
    private Double cableLength;

    /**
     * 起点电站
     */
    private String startStation;

    /**
     * 起点电站类型
     */
    private String startStationType;

    /**
     * 出线间隔
     */
    private String startBay;

    /**
     * 起点开关
     */
    private String startSwitch;

    /**
     * 起始开关类型
     */
    private String startSwitchType;

    /**
     * 投运日期
     */
    private Date startTime;

    /**
     * 退运日期
     */
    private Date stopTime;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 最新更新时间(带毫秒)
     */
    private Date lastUpdateTime;

    /**
     * 是否农网
     */
    private String isRural;

    /**
     * 重要程度
     */
    private String importance;

    /**
     * 供电区域
     */
    private String supplyArea;



    /**
     * 线路色标
     */
    private String colorCode;

    /**
     * 营配标识
     */
    private String pubPrivFlag;

    /**
     * 地区特征
     */
    private String regionalism;

    /**
     * 供电网格
     */
    private String gridCode;

    /**
     * 供电网格id
     */
    private String gridId;

    /**
     *调度分区
     */
    private String dispatchArea;

    /**
     * 可靠性编号
     */
    private String reliableDevName;

    /**
     * 运检网络
     */
    private String maintGrid;

    /**
     * 代维线路长度
     */
    private String commissionLength;


    /**
     * 坐标集合
     */
    private String geoList;



}
