package com.ruoyi.entity.device;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 配电站内变压器(0302)
 */
@Data
@TableName("device_station_transformer")
public class DeviceStationTransformer {
    /**
     * 资源ID
     */
    @TableId("psr_id")
    private String psrId;

    /**
     * 资产ID
     */
    @TableField("ast_id")
    private String astId;

    /**
     * 设备名称
     */
    @TableField("name")
    private String name;

    /**
     * 运行编号
     */
    @TableField("run_dev_name")
    private String runDevName;

    /**
     * 全路径名称
     */
    @TableField("full_path_name")
    private String fullPathName;

    /**
     * 所属地市
     */
    @TableField("city")
    private String city;

    /**
     * 运维单位
     */
    @TableField("maint_org")
    private String maintOrg;

    /**
     * 维护班组
     */
    @TableField("maint_group")
    private String maintGroup;

    /**
     * 设备主人
     */
    @TableField("equipment_owner")
    private String equipmentOwner;

    /**
     * 所属站房
     */
    @TableField("station")
    private String station;

    /**
     * 所属间隔
     */
    @TableField("bay")
    private String bay;

    /**
     * 站房类型
     */
    @TableField("station_type")
    private String stationType;

    /**
     * 电压等级
     */
    @TableField("voltage_level")
    private String voltageLevel;

    /**
     * 运行状态
     */
    @TableField("psr_state")
    private String psrState;

    /**
     * 投运日期
     */
    @TableField("start_time")
    private LocalDate startTime;

    /**
     * 退运日期
     */
    @TableField("stop_time")
    private LocalDate stopTime;

    /**
     * 是否农网
     */
    @TableField("is_rural")
    private String isRural;

    /**
     * 重要等级
     */
    @TableField("importance")
    private String importance;

    /**
     * 地区特征
     */
    @TableField("regionalism")
    private String regionalism;

    /**
     * 供电区域
     */
    @TableField("supply_area")
    private String supplyArea;

    /**
     * 使用性质
     */
    @TableField("use_nature")
    private String useNature;

    /**
     * 电源数目
     */
    @TableField("powersource_quant")
    private String powersourceQuant;

    /**
     * 管辖机构
     */
    @TableField("dispatch_jurisdiction")
    private String dispatchJurisdiction;

    /**
     * 操作机构
     */
    @TableField("dispatch_operation")
    private String dispatchOperation;

    /**
     * 许可机构
     */
    @TableField("dispatch_permission")
    private String dispatchPermission;

    /**
     * 监控机构
     */
    @TableField("dispatch_monitor")
    private String dispatchMonitor;

    /**
     * 所属主干/分支线
     */
    @TableField("branch_feeder")
    private String branchFeeder;

    /**
     * 所属馈线
     */
    @TableField("feeder")
    private String feeder;

    /**
     * 创建时间
     */
    @TableField("ctime")
    private LocalDate ctime;

    /**
     * 所属开关段
     */
    @TableField("switch_segment")
    private String switchSegment;

    /**
     * 营配标识
     */
    @TableField("pub_priv_flag")
    private String pubPrivFlag;

    /**
     * 用电客户唯一标识
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 安装地址
     */
    @TableField("installation_address")
    private String installationAddress;

    /**
     * 所属接入点
     */
    @TableField("join_ec")
    private String joinEc;

    /**
     * 最后更新时间
     */
    @TableField("last_update_time")
    private LocalDateTime lastUpdateTime;

    /**
     * 所属可靠性分段
     */
    @TableField("reliable_segment")
    private String reliableSegment;

    /**
     * 营销用户户号
     */
    @TableField("cons_no")
    private String consNo;

    /**
     * 营销运维单位
     */
    @TableField("cms_maint_org")
    private String cmsMaintOrg;

    /**
     * 营配配变运行状态
     */
    @TableField("cms_state")
    private String cmsState;

    /**
     * 是否标准定制
     */
    @TableField("is_standard_customize")
    private String isStandardCustomize;

    /**
     * 城乡属性
     */
    @TableField("urban_rural")
    private String urbanRural;

    /**
     * 行政区域
     */
    @TableField("administ_regions")
    private String administRegions;

    /**
     * 安装容量
     */
    @TableField("installed_capacity")
    private String installedCapacity;

    /**
     * 经度
     */
    @TableField("longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private Double latitude;

    /**
     * 额定容量，
     */
    private String ratedCapacity;
}
