package com.ruoyi.entity.map.bo;

import com.ruoyi.entity.device.DeviceFeeder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@EqualsAndHashCode()
@NoArgsConstructor
@Accessors
public class FeederRangeQueryBo {

    /**
     * 主线路集合
     */
    private List<List<double[]>> coordinateList;

    /**
     * 范围
     */
    private Double range;

    /**
     * 目标所属id
     */
    private String psrId;


    /**
     * 是否返回所有附近
     */
    private Integer num;

    private List<DeviceFeeder> deviceFeederList;
}
