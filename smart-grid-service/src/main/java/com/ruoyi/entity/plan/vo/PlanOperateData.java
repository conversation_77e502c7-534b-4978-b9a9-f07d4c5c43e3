package com.ruoyi.entity.plan.vo;

import com.ruoyi.graph.vo.NodeVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PlanOperateData {
    public PlanOperateData(List<NodeVo> edges, List<NodeVo> devices) {
        this.edges = edges;
        this.devices = devices;
    }

    List<NodeVo> edges;

    List<NodeVo> devices;

}
