package com.ruoyi.entity.gc.dto;

import lombok.Data;

import java.util.Date;

/**
 * 网架变更响应DTO
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
public class GridChangeResponse {

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 方案ID
     */
    private Long schemaId;

    /**
     * 方案名称
     */
    private String schemaName;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本号
     */
    private String versionNum;

    /**
     * 是否当前版本
     */
    private Boolean isCurrent;

    /**
     * 容器数量
     */
    private Integer containerCount;

    /**
     * 设备数量
     */
    private Integer deviceCount;

    /**
     * 电源点数量
     */
    private Integer powerCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 处理消息
     */
    private String message;
}
