package com.ruoyi.entity.gc;

import lombok.Data;

import java.io.Serializable;

/**
 * 网架变更-容器表（变电站、馈线、环网柜、组合开关）
 * @TableName gc_con
 */
@Data
public class GcCon implements Serializable {

    /**
     * id 自增1
     */
    private Long id;
    /**
     * 容器名
     */
    private String name;
    /**
     * 容器别名
     */
    private String aliasName;
    /**
     * 容器rdfid
     */
    private String rdfid;
    /**
     * 容器mrid
     */
    private String mrid;
    /**
     * 容器psrid
     */
    private String psrid;
    /**
     * 容器psrtype
     */
    private String psrtype;
    /**
     * 容器类型标识 1变电站 2馈线 3柜子 4组合开关(可以忽略)
     */

    private Integer type;
    /**
     * 所属版本id
     */
    private Long versionId;
    /**
     * 实际模型id
     */
    private Long objId = -1l;
    /**
     * 实际模型表号 3变电站 201馈线 202柜子 203组合开关(可以忽略)
     */
    private Long objTableno = -1l;
    /**
     * 所属容器id
     */
    private Long containerId;


}
