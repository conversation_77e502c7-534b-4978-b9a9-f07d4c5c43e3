package com.ruoyi.entity.gc;

import lombok.Data;

import java.io.Serializable;

/**
 * 网架变更-设备表
 * @TableName gc_dev
 */
@Data
public class GcDev implements Serializable {

    /**
     * id 自增1
     */


    private Long id;
    /**
     * 设备名
     */


    private String name;
    /**
     * 设备别名
     */


    private String aliasName;
    /**
     * 设备rdfid
     */


    private String rdfid;
    /**
     * 设备mrid
     */


    private String mrid;
    /**
     * 设备psrid
     */


    private String psrid;
    /**
     * 设备psrtype
     */


    private String psrtype;
    /**
     * 设备类型标识 0主网开关 1配网开关 2刀闸 3熔断器 4母线 5地刀 6电缆头 7负荷 8杆塔 9馈线段 10配变 11绕组
     */

    private Integer type;
    /**
     * 版本id
     */

    private Long versionId;
    /**
     * 实际模型id
     */

    private Long objId;
    /**
     * 实际模型表号 100主网开关 204配网开关 205刀闸 214熔断器 211母线 207地刀 215电缆头 209负荷 212杆塔 206馈线段 208配变 210绕组
     */

    private Long objTableno;
    /**
     * 所属容器id (gc_con表id)
     */

    private Long containerId;
    /**
     * 单节点设备ind作为nd
     */

    private Long ind;
    /**
     * 单节点设备jnd不处理
     */

    private Long jnd;
    /**
     * 电压等级
     */

    private Long bvId;


}
