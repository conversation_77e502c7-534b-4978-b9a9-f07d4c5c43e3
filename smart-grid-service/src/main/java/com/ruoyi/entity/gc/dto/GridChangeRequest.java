package com.ruoyi.entity.gc.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 网架变更请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
public class GridChangeRequest {

    /**
     * 问题ID
     */
    @NotNull(message = "问题ID不能为空")
    private Long problemId;

    /**
     * 馈线ID
     */
    @NotBlank(message = "馈线ID不能为空")
    private String feederId;

    /**
     * 方案名称
     */
    @NotBlank(message = "方案名称不能为空")
    private String schemaName;

    /**
     * 方案描述
     */
    private String description;

    /**
     * 操作数据JSON字符串
     */
    private String operateDataJson;

    /**
     * 是否设为当前版本
     */
    private Boolean setAsCurrent = true;
}
