package com.ruoyi.entity.gc;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 网架变更-版本表
 * @TableName gc_version
 */
@Data
public class GcVersion implements Serializable {

    /**
     * 版本id 自增1
     */

    private Long id;
    /**
     * 版本名
     */
    private String name;
    /**
     * 版本号
     */

    private String versionNum;
    /**
     * 版本描述
     */

    private String description;
    /**
     * 是否当前版本 0否 1是
     */

    private Integer isCurrent;
    /**
     * 方案id
     */

    private Long schemaId;
    /**
     * 创建人
     */


    private String createUser;
    /**
     * 创建时间
     */

    private Date createDt;
    /**
     * 更新时间
     */
    private Date updateDt;
    /**
     * 问题id
     */

    private Long problemId;
}
