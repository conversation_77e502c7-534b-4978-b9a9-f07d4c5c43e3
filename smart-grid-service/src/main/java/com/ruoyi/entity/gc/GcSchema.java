package com.ruoyi.entity.gc;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 网架变更-方案表
 * @TableName gc_schema
 */
@Data
public class GcSchema implements Serializable {

    /**
     * 方案id 自增1
     */


    private Long id;
    /**
     * 方案名
     */


    private String name;
    /**
     * 方案描述
     */


    private String description;
    /**
     * 状态
     */

    private Integer status;
    /**
     * 创建人
     */


    private String createUser;
    /**
     * 创建时间
     */

    private Date createDt;
    /**
     * 更新时间
     */

    private Date updateDt;
    /**
     * 问题id
     */


    private Long problemId;


}
