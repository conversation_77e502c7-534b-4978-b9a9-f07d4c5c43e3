package com.ruoyi.entity.gc;

import lombok.Data;

import java.io.Serializable;

/**
 * 网架变更-馈线段参数表
 * @TableName gc_dev_para_segment
 */
@Data
public class GcDevParaSegment implements Serializable {

    /**
     * 馈线段id
     */


    private Long id;
    /**
     * 版本id
     */

    private Long versionId;
    /**
     * 电缆长度
     */

    private Double lenM;
    /**
     * 电阻
     */

    private Double rOPerKm;
    /**
     * 电抗
     */

    private Double xOPerKm;
    /**
     * 电容
     */

    private Double cNfPerKm;
    /**
     * 正序电阻
     */

    private Double r1OPerKm;
    /**
     * 正序电抗
     */

    private Double x1OPerKm;
    /**
     * 正序电容
     */

    private Double c1NfPerKm;
    /**
     * 负序电阻
     */

    private Double r2OPerKm;
    /**
     * 负序电抗
     */

    private Double x2OPerKm;
    /**
     * 负序电容
     */

    private Double c2NfPerKm;
    /**
     * 零序电阻
     */

    private Double r0OPerKm;
    /**
     * 零序电抗
     */

    private Double x0OPerKm;
    /**
     * 零序电容
     */

    private Double c0NfPerKm;


}
