package com.ruoyi.enetity.gc.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
* 网架变更-熔断器参数表
* @TableName gc_dev_para_fuse
*/
public class GcDevParaFuse implements Serializable {

    /**
    * 熔断器id
    */
    @NotNull(message="[熔断器id]不能为空")
    @ApiModelProperty("熔断器id")
    private Long id;
    /**
    * 版本id
    */
    @ApiModelProperty("版本id")
    private Long versionId;
    /**
    * 分合位 0分 1合
    */
    @ApiModelProperty("分合位 0分 1合")
    private Integer posValue;

    /**
    * 熔断器id
    */
    private void setId(Long id){
    this.id = id;
    }

    /**
    * 版本id
    */
    private void setVersionId(Long versionId){
    this.versionId = versionId;
    }

    /**
    * 分合位 0分 1合
    */
    private void setPosValue(Integer posValue){
    this.posValue = posValue;
    }


    /**
    * 熔断器id
    */
    private Long getId(){
    return this.id;
    }

    /**
    * 版本id
    */
    private Long getVersionId(){
    return this.versionId;
    }

    /**
    * 分合位 0分 1合
    */
    private Integer getPosValue(){
    return this.posValue;
    }

}
