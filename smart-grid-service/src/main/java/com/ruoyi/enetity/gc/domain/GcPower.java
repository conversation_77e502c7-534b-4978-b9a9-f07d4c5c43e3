package com.ruoyi.enetity.gc.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
* 网架变更-电源点表
* @TableName gc_power
*/
public class GcPower implements Serializable {

    /**
    * 馈线id
    */
    @NotNull(message="[馈线id]不能为空")
    @ApiModelProperty("馈线id")
    private Long id;
    /**
    * 版本id
    */
    @ApiModelProperty("版本id")
    private Long versionId;
    /**
    * 厂站id
    */
    @ApiModelProperty("厂站id")
    private Long stationId;
    /**
    * 出线开关id
    */
    @ApiModelProperty("出线开关id")
    private Long cbId;
    /**
    * 首节点
    */
    @ApiModelProperty("首节点")
    private Long headNd;

    /**
    * 馈线id
    */
    private void setId(Long id){
    this.id = id;
    }

    /**
    * 版本id
    */
    private void setVersionId(Long versionId){
    this.versionId = versionId;
    }

    /**
    * 厂站id
    */
    private void setStationId(Long stationId){
    this.stationId = stationId;
    }

    /**
    * 出线开关id
    */
    private void setCbId(Long cbId){
    this.cbId = cbId;
    }

    /**
    * 首节点
    */
    private void setHeadNd(Long headNd){
    this.headNd = headNd;
    }


    /**
    * 馈线id
    */
    private Long getId(){
    return this.id;
    }

    /**
    * 版本id
    */
    private Long getVersionId(){
    return this.versionId;
    }

    /**
    * 厂站id
    */
    private Long getStationId(){
    return this.stationId;
    }

    /**
    * 出线开关id
    */
    private Long getCbId(){
    return this.cbId;
    }

    /**
    * 首节点
    */
    private Long getHeadNd(){
    return this.headNd;
    }

}
