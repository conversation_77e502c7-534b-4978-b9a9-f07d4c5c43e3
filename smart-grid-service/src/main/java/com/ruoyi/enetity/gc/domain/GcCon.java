package com.ruoyi.enetity.gc.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
* 网架变更-容器表（变电站、馈线、环网柜、组合开关）
* @TableName gc_con
*/
public class GcCon implements Serializable {

    /**
    * id 自增1
    */
    @NotNull(message="[id 自增1]不能为空")
    @ApiModelProperty("id 自增1")
    private Long id;
    /**
    * 容器名
    */
    @Size(max= 512,message="编码长度不能超过512")
    @ApiModelProperty("容器名")
    @Length(max= 512,message="编码长度不能超过512")
    private String name;
    /**
    * 容器别名
    */
    @Size(max= 512,message="编码长度不能超过512")
    @ApiModelProperty("容器别名")
    @Length(max= 512,message="编码长度不能超过512")
    private String aliasName;
    /**
    * 容器rdfid
    */
    @Size(max= 128,message="编码长度不能超过128")
    @ApiModelProperty("容器rdfid")
    @Length(max= 128,message="编码长度不能超过128")
    private String rdfid;
    /**
    * 容器mrid
    */
    @Size(max= 128,message="编码长度不能超过128")
    @ApiModelProperty("容器mrid")
    @Length(max= 128,message="编码长度不能超过128")
    private String mrid;
    /**
    * 容器psrid
    */
    @Size(max= 128,message="编码长度不能超过128")
    @ApiModelProperty("容器psrid")
    @Length(max= 128,message="编码长度不能超过128")
    private String psrid;
    /**
    * 容器psrtype
    */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("容器psrtype")
    @Length(max= 64,message="编码长度不能超过64")
    private String psrtype;
    /**
    * 容器类型标识 1变电站 2馈线 3柜子 4组合开关(可以忽略)
    */
    @ApiModelProperty("容器类型标识 1变电站 2馈线 3柜子 4组合开关(可以忽略)")
    private Integer type;
    /**
    * 所属版本id
    */
    @ApiModelProperty("所属版本id")
    private Long versionId;
    /**
    * 实际模型id
    */
    @ApiModelProperty("实际模型id")
    private Long objId;
    /**
    * 实际模型表号 3变电站 201馈线 202柜子 203组合开关(可以忽略)
    */
    @ApiModelProperty("实际模型表号 3变电站 201馈线 202柜子 203组合开关(可以忽略)")
    private Integer objTableno;
    /**
    * 所属容器id
    */
    @ApiModelProperty("所属容器id")
    private Long containerId;

    /**
    * id 自增1
    */
    private void setId(Long id){
    this.id = id;
    }

    /**
    * 容器名
    */
    private void setName(String name){
    this.name = name;
    }

    /**
    * 容器别名
    */
    private void setAliasName(String aliasName){
    this.aliasName = aliasName;
    }

    /**
    * 容器rdfid
    */
    private void setRdfid(String rdfid){
    this.rdfid = rdfid;
    }

    /**
    * 容器mrid
    */
    private void setMrid(String mrid){
    this.mrid = mrid;
    }

    /**
    * 容器psrid
    */
    private void setPsrid(String psrid){
    this.psrid = psrid;
    }

    /**
    * 容器psrtype
    */
    private void setPsrtype(String psrtype){
    this.psrtype = psrtype;
    }

    /**
    * 容器类型标识 1变电站 2馈线 3柜子 4组合开关(可以忽略)
    */
    private void setType(Integer type){
    this.type = type;
    }

    /**
    * 所属版本id
    */
    private void setVersionId(Long versionId){
    this.versionId = versionId;
    }

    /**
    * 实际模型id
    */
    private void setObjId(Long objId){
    this.objId = objId;
    }

    /**
    * 实际模型表号 3变电站 201馈线 202柜子 203组合开关(可以忽略)
    */
    private void setObjTableno(Integer objTableno){
    this.objTableno = objTableno;
    }

    /**
    * 所属容器id
    */
    private void setContainerId(Long containerId){
    this.containerId = containerId;
    }


    /**
    * id 自增1
    */
    private Long getId(){
    return this.id;
    }

    /**
    * 容器名
    */
    private String getName(){
    return this.name;
    }

    /**
    * 容器别名
    */
    private String getAliasName(){
    return this.aliasName;
    }

    /**
    * 容器rdfid
    */
    private String getRdfid(){
    return this.rdfid;
    }

    /**
    * 容器mrid
    */
    private String getMrid(){
    return this.mrid;
    }

    /**
    * 容器psrid
    */
    private String getPsrid(){
    return this.psrid;
    }

    /**
    * 容器psrtype
    */
    private String getPsrtype(){
    return this.psrtype;
    }

    /**
    * 容器类型标识 1变电站 2馈线 3柜子 4组合开关(可以忽略)
    */
    private Integer getType(){
    return this.type;
    }

    /**
    * 所属版本id
    */
    private Long getVersionId(){
    return this.versionId;
    }

    /**
    * 实际模型id
    */
    private Long getObjId(){
    return this.objId;
    }

    /**
    * 实际模型表号 3变电站 201馈线 202柜子 203组合开关(可以忽略)
    */
    private Integer getObjTableno(){
    return this.objTableno;
    }

    /**
    * 所属容器id
    */
    private Long getContainerId(){
    return this.containerId;
    }

}
