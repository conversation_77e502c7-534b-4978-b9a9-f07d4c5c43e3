package com.ruoyi.enetity.gc.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
* 网架变更-开关参数表
* @TableName gc_dev_para_cb
*/
public class GcDevParaCb implements Serializable {

    /**
    * 开关id
    */
    @NotNull(message="[开关id]不能为空")
    @ApiModelProperty("开关id")
    private Long id;
    /**
    * 版本id
    */
    @ApiModelProperty("版本id")
    private Long versionId;
    /**
    * 分合位 0分 1合
    */
    @ApiModelProperty("分合位 0分 1合")
    private Integer posValue;

    /**
    * 开关id
    */
    private void setId(Long id){
    this.id = id;
    }

    /**
    * 版本id
    */
    private void setVersionId(Long versionId){
    this.versionId = versionId;
    }

    /**
    * 分合位 0分 1合
    */
    private void setPosValue(Integer posValue){
    this.posValue = posValue;
    }


    /**
    * 开关id
    */
    private Long getId(){
    return this.id;
    }

    /**
    * 版本id
    */
    private Long getVersionId(){
    return this.versionId;
    }

    /**
    * 分合位 0分 1合
    */
    private Integer getPosValue(){
    return this.posValue;
    }

}
