package com.ruoyi.enetity.gc.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
* 网架变更-版本表
* @TableName gc_version
*/
public class GcVersion implements Serializable {

    /**
    * 版本id 自增1
    */
    @NotNull(message="[版本id 自增1]不能为空")
    @ApiModelProperty("版本id 自增1")
    private Long id;
    /**
    * 版本名
    */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("版本名")
    @Length(max= 64,message="编码长度不能超过64")
    private String name;
    /**
    * 版本号
    */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("版本号")
    @Length(max= 64,message="编码长度不能超过64")
    private String versionNum;
    /**
    * 版本描述
    */
    @Size(max= 256,message="编码长度不能超过256")
    @ApiModelProperty("版本描述")
    @Length(max= 256,message="编码长度不能超过256")
    private String description;
    /**
    * 是否当前版本 0否 1是
    */
    @ApiModelProperty("是否当前版本 0否 1是")
    private Integer isCurrent;
    /**
    * 方案id
    */
    @ApiModelProperty("方案id")
    private Long schemaId;
    /**
    * 创建人
    */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("创建人")
    @Length(max= 64,message="编码长度不能超过64")
    private String createUser;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createDt;
    /**
    * 更新时间
    */
    @ApiModelProperty("更新时间")
    private Date updateDt;
    /**
    * 问题id
    */
    @NotNull(message="[问题id]不能为空")
    @ApiModelProperty("问题id")
    private Long problemId;

    /**
    * 版本id 自增1
    */
    private void setId(Long id){
    this.id = id;
    }

    /**
    * 版本名
    */
    private void setName(String name){
    this.name = name;
    }

    /**
    * 版本号
    */
    private void setVersionNum(String versionNum){
    this.versionNum = versionNum;
    }

    /**
    * 版本描述
    */
    private void setDescription(String description){
    this.description = description;
    }

    /**
    * 是否当前版本 0否 1是
    */
    private void setIsCurrent(Integer isCurrent){
    this.isCurrent = isCurrent;
    }

    /**
    * 方案id
    */
    private void setSchemaId(Long schemaId){
    this.schemaId = schemaId;
    }

    /**
    * 创建人
    */
    private void setCreateUser(String createUser){
    this.createUser = createUser;
    }

    /**
    * 创建时间
    */
    private void setCreateDt(Date createDt){
    this.createDt = createDt;
    }

    /**
    * 更新时间
    */
    private void setUpdateDt(Date updateDt){
    this.updateDt = updateDt;
    }

    /**
    * 问题id
    */
    private void setProblemId(Long problemId){
    this.problemId = problemId;
    }


    /**
    * 版本id 自增1
    */
    private Long getId(){
    return this.id;
    }

    /**
    * 版本名
    */
    private String getName(){
    return this.name;
    }

    /**
    * 版本号
    */
    private String getVersionNum(){
    return this.versionNum;
    }

    /**
    * 版本描述
    */
    private String getDescription(){
    return this.description;
    }

    /**
    * 是否当前版本 0否 1是
    */
    private Integer getIsCurrent(){
    return this.isCurrent;
    }

    /**
    * 方案id
    */
    private Long getSchemaId(){
    return this.schemaId;
    }

    /**
    * 创建人
    */
    private String getCreateUser(){
    return this.createUser;
    }

    /**
    * 创建时间
    */
    private Date getCreateDt(){
    return this.createDt;
    }

    /**
    * 更新时间
    */
    private Date getUpdateDt(){
    return this.updateDt;
    }

    /**
    * 问题id
    */
    private Long getProblemId(){
    return this.problemId;
    }

}
