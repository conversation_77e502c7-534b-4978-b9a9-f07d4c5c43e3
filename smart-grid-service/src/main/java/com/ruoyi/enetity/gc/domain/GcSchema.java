package com.ruoyi.enetity.gc.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
* 网架变更-方案表
* @TableName gc_schema
*/
public class GcSchema implements Serializable {

    /**
    * 方案id 自增1
    */
    @NotNull(message="[方案id 自增1]不能为空")
    @ApiModelProperty("方案id 自增1")
    private Long id;
    /**
    * 方案名
    */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("方案名")
    @Length(max= 64,message="编码长度不能超过64")
    private String name;
    /**
    * 方案描述
    */
    @Size(max= 256,message="编码长度不能超过256")
    @ApiModelProperty("方案描述")
    @Length(max= 256,message="编码长度不能超过256")
    private String description;
    /**
    * 状态
    */
    @ApiModelProperty("状态")
    private Integer status;
    /**
    * 创建人
    */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("创建人")
    @Length(max= 64,message="编码长度不能超过64")
    private String createUser;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createDt;
    /**
    * 更新时间
    */
    @ApiModelProperty("更新时间")
    private Date updateDt;
    /**
    * 问题id
    */
    @NotNull(message="[问题id]不能为空")
    @ApiModelProperty("问题id")
    private Long problemId;

    /**
    * 方案id 自增1
    */
    private void setId(Long id){
    this.id = id;
    }

    /**
    * 方案名
    */
    private void setName(String name){
    this.name = name;
    }

    /**
    * 方案描述
    */
    private void setDescription(String description){
    this.description = description;
    }

    /**
    * 状态
    */
    private void setStatus(Integer status){
    this.status = status;
    }

    /**
    * 创建人
    */
    private void setCreateUser(String createUser){
    this.createUser = createUser;
    }

    /**
    * 创建时间
    */
    private void setCreateDt(Date createDt){
    this.createDt = createDt;
    }

    /**
    * 更新时间
    */
    private void setUpdateDt(Date updateDt){
    this.updateDt = updateDt;
    }

    /**
    * 问题id
    */
    private void setProblemId(Long problemId){
    this.problemId = problemId;
    }


    /**
    * 方案id 自增1
    */
    private Long getId(){
    return this.id;
    }

    /**
    * 方案名
    */
    private String getName(){
    return this.name;
    }

    /**
    * 方案描述
    */
    private String getDescription(){
    return this.description;
    }

    /**
    * 状态
    */
    private Integer getStatus(){
    return this.status;
    }

    /**
    * 创建人
    */
    private String getCreateUser(){
    return this.createUser;
    }

    /**
    * 创建时间
    */
    private Date getCreateDt(){
    return this.createDt;
    }

    /**
    * 更新时间
    */
    private Date getUpdateDt(){
    return this.updateDt;
    }

    /**
    * 问题id
    */
    private Long getProblemId(){
    return this.problemId;
    }

}
