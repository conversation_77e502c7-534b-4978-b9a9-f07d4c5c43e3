package com.ruoyi.enetity.gc.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
* 网架变更-馈线段参数表
* @TableName gc_dev_para_segment
*/
public class GcDevParaSegment implements Serializable {

    /**
    * 馈线段id
    */
    @NotNull(message="[馈线段id]不能为空")
    @ApiModelProperty("馈线段id")
    private Long id;
    /**
    * 版本id
    */
    @ApiModelProperty("版本id")
    private Long versionId;
    /**
    * 电缆长度
    */
    @ApiModelProperty("电缆长度")
    private Double lenM;
    /**
    * 电阻
    */
    @ApiModelProperty("电阻")
    private Double rOPerKm;
    /**
    * 电抗
    */
    @ApiModelProperty("电抗")
    private Double xOPerKm;
    /**
    * 电容
    */
    @ApiModelProperty("电容")
    private Double cNfPerKm;
    /**
    * 正序电阻
    */
    @ApiModelProperty("正序电阻")
    private Double r1OPerKm;
    /**
    * 正序电抗
    */
    @ApiModelProperty("正序电抗")
    private Double x1OPerKm;
    /**
    * 正序电容
    */
    @ApiModelProperty("正序电容")
    private Double c1NfPerKm;
    /**
    * 负序电阻
    */
    @ApiModelProperty("负序电阻")
    private Double r2OPerKm;
    /**
    * 负序电抗
    */
    @ApiModelProperty("负序电抗")
    private Double x2OPerKm;
    /**
    * 负序电容
    */
    @ApiModelProperty("负序电容")
    private Double c2NfPerKm;
    /**
    * 零序电阻
    */
    @ApiModelProperty("零序电阻")
    private Double r0OPerKm;
    /**
    * 零序电抗
    */
    @ApiModelProperty("零序电抗")
    private Double x0OPerKm;
    /**
    * 零序电容
    */
    @ApiModelProperty("零序电容")
    private Double c0NfPerKm;

    /**
    * 馈线段id
    */
    private void setId(Long id){
    this.id = id;
    }

    /**
    * 版本id
    */
    private void setVersionId(Long versionId){
    this.versionId = versionId;
    }

    /**
    * 电缆长度
    */
    private void setLenM(Double lenM){
    this.lenM = lenM;
    }

    /**
    * 电阻
    */
    private void setROPerKm(Double rOPerKm){
    this.rOPerKm = rOPerKm;
    }

    /**
    * 电抗
    */
    private void setXOPerKm(Double xOPerKm){
    this.xOPerKm = xOPerKm;
    }

    /**
    * 电容
    */
    private void setCNfPerKm(Double cNfPerKm){
    this.cNfPerKm = cNfPerKm;
    }

    /**
    * 正序电阻
    */
    private void setR1OPerKm(Double r1OPerKm){
    this.r1OPerKm = r1OPerKm;
    }

    /**
    * 正序电抗
    */
    private void setX1OPerKm(Double x1OPerKm){
    this.x1OPerKm = x1OPerKm;
    }

    /**
    * 正序电容
    */
    private void setC1NfPerKm(Double c1NfPerKm){
    this.c1NfPerKm = c1NfPerKm;
    }

    /**
    * 负序电阻
    */
    private void setR2OPerKm(Double r2OPerKm){
    this.r2OPerKm = r2OPerKm;
    }

    /**
    * 负序电抗
    */
    private void setX2OPerKm(Double x2OPerKm){
    this.x2OPerKm = x2OPerKm;
    }

    /**
    * 负序电容
    */
    private void setC2NfPerKm(Double c2NfPerKm){
    this.c2NfPerKm = c2NfPerKm;
    }

    /**
    * 零序电阻
    */
    private void setR0OPerKm(Double r0OPerKm){
    this.r0OPerKm = r0OPerKm;
    }

    /**
    * 零序电抗
    */
    private void setX0OPerKm(Double x0OPerKm){
    this.x0OPerKm = x0OPerKm;
    }

    /**
    * 零序电容
    */
    private void setC0NfPerKm(Double c0NfPerKm){
    this.c0NfPerKm = c0NfPerKm;
    }


    /**
    * 馈线段id
    */
    private Long getId(){
    return this.id;
    }

    /**
    * 版本id
    */
    private Long getVersionId(){
    return this.versionId;
    }

    /**
    * 电缆长度
    */
    private Double getLenM(){
    return this.lenM;
    }

    /**
    * 电阻
    */
    private Double getROPerKm(){
    return this.rOPerKm;
    }

    /**
    * 电抗
    */
    private Double getXOPerKm(){
    return this.xOPerKm;
    }

    /**
    * 电容
    */
    private Double getCNfPerKm(){
    return this.cNfPerKm;
    }

    /**
    * 正序电阻
    */
    private Double getR1OPerKm(){
    return this.r1OPerKm;
    }

    /**
    * 正序电抗
    */
    private Double getX1OPerKm(){
    return this.x1OPerKm;
    }

    /**
    * 正序电容
    */
    private Double getC1NfPerKm(){
    return this.c1NfPerKm;
    }

    /**
    * 负序电阻
    */
    private Double getR2OPerKm(){
    return this.r2OPerKm;
    }

    /**
    * 负序电抗
    */
    private Double getX2OPerKm(){
    return this.x2OPerKm;
    }

    /**
    * 负序电容
    */
    private Double getC2NfPerKm(){
    return this.c2NfPerKm;
    }

    /**
    * 零序电阻
    */
    private Double getR0OPerKm(){
    return this.r0OPerKm;
    }

    /**
    * 零序电抗
    */
    private Double getX0OPerKm(){
    return this.x0OPerKm;
    }

    /**
    * 零序电容
    */
    private Double getC0NfPerKm(){
    return this.c0NfPerKm;
    }

}
