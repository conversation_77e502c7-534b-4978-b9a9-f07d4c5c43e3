package com.ruoyi.enetity.gc.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

/**
* 网架变更-设备表
* @TableName gc_dev
*/
public class GcDev implements Serializable {

    /**
    * id 自增1
    */
    @NotNull(message="[id 自增1]不能为空")
    @ApiModelProperty("id 自增1")
    private Long id;
    /**
    * 设备名
    */
    @Size(max= 512,message="编码长度不能超过512")
    @ApiModelProperty("设备名")
    @Length(max= 512,message="编码长度不能超过512")
    private String name;
    /**
    * 设备别名
    */
    @Size(max= 512,message="编码长度不能超过512")
    @ApiModelProperty("设备别名")
    @Length(max= 512,message="编码长度不能超过512")
    private String aliasName;
    /**
    * 设备rdfid
    */
    @Size(max= 128,message="编码长度不能超过128")
    @ApiModelProperty("设备rdfid")
    @Length(max= 128,message="编码长度不能超过128")
    private String rdfid;
    /**
    * 设备mrid
    */
    @Size(max= 128,message="编码长度不能超过128")
    @ApiModelProperty("设备mrid")
    @Length(max= 128,message="编码长度不能超过128")
    private String mrid;
    /**
    * 设备psrid
    */
    @Size(max= 128,message="编码长度不能超过128")
    @ApiModelProperty("设备psrid")
    @Length(max= 128,message="编码长度不能超过128")
    private String psrid;
    /**
    * 设备psrtype
    */
    @Size(max= 64,message="编码长度不能超过64")
    @ApiModelProperty("设备psrtype")
    @Length(max= 64,message="编码长度不能超过64")
    private String psrtype;
    /**
    * 设备类型标识 0主网开关 1配网开关 2刀闸 3熔断器 4母线 5地刀 6电缆头 7负荷 8杆塔 9馈线段 10配变 11绕组
    */
    @ApiModelProperty("设备类型标识 0主网开关 1配网开关 2刀闸 3熔断器 4母线 5地刀 6电缆头 7负荷 8杆塔 9馈线段 10配变 11绕组")
    private Integer type;
    /**
    * 版本id
    */
    @ApiModelProperty("版本id")
    private Long versionId;
    /**
    * 实际模型id
    */
    @ApiModelProperty("实际模型id")
    private Long objId;
    /**
    * 实际模型表号 100主网开关 204配网开关 205刀闸 214熔断器 211母线 207地刀 215电缆头 209负荷 212杆塔 206馈线段 208配变 210绕组
    */
    @ApiModelProperty("实际模型表号 100主网开关 204配网开关 205刀闸 214熔断器 211母线 207地刀 215电缆头 209负荷 212杆塔 206馈线段 208配变 210绕组")
    private Integer objTableno;
    /**
    * 所属容器id (gc_con表id)
    */
    @ApiModelProperty("所属容器id (gc_con表id)")
    private Long containerId;
    /**
    * 单节点设备ind作为nd
    */
    @ApiModelProperty("单节点设备ind作为nd")
    private Long ind;
    /**
    * 单节点设备jnd不处理
    */
    @ApiModelProperty("单节点设备jnd不处理")
    private Long jnd;
    /**
    * 电压等级
    */
    @ApiModelProperty("电压等级")
    private Long bvId;

    /**
    * id 自增1
    */
    private void setId(Long id){
    this.id = id;
    }

    /**
    * 设备名
    */
    private void setName(String name){
    this.name = name;
    }

    /**
    * 设备别名
    */
    private void setAliasName(String aliasName){
    this.aliasName = aliasName;
    }

    /**
    * 设备rdfid
    */
    private void setRdfid(String rdfid){
    this.rdfid = rdfid;
    }

    /**
    * 设备mrid
    */
    private void setMrid(String mrid){
    this.mrid = mrid;
    }

    /**
    * 设备psrid
    */
    private void setPsrid(String psrid){
    this.psrid = psrid;
    }

    /**
    * 设备psrtype
    */
    private void setPsrtype(String psrtype){
    this.psrtype = psrtype;
    }

    /**
    * 设备类型标识 0主网开关 1配网开关 2刀闸 3熔断器 4母线 5地刀 6电缆头 7负荷 8杆塔 9馈线段 10配变 11绕组
    */
    private void setType(Integer type){
    this.type = type;
    }

    /**
    * 版本id
    */
    private void setVersionId(Long versionId){
    this.versionId = versionId;
    }

    /**
    * 实际模型id
    */
    private void setObjId(Long objId){
    this.objId = objId;
    }

    /**
    * 实际模型表号 100主网开关 204配网开关 205刀闸 214熔断器 211母线 207地刀 215电缆头 209负荷 212杆塔 206馈线段 208配变 210绕组
    */
    private void setObjTableno(Integer objTableno){
    this.objTableno = objTableno;
    }

    /**
    * 所属容器id (gc_con表id)
    */
    private void setContainerId(Long containerId){
    this.containerId = containerId;
    }

    /**
    * 单节点设备ind作为nd
    */
    private void setInd(Long ind){
    this.ind = ind;
    }

    /**
    * 单节点设备jnd不处理
    */
    private void setJnd(Long jnd){
    this.jnd = jnd;
    }

    /**
    * 电压等级
    */
    private void setBvId(Long bvId){
    this.bvId = bvId;
    }


    /**
    * id 自增1
    */
    private Long getId(){
    return this.id;
    }

    /**
    * 设备名
    */
    private String getName(){
    return this.name;
    }

    /**
    * 设备别名
    */
    private String getAliasName(){
    return this.aliasName;
    }

    /**
    * 设备rdfid
    */
    private String getRdfid(){
    return this.rdfid;
    }

    /**
    * 设备mrid
    */
    private String getMrid(){
    return this.mrid;
    }

    /**
    * 设备psrid
    */
    private String getPsrid(){
    return this.psrid;
    }

    /**
    * 设备psrtype
    */
    private String getPsrtype(){
    return this.psrtype;
    }

    /**
    * 设备类型标识 0主网开关 1配网开关 2刀闸 3熔断器 4母线 5地刀 6电缆头 7负荷 8杆塔 9馈线段 10配变 11绕组
    */
    private Integer getType(){
    return this.type;
    }

    /**
    * 版本id
    */
    private Long getVersionId(){
    return this.versionId;
    }

    /**
    * 实际模型id
    */
    private Long getObjId(){
    return this.objId;
    }

    /**
    * 实际模型表号 100主网开关 204配网开关 205刀闸 214熔断器 211母线 207地刀 215电缆头 209负荷 212杆塔 206馈线段 208配变 210绕组
    */
    private Integer getObjTableno(){
    return this.objTableno;
    }

    /**
    * 所属容器id (gc_con表id)
    */
    private Long getContainerId(){
    return this.containerId;
    }

    /**
    * 单节点设备ind作为nd
    */
    private Long getInd(){
    return this.ind;
    }

    /**
    * 单节点设备jnd不处理
    */
    private Long getJnd(){
    return this.jnd;
    }

    /**
    * 电压等级
    */
    private Long getBvId(){
    return this.bvId;
    }

}
