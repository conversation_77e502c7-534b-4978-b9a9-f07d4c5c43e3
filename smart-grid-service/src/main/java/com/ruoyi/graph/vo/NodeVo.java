package com.ruoyi.graph.vo;

import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.graph.Ports;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 节点（返回）
 */
@Data
public class NodeVo {
    public NodeVo() {

    }
    public NodeVo(String id, String psrId, String psrType) {
        this.id = id;
        this.psrId = psrId;
        this.psrType = psrType;
    }

    public NodeVo(String id, String psrId, String psrType, String name, boolean isEdge, String type, String shapeKey) {
        this.id = id;
        this.psrId = psrId;
        this.psrType = psrType;
        this.name = name;
        this.isEdge = isEdge;
        this.type = type;
        this.shapeKey = shapeKey;
    }

    /**
     * 唯一ID
     */
    private String id;

    /**
     * 唯一ID 电网设备ID
     */
    private String psrId;

    /**
     * 唯一ID 电网设备ID
     */
    private String psrType;

    /**
     * 设备名称
     */
    private String name;



    /**
     * 当前的节点类型 详情参考 TYPE_PSR 、 TYPE_SELF
     */
    private String type;

    /**
     * 边的线型
     */
    private String lineType;

    /**
     * 渲染key
     */
    private String shapeKey;


    /**
     * 是否为边
     */
    private boolean isEdge = false;

    /**
     * 子节点和连接的边集合
     */
    private List<String> childrenIds, edgeIds = new ArrayList<>();

    /**
     * 父节点
     */
    private String parentId;

    /**
     * 坐标对象
     */
    private Object coordinates;

    /**
     * 自定义属性值
     */
    private HashMap<String,Object> properties;


    /**
     * 源节点和目标节点
     */
    private String sourceId, targetId;

    private Ports ports;

    /**
     * 起始连接port
     */
    private String sourcePort;

    /**
     * 结束连接port
     */
    private String targetPort;

}

