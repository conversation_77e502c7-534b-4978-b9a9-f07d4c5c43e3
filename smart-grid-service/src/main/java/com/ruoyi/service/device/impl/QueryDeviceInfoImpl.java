package com.ruoyi.service.device.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.bo.DeviceAccessPointBo;
import com.ruoyi.entity.device.bo.PsrIdAndPsrType;
import com.ruoyi.entity.device.vo.*;
import com.ruoyi.graph.Node;
import com.ruoyi.mapper.device.*;
import com.ruoyi.util.coordinates.CoordinateConverter;
import net.objecthunter.exp4j.Expression;
import net.objecthunter.exp4j.ExpressionBuilder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.ruoyi.constant.DeviceConstants.JUNCTION_TYPES;
import static com.ruoyi.constant.DeviceConstants.POLE_TYPES;
import static com.ruoyi.entity.cost.DeviceType.*;

@Service
public class QueryDeviceInfoImpl {

    //  配电柱上负荷开关（0112）、配电柱上断  路器（0111）
    @Autowired
    DevicePoleBreakMapper devicePoleBreakMapper;

    // device_station_isolate_kg 变电站内隔离开关(0306)
    @Autowired
    DeviceStationIsolateKgMapper deviceStationIsolateKgMapper;

    // device_station_load_kg 变电站内负荷开关(0307)
    @Autowired
    DeviceStationLoadKgMapper deviceStationLoadKgMapper;

    // device_station_breaker 变电站内断路器(0305)
    @Autowired
    DeviceStationBreakerMapper deviceStationBreakerMapper;

    // (配电站内变压器(0302)
    @Autowired
    DeviceStationTransformerMapper deviceStationTransformerMapper;

    @Autowired
    DeviceMapper deviceMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    /**
     * 根据设备id和设备type 获取设备名称
     *
     * @param psrId
     * @param psrType
     * @return
     */
    public DevInfo getKg(String psrId, String psrType) {

        // 配电柱上负荷开关（0112）、配电柱上断  路器（0111）
        if (StringUtils.equals(psrType, "0112") || StringUtils.equals(psrType, "0112")) {
            LambdaQueryWrapper<DevicePoleBreak> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DevicePoleBreak::getPsrId, psrId).eq(DevicePoleBreak::getPsrType, psrType);
            DevicePoleBreak devicePoleBreak = devicePoleBreakMapper.selectOne(queryWrapper);
            return new DevInfo(devicePoleBreak.getPsrId(), devicePoleBreak.getPsrType(), devicePoleBreak.getName());
        } else if (StringUtils.equals(psrType, "0306")) {
            DeviceStationIsolateKg deviceStationIsolateKg = deviceStationIsolateKgMapper.selectByPsrId(psrId);
            return new DevInfo(deviceStationIsolateKg.getPsrId(), "0306", deviceStationIsolateKg.getName());
        } else if (StringUtils.equals(psrType, "0307")) {
            DeviceStationLoadKg deviceStationLoadKg = deviceStationLoadKgMapper.selectByPsrId(psrId);
            return new DevInfo(deviceStationLoadKg.getPsrId(), "0307", deviceStationLoadKg.getName());
        } else if (StringUtils.equals(psrType, "0305")) {
            DeviceStationBreaker deviceStationBreaker = deviceStationBreakerMapper.selectByPsrId(psrId);
            return new DevInfo(deviceStationBreaker.getPsrId(), "0305", deviceStationBreaker.getName());
        }
        return null;
    }


    /**
     * 根据线路id查询母线等信息
     *
     * @param feederId
     * @return
     */
    public ByFeederBusBarAndBreaker selectBusBarAndBreaker(String feederId) {
        ByFeederBusBarAndBreaker byFeederBusBarAndBreaker = new ByFeederBusBarAndBreaker();
        //第一步 根据feederId查询设备线路mrid和变电站id
        Middle feederMrIdAndSubstationIdMiddle = deviceMapper.selectFeederMrIdAndSubstationIdInfo(feederId);
        if (feederMrIdAndSubstationIdMiddle != null) {
            byFeederBusBarAndBreaker.setSubstationId(feederMrIdAndSubstationIdMiddle.getId2());
            //查询变电站信息
            String substationName = deviceMapper.selectSubstationName(feederMrIdAndSubstationIdMiddle.getId2());
            if (StringUtils.isNotBlank(substationName)) {
                byFeederBusBarAndBreaker.setSubstationName(substationName);
            }

        }

        //第二步查询根据feederId查询母线id和间隔id
        Middle busBarIdAndBreakerIdMiddle = deviceMapper.selectBusBarIdAndBreakerIdInfo(feederId);
        if (busBarIdAndBreakerIdMiddle != null) {
            //查询间隔信息
            Breaker breaker = deviceMapper.seletBreaker(busBarIdAndBreakerIdMiddle.getId1());
            if (breaker != null) {
                byFeederBusBarAndBreaker.setBreakerId(breaker.getBreakerId());
                byFeederBusBarAndBreaker.setBreakerName(breaker.getBreakerName());
                byFeederBusBarAndBreaker.setIsInUse(isValidString(breaker.getBreakerName()));
            }
            BusBar busBar = deviceMapper.seletBusBar(busBarIdAndBreakerIdMiddle.getId2());
            //查询母线信息
            if (busBar != null) {
                byFeederBusBarAndBreaker.setBusBarId(busBar.getBusBarId());
                byFeederBusBarAndBreaker.setBusBarName(busBar.getBusBarName());
            }
        }


        return byFeederBusBarAndBreaker;

    }


    /**
     * 根据变电站名称查询所有的母线等信息
     *
     * @param substationName
     * @return
     */
    public BySubstationBusBarAndBreaker selectSubstationBusBarAndBreaker(String substationName) {
        BySubstationBusBarAndBreaker bySubstationBusBarAndBreaker = new BySubstationBusBarAndBreaker();
        //第一步根据变电站名字查询ID
        String substationId = deviceMapper.selectSubstationId(substationName);
        if (StringUtils.isBlank(substationId)) {
            return null;
        }
        List<BusBar> returnBusBarList = new ArrayList<>();
        //查询变电站下的间隔
        List<Breaker> breakerList = deviceMapper.selectBreakerList(substationId);
        if (CollectionUtils.isNotEmpty(breakerList)) {
            for (Breaker breaker : breakerList) {
                breaker.setIsInUse(isValidString(breaker.getBreakerName()));
            }
            bySubstationBusBarAndBreaker.setBreakerList(breakerList);
            List<String> idList = breakerList.stream().map(Breaker::getBreakerId).collect(Collectors.toList());
            List<BusBar> busBarList = deviceMapper.selectBusBarListByBreak(idList);
            returnBusBarList.addAll(busBarList);
        }

        //去重
        if (CollectionUtils.isNotEmpty(returnBusBarList)) {
            bySubstationBusBarAndBreaker.setBusBarList(new ArrayList<>(returnBusBarList.stream()
                    .filter(busBar -> busBar != null && busBar.getBusBarId() != null) // 过滤 null
                    .collect(Collectors.toMap(
                            BusBar::getBusBarId,  // 以 busBarId 作为 Key
                            busBar -> busBar,     // 保留 BusBar 本身
                            (existing, replacement) -> existing  // 重复时保留已存在的
                    ))
                    .values()));

        }


        return bySubstationBusBarAndBreaker;
    }

    /**
     * 根据设备ID和类型列表查询设备的额定容量
     * @param idAndPsrTypeList 包含设备ID和类型的列表
     * @return 所有设备的额定容量列表（无效或空值转为0.0），保持原始ID顺序
     */
    public List<Double> selectDeviceRatedCapacity(List<PsrIdAndPsrType> idAndPsrTypeList) {
        // 按设备类型分组，生成类型->ID列表的映射（保持原始顺序）
        Map<String, List<String>> typeToIdsMap = idAndPsrTypeList.stream()
                .collect(Collectors.groupingBy(
                        PsrIdAndPsrType::getPsrType,
                        LinkedHashMap::new, // 保持类型分组顺序
                        Collectors.mapping(PsrIdAndPsrType::getPsrId, Collectors.toList())
                ));

        // 对每种类型处理并扁平化为单个流
        return typeToIdsMap.entrySet().stream()
                .flatMap(entry -> {
                    List<String> ids = entry.getValue();
                    List<Double> capacities = processDeviceType(entry.getKey(), ids);

                    // 创建ID到容量的映射以便排序
                    Map<String, Double> idToCapacity = IntStream.range(0, ids.size())
                            .boxed()
                            .collect(Collectors.toMap(
                                    ids::get,
                                    i -> capacities.get(i),
                                    (a, b) -> a,
                                    LinkedHashMap::new
                            ));

                    // 按原始ID顺序返回结果
                    return ids.stream()
                            .map(id -> idToCapacity.getOrDefault(id, 0.0));
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据设备类型处理对应的设备ID列表
     * @param psrType 设备类型代码
     * @param ids 该类型下的设备ID列表
     * @return 该类型所有设备的额定容量列表
     */
    private List<Double> processDeviceType(String psrType, List<String> ids) {
        // 空列表检查：如果设备ID列表为空，直接返回空结果
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        // 根据不同类型调用不同的处理方法
        switch (psrType) {
            case ZSB: // 柱上变压器类型
                return processPoleTransformerCapacity(ids);
            case ZNPDBYQ: // 站内配电变压器类型
                return processStationTransformerCapacity(ids);
            case SYB: // 所用变压器类型
                return processServiceTransformerCapacity(ids);
            default: // 未知类型返回空列表
                return Collections.emptyList();
        }
    }

    /**
     * 处理柱上变压器类型的设备(容量)
     * @param ids 设备ID列表（保持顺序）
     * @return 额定容量列表（与输入ID顺序一致）
     */
    private List<Double> processPoleTransformerCapacity(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<DevicePoleTransformer> transformers = deviceMapper.selectDevicePoleTransformer(ids);
        return mapResultsToOriginalOrder(ids, transformers, DevicePoleTransformer::getPsrId, DevicePoleTransformer::getRatedCapacity);
    }

    /**
     * 处理站内变压器类型的设备(容量)
     * @param ids 设备ID列表（保持顺序）
     * @return 额定容量列表（与输入ID顺序一致）
     */
    private List<Double> processStationTransformerCapacity(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<DeviceStationTransformer> transformers = deviceMapper.selectDeviceStationTransformer(ids);
        return mapResultsToOriginalOrder(ids, transformers, DeviceStationTransformer::getPsrId, DeviceStationTransformer::getRatedCapacity);
    }

    /**
     * 处理所用变压器类型的设备(容量)
     * @param ids 设备ID列表（保持顺序）
     * @return 额定容量列表（与输入ID顺序一致）
     */
    private List<Double> processServiceTransformerCapacity(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<StationServiceTransformer> transformers = deviceMapper.selectStationServiceTransformer(ids);
        return mapResultsToOriginalOrder(ids, transformers, StationServiceTransformer::getPsrId, StationServiceTransformer::getRatedCapacity);
    }

    /**
     * 通用方法：将查询结果映射回原始ID顺序
     * @param ids 原始ID列表（保持顺序）
     * @param results 查询结果列表
     * @param idExtractor 从结果对象提取ID的方法引用
     * @param capacityExtractor 从结果对象提取容量的方法引用
     * @return 按原始ID顺序排列的容量列表
     */
    private <T> List<Double> mapResultsToOriginalOrder(List<String> ids,
                                                       List<T> results,
                                                       Function<T, String> idExtractor,
                                                       Function<T, String> capacityExtractor) {
        // 创建ID到对象的映射
        Map<String, T> idToObject = results.stream()
                .collect(Collectors.toMap(
                        idExtractor,
                        Function.identity(),
                        (a, b) -> a
                ));

        // 按原始ID顺序获取容量
        return ids.stream()
                .map(id -> {
                    T obj = idToObject.get(id);
                    String capacity = obj != null ? capacityExtractor.apply(obj) : null;
                    return parseCapacity(capacity);
                })
                .collect(Collectors.toList());
    }

    /**
     * 解析容量字符串为Double值
     * @param capacityStr 容量字符串（可能为null或空）
     * @return 解析后的Double值（无效值返回0.0）
     */
    private Double parseCapacity(String capacityStr) {
        return StringUtils.isBlank(capacityStr) ? 0.0 : Double.parseDouble(capacityStr);
    }


    /**
     * 根据设备ID和类型列表查询设备信息
     * @param idAndPsrTypeList 包含设备ID和类型的列表
     * @return 设备信息列表（保持原始顺序）
     */
    public List<PublicSpecializedTransformer> selectDevice(List<PsrIdAndPsrType> idAndPsrTypeList) {
        // 按设备类型分组
        Map<String, List<String>> typeToIdsMap = idAndPsrTypeList.stream()
                .collect(Collectors.groupingBy(
                        PsrIdAndPsrType::getPsrType,
                        LinkedHashMap::new, // 保持类型分组顺序
                        Collectors.mapping(PsrIdAndPsrType::getPsrId, Collectors.toList())
                ));

        // 对每种类型处理并收集结果
        return typeToIdsMap.entrySet().stream()
                .flatMap(entry -> {
                    String psrType = entry.getKey();
                    List<String> ids = entry.getValue();

                    // 根据不同类型调用不同的处理方法
                    List<PublicSpecializedTransformer> result = new ArrayList<>();
                    switch (psrType) {
                        case ZSB:
                            result = processPoleTransformers(ids);
                            break;
                        case ZNPDBYQ:
                            result = processStationTransformers(ids);
                            break;
                        case SYB:
                            result = processServiceTransformers(ids);
                            break;
                    }
                    return result.stream();
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理柱上变压器类型的设备
     * @param ids 设备ID列表
     * @return 转换后的设备信息列表
     */
    private List<PublicSpecializedTransformer> processPoleTransformers(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<DevicePoleTransformer> transformers = deviceMapper.selectDevicePoleTransformer(ids);
        return transformers.stream()
                .map(this::convertToPublicTransformer)
                .peek(t -> t.setPsrType("0110")) // 设置特定类型
                .collect(Collectors.toList());
    }

    /**
     * 处理站内变压器类型的设备
     * @param ids 设备ID列表
     * @return 转换后的设备信息列表
     */
    private List<PublicSpecializedTransformer> processStationTransformers(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<DeviceStationTransformer> transformers = deviceMapper.selectDeviceStationTransformer(ids);
        return transformers.stream()
                .map(this::convertToPublicTransformer)
                .peek(t -> {
                    t.setPsrType("0302");
                    t.setRatedCapacity(t.getRatedCapacity()); // 特殊字段处理
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理所用变压器类型的设备
     * @param ids 设备ID列表
     * @return 转换后的设备信息列表
     */
    private List<PublicSpecializedTransformer> processServiceTransformers(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<StationServiceTransformer> transformers = deviceMapper.selectStationServiceTransformer(ids);
        return transformers.stream()
                .map(this::convertToPublicTransformer)
                .peek(t -> t.setPsrType("0303"))
                .collect(Collectors.toList());
    }

    /**
     * 通用转换方法 - 将各种变压器类型转换为PublicSpecializedTransformer
     * @param source 源对象
     * @return 转换后的PublicSpecializedTransformer
     */
    private <T> PublicSpecializedTransformer convertToPublicTransformer(T source) {
        PublicSpecializedTransformer result = new PublicSpecializedTransformer();
        if (source == null) {
            return result;
        }
        if (source instanceof DevicePoleTransformer) {
            DevicePoleTransformer transformer = (DevicePoleTransformer) source;
            result.setFeeder(transformer.getFeeder());
            result.setName(transformer.getName());
            result.setPsrId(transformer.getPsrId());
            result.setRatedCapacity(transformer.getRatedCapacity());
            result.setJoinEc(transformer.getJoinEc());
            result.setPsrState(transformer.getPsrState());
            result.setPubPrivFlag(transformer.getPubPrivFlag());
            result.setLongitude(transformer.getLongitude() != null ? transformer.getLongitude().toString() : null);
            result.setLatitude(transformer.getLatitude() != null ? transformer.getLatitude().toString() : null);
            result.setVoltageLevel(transformer.getVoltageLevel());
        }
        else if (source instanceof DeviceStationTransformer) {
            DeviceStationTransformer transformer = (DeviceStationTransformer) source;
            result.setFeeder(transformer.getFeeder());
            result.setName(transformer.getName());
            result.setPsrId(transformer.getPsrId());
            result.setRatedCapacity(transformer.getInstalledCapacity()); // 特殊字段
            result.setJoinEc(transformer.getJoinEc());
            result.setPsrState(transformer.getPsrState());
            result.setPubPrivFlag(transformer.getPubPrivFlag());
            result.setLongitude(transformer.getLongitude() != null ? transformer.getLongitude().toString() : null);
            result.setLatitude(transformer.getLatitude() != null ? transformer.getLatitude().toString() : null);
            result.setVoltageLevel(transformer.getVoltageLevel());
        }
        else if (source instanceof StationServiceTransformer) {
            StationServiceTransformer transformer = (StationServiceTransformer) source;
            result.setFeeder(transformer.getFeeder());
            result.setName(transformer.getName());
            result.setPsrId(transformer.getPsrId());
            result.setRatedCapacity(transformer.getRatedCapacity());
            result.setJoinEc(transformer.getJoinEc());
            result.setPsrState(transformer.getPsrState());
            result.setPubPrivFlag(transformer.getPubPrivFlag());
            result.setLongitude(transformer.getLongitude() != null ? transformer.getLongitude().toString() : null);
            result.setLatitude(transformer.getLatitude() != null ? transformer.getLatitude().toString() : null);
            result.setVoltageLevel(transformer.getVoltageLevel());
        }

        return result;
    }


    /**
     * 根据设备id和设备类型查找设备坐标(目前是查抄wlgt和0202、0203)
     *
     * @param
     * @return 容量
     */
    public List<Double> selectDeviceCoords(String psrId, String psrType) {
        List<Double> coordsList = new ArrayList<>();

        //wlgt坐标
        if (POLE_TYPES.contains(psrType)) {
            String coords = deviceMapper.selectWLGTCoords(psrId);
            if (StringUtils.isNotBlank(coords)) {
                coordsList.addAll(CoordinateConverter.parseCommaCoordinates(coords, ","));
            }
        }
        //0202、0303的坐标
        if (JUNCTION_TYPES.contains(psrType)) {
            String coords = deviceMapper.selectSDDLCoords(psrId);
            if (StringUtils.isNotBlank(coords)) {
                coordsList.addAll(CoordinateConverter.parseCommaCoordinates(coords, ","));
            }
        }


        return coordsList;
    }

    /**
     * 根据中压用户接入点id查询其对应容量
     *
     * @param joinEcList
     * @return 容量
     */
    public List<DeviceAccessPointBo> selectJoinEcRatedCapacity(List<String> joinEcList) {
        List<DeviceAccessPointBo> deviceAccessPointList = deviceMapper.selectJoinEcCapacity(joinEcList);
        for (DeviceAccessPointBo accessPoint : deviceAccessPointList) {
            //如果是空则返回0
            if (StringUtils.isBlank(accessPoint.getCapacity())) {
                accessPoint.setCapacity("0");
            }
            //如果是双容量只取第一个容量
            accessPoint.setCapacity(isTwoCapacity(accessPoint.getCapacity()));

        }
        return deviceAccessPointList;
    }

    /**
     * 根据间隔id查询所在母线下所有的间隔id
     *
     * @param breakId
     * @return 容量
     */
    public List<Breaker> selectBreakIds(String breakId) {
        List<Breaker> breakIds = deviceMapper.selectBreakIds(breakId);
        if (CollectionUtils.isNotEmpty(breakIds)) {
            for (Breaker breaker : breakIds) {
                breaker.setIsInUse(isValidString(breaker.getBreakerName()));
            }
        }
        return breakIds;
    }


    /**
     * 根据间隔名称判断是否是在用的
     *
     * @param input
     * @return
     */
    public static boolean isValidString(String input) {
        if (input == null) {
            return true; // 或根据需求返回 false
        }
        return !input.contains("预留") && !input.contains("备用");
    }

    /**
     * 判断容量是否是2个
     *
     * @param input
     * @return
     */
    public static String isTwoCapacity(String input) {
        if (input == null) {
            return "0"; // 或根据需求返回 false
        }
        if (input.contains("/")) {
            String[] s = input.split("/");
            return calculation(s[0]);
        }

        return calculation(input);
    }

    //计算容量
    public static String calculation(String input) {
        //带单位就先去掉单位
        if (input.contains("kVA")) {
            String result = input.replaceAll("kVA", "");
            Expression expression = new ExpressionBuilder(result).build();
            return String.valueOf(expression.evaluate());
        }
        //不带单位直接计算
        Expression expression = new ExpressionBuilder(input).build();
        return String.valueOf(expression.evaluate());
    }

    /**
     * 根据中压用户接入点设备ID集合获取 配电站内变压器(0302)
     */
    public List<DeviceStationTransformer> getMiddleUserPdPb(List<String> psrIds) {
        LambdaQueryWrapper<DeviceStationTransformer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceStationTransformer::getJoinEc, psrIds);

        return deviceStationTransformerMapper.selectList(queryWrapper);
    }


}
