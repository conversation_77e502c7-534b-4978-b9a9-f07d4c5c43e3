package com.ruoyi.service.cost.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.cost.*;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.vo.PlanCost;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.mapper.cost.CostMapper;
import com.ruoyi.service.cost.ICostService;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ruoyi.entity.cost.DeviceType.*;
import static com.ruoyi.entity.cost.DeviceType.CABLE;


@Service
public class CostServiceImpl implements ICostService {
    @Autowired
    CostMapper costMapper;

    /**
     * 查询符合条件的造价
     * @param costList
     * @return
     */
    @Override
    public Cost selectCost(List<CostType> costList) {

        Cost cost = new Cost();
        //循环解出每个类型的造价，根据不同类型的不同数量计算价格
        for (CostType costType : costList) {
            if (costType instanceof CostBayUnit) {
               // 间隔单元
                CostBayUnit cable = (CostBayUnit) costType;
                //查询单元价格类型造价实体
                CostBayUnit entity = costMapper.selectCostBayUnit(cable);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            } else if (costType instanceof CostBoxTypeSubstation) {
                //箱式变电站
                CostBoxTypeSubstation costBoxTypeSubstation = (CostBoxTypeSubstation) costType;
                //查询箱式变电站的类型造价实体
                CostBoxTypeSubstation entity = costMapper.selectCostBoxTypeSubstation(costBoxTypeSubstation);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            } else if (costType instanceof CostCableLine) {
                //电缆线路
                CostCableLine costCableLine = (CostCableLine) costType;
                //查询电缆线路的类型造价实体
                CostCableLine entity = costMapper.selectCostCableLine(costCableLine);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            } else if (costType instanceof CostPoleSwitch) {
                //柱上开关
                CostPoleSwitch costPoleSwitch = (CostPoleSwitch) costType;
                //查询柱上开关的类型造价实体
                CostPoleSwitch entity = costMapper.selectCostPoleSwitch(costPoleSwitch);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            } else if (costType instanceof CostPoleUbstation) {
                //柱上变
                CostPoleUbstation costPoleUbstation = (CostPoleUbstation) costType;
                //查询柱上变的类型造价实体
                CostPoleUbstation entity = costMapper.selectCostPoleUbstation(costPoleUbstation);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }else if (costType instanceof CostPowerDistributionRoom) {
                //配电室
                CostPowerDistributionRoom costPowerDistributionRoom = (CostPowerDistributionRoom) costType;
                //查询配电室的类型造价实体
                CostPowerDistributionRoom entity = costMapper.selectCostPowerDistributionRoom(costPowerDistributionRoom);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }else if (costType instanceof CostPowerDuct) {
                //电力排管
                CostPowerDuct costPowerDuct = (CostPowerDuct) costType;
                //查询电力排管的类型造价实体
                CostPowerDuct entity = costMapper.selectCostPowerDuct(costPowerDuct);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }else if (costType instanceof CostPowerGroove) {
                //电力沟槽
                CostPowerGroove costPowerGroove = (CostPowerGroove) costType;
                //查询电力沟槽的类型造价实体
                CostPowerGroove entity = costMapper.selectCostPowerGroove(costPowerGroove);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }else if (costType instanceof CostPowerTunnel) {
                //电力隧道
                CostPowerTunnel costPowerTunnel = (CostPowerTunnel) costType;
                //查询电力隧道的类型造价实体
                CostPowerTunnel entity = costMapper.selectCostPowerTunnel(costPowerTunnel);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }else if (costType instanceof CostRingCabinet) {
                //环柜
                CostRingCabinet costRingCabinet = (CostRingCabinet) costType;
                //查询环柜的类型造价实体
                CostRingCabinet entity = costMapper.selectCostRingCabinet(costRingCabinet);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }else if (costType instanceof CostSwitchStation) {
                //开关站
                CostSwitchStation costSwitchStation = (CostSwitchStation) costType;
                //查询开关站的类型造价实体
                CostSwitchStation entity = costMapper.selectCostSwitchStation(costSwitchStation);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }else if (costType instanceof CostTransformer) {
                //变压器
                CostTransformer costTransformer = (CostTransformer) costType;
                //查询变压器的类型造价实体
                CostTransformer entity = costMapper.selectCostTransformer(costTransformer);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }else if (costType instanceof CostTrollyWire) {
                //架空线
                CostTrollyWire costTransformer = (CostTrollyWire) costType;
                //查询架空线的类型造价实体
                CostTrollyWire entity = costMapper.selectCostTrollyWire(costTransformer);
                //累计价格
                cumulative(cost,entity,costType.getNum());
            }


        }

        return cost;

    }


    /**
     * 根据每个此传进来的造价，计算累计的造价
     * @param cost
     * @param costType
     * @param num
     */

    public  void cumulative(Cost cost, CostType costType,Double num){
        if(cost.getTotalCost()==null){
            cost.setTotalCost(DoubleFormatter.formatToThreeDecimals2(costType.getTotalCost()*num));

            cost.setPurchaseCost(DoubleFormatter.formatToThreeDecimals2(costType.getPurchaseCost()*num));

            cost.setBuildingCost(DoubleFormatter.formatToThreeDecimals2(costType.getBuildingCost()*num));

            cost.setInstallCost(DoubleFormatter.formatToThreeDecimals2(costType.getInstallCost()*num));

            cost.setOtherCost(DoubleFormatter.formatToThreeDecimals2(costType.getOtherCost()*num));
        }else {
            cost.setTotalCost(DoubleFormatter.formatToThreeDecimals2(cost.getTotalCost()+(costType.getTotalCost()*num)));

            cost.setPurchaseCost(DoubleFormatter.formatToThreeDecimals2(cost.getPurchaseCost()+(costType.getPurchaseCost()*num)));

            cost.setBuildingCost(DoubleFormatter.formatToThreeDecimals2(cost.getBuildingCost()+(costType.getBuildingCost()*num)));

            cost.setInstallCost(DoubleFormatter.formatToThreeDecimals2(cost.getInstallCost()+(costType.getInstallCost()*num)));

            cost.setOtherCost(DoubleFormatter.formatToThreeDecimals2(cost.getOtherCost()+(costType.getOtherCost()*num)));
        }




    }

    /**
     * 根据方案计算造价
     */
    @Override
    public PlanCost computeCost(Plan plan) {

        // 设备列表的JSON字符串
        JSONObject root = JSON.parseObject(plan.getOperateData());

        // 解析获得设备的nodeList
        JSONArray devices = root.getJSONArray("devices");
        // 使用FastJSON解析
        List<NodeVo> devicesVoList = JSON.parseObject(
                devices.toJSONString(),
                new TypeReference<List<NodeVo>>() {
                }
        );
        List<Node> devicesList = NodeUtils.toNodes(devicesVoList);


        // 解析获得边的nodeList
        JSONArray edges = root.getJSONArray("edges");
        List<NodeVo> edgesVoList = JSON.parseObject(
                edges.toJSONString(),
                new TypeReference<List<NodeVo>>() {
                }
        );
        List<Node> edgesList = NodeUtils.toNodes(edgesVoList);

        //统计非电网的每种设备数量
        // 按 psrType 分组，每组收集所有 Node 的 properties
        Map<String, List<HashMap<String, Object>>> groupedProperties = devicesList.stream()
                .filter(node -> node.getPsrType() != null)  // 过滤 psrType 为 null 的节点
                .collect(Collectors.groupingBy(
                        Node::getPsrType,  // 按 psrType 分组
                        Collectors.mapping(Node::getProperties, Collectors.toList())  // 收集 properties
                ));

        // 按 psrType 分组，每组只保留 geometry 字段，并明确指定类型为 Geometry
        Map<String, List<Geometry>> feederMap = edgesList.stream()
                .filter(device -> device.getPsrType() != null && !device.getPsrType().isEmpty() && !device.isPsrNode())
                .filter(device -> device.getGeometry() instanceof Geometry) // 确保 geometry 类型正确
                .collect(Collectors.groupingBy(
                        Node::getPsrType,
                        Collectors.mapping(
                                Node::getGeometry, // 强制类型转换为 Geometry
                                Collectors.toList()
                        )
                ));

        List<CostType> costTypes = new ArrayList<>();
        PlanCost planCost = new PlanCost();
        //统计物理杆塔数量以及造价查询实体
        int wlgtCount = 0;
        List<HashMap<String, Object>> wlgtList = groupedProperties.get(WLGT);
        if (CollectionUtils.isNotEmpty(wlgtList)) {
            for (HashMap<String, Object> objectHashMap : wlgtList) {
                wlgtCount++;

            }
        }
        planCost.setWlgtCount(wlgtCount);

        int rungtCount = 0;
        //统计运行杆塔数量以及造价查询实体
        List<HashMap<String, Object>> rungtList = groupedProperties.get(RUNGT);
        if (CollectionUtils.isNotEmpty(rungtList)) {
            for (HashMap<String, Object> objectHashMap : rungtList) {
                rungtCount++;

            }
        }
        planCost.setRungtCount(rungtCount);

        //统计环网柜1数量以及造价查询实体
        int hwgCount = 0;
        List<HashMap<String, Object>> hwg1List = groupedProperties.get(HWG1);
        if (CollectionUtils.isNotEmpty(hwg1List)) {
            for (HashMap<String, Object> objectHashMap : hwg1List) {
                hwgCount++;
                CostRingCabinet costRingCabinet = new CostRingCabinet();
                costRingCabinet.setVoltageLevel("10kV");
                costRingCabinet.setBusConnection("单母线");
                costRingCabinet.setInlineInt(2);
                costRingCabinet.setOutlineInt(4);
                costRingCabinet.setNum(1.0);
                costTypes.add(costRingCabinet);
            }
        }

        //统计环网柜2数量以及造价查询实体
        List<HashMap<String, Object>> hwg2List = groupedProperties.get(HWG2);
        if (CollectionUtils.isNotEmpty(hwg2List)) {
            for (HashMap<String, Object> objectHashMap : hwg1List) {
                hwgCount++;
                CostRingCabinet costRingCabinet = new CostRingCabinet();
                costRingCabinet.setVoltageLevel("10kV");
                costRingCabinet.setBusConnection("单母线");
                costRingCabinet.setInlineInt(2);
                costRingCabinet.setOutlineInt(4);
                costRingCabinet.setNum(1.0);
                costTypes.add(costRingCabinet);
            }
        }
        planCost.setHwgCount(hwgCount);

        //统计开关站数量以及造价查询实体
        int kgzCount = 0;
        List<HashMap<String, Object>> kgzList = groupedProperties.get(KGZ);
        if (CollectionUtils.isNotEmpty(kgzList)) {
            for (HashMap<String, Object> objectHashMap : kgzList) {
                kgzCount++;
                CostRingCabinet costRingCabinet = new CostRingCabinet();
                costRingCabinet.setVoltageLevel("10kV");
                costRingCabinet.setBusConnection("单母线");
                costRingCabinet.setInlineInt(2);
                costRingCabinet.setOutlineInt(4);
                costRingCabinet.setNum(1.0);
                costTypes.add(costRingCabinet);
            }
        }
        planCost.setKgzCount(kgzCount);

        //统计柱上负荷开关数量以及造价查询实体
        int zsfhkgCount = 0;
        List<HashMap<String, Object>> zsfhkgList = groupedProperties.get(ZSFHKG);
        if (CollectionUtils.isNotEmpty(zsfhkgList)) {
            for (HashMap<String, Object> objectHashMap : zsfhkgList) {
                zsfhkgCount++;
                CostPoleSwitch costPoleSwitch = new CostPoleSwitch();
                costPoleSwitch.setVoltageLevel("10kV");
                costPoleSwitch.setSwitchType("柱上负荷开关");
                costPoleSwitch.setMaxCurrent(630);
                costPoleSwitch.setCutCurrent(20);
                costPoleSwitch.setNum(1.0);
                costTypes.add(costPoleSwitch);
            }
        }
        planCost.setZsfhkgCount(zsfhkgCount);

        //统计柱上断路器数量以及造价查询实体
        int zsdlqCount = 0;
        List<HashMap<String, Object>> zsdlqList = groupedProperties.get(ZSDLQ);
        if (CollectionUtils.isNotEmpty(zsdlqList)) {
            for (HashMap<String, Object> objectHashMap : zsdlqList) {
                zsdlqCount++;
                CostPoleSwitch costPoleSwitch = new CostPoleSwitch();
                costPoleSwitch.setVoltageLevel("10kV");
                costPoleSwitch.setSwitchType("柱上断路器");
                costPoleSwitch.setMaxCurrent(630);
                costPoleSwitch.setCutCurrent(20);
                costPoleSwitch.setNum(1.0);
                costTypes.add(costPoleSwitch);
            }
        }
        planCost.setZsdlqCount(zsdlqCount);

        //统计站内负荷开关数量以及造价查询实体
        int znfhkgCount = 0;
        List<HashMap<String, Object>> znfhkgList = groupedProperties.get(ZNFHKG);
        if (CollectionUtils.isNotEmpty(znfhkgList)) {
            for (HashMap<String, Object> objectHashMap : znfhkgList) {
                znfhkgCount++;
                CostBayUnit costBayUnit = new CostBayUnit();
                costBayUnit.setVoltageLevel("10kV");
                costBayUnit.setGapType("出线单元");
                costBayUnit.setCapacity("");
                costBayUnit.setFacilityType("站内断路器");
                costBayUnit.setMaxCutCurrent(20.000);
                costBayUnit.setMaxCurrent(630.000);
                costBayUnit.setNum(1.0);
                costTypes.add(costBayUnit);
            }
        }
        planCost.setZnfhkgCount(znfhkgCount);

        //统计站内断路器数量以及造价查询实体
        int zndlqCount = 0;
        List<HashMap<String, Object>> zndlqList = groupedProperties.get(ZNDLQ);
        if (CollectionUtils.isNotEmpty(zndlqList)) {
            for (HashMap<String, Object> objectHashMap : zndlqList) {
                zndlqCount++;
                CostBayUnit costBayUnit = new CostBayUnit();
                costBayUnit.setVoltageLevel("10kV");
                costBayUnit.setGapType("出线单元");
                costBayUnit.setCapacity("");
                costBayUnit.setFacilityType("站内断路器");
                costBayUnit.setMaxCutCurrent(20.000);
                costBayUnit.setMaxCurrent(630.000);
                costBayUnit.setNum(1.0);
                costTypes.add(costBayUnit);
            }
        }
        planCost.setZndlqCount(zndlqCount);

        //统计站内配电变压器数量以及造价查询实体
        int znpdbyqCount = 0;
        List<HashMap<String, Object>> znpdbyqList = groupedProperties.get(ZNPDBYQ);
        if (CollectionUtils.isNotEmpty(znpdbyqList)) {
            for (HashMap<String, Object> objectHashMap : znpdbyqList) {
                znpdbyqCount++;
                CostTransformer costTransformer = new CostTransformer();
                costTransformer.setVoltageLevel("10kV");
                costTransformer.setVoltageRatio("10/0.4");
                costTransformer.setCapacity(200);
                costTransformer.setNum(1.0);
                costTypes.add(costTransformer);
            }
        }
        planCost.setZnpdbyqCount(znpdbyqCount);

        //统计箱变1数量以及造价查询实体
        int xbCount = 0;
        List<HashMap<String, Object>> xb1List = groupedProperties.get(XB1);
        if (CollectionUtils.isNotEmpty(xb1List)) {
            for (HashMap<String, Object> objectHashMap : xb1List) {
                xbCount++;
                CostBoxTypeSubstation costBoxTypeSubstation = new CostBoxTypeSubstation();
                costBoxTypeSubstation.setVoltageLevel("10kV");
                costBoxTypeSubstation.setBusConnection("单母线");
                costBoxTypeSubstation.setCurrentNumber(1);
                costBoxTypeSubstation.setEndNumber(1);
                costBoxTypeSubstation.setInlineInt(1);
                costBoxTypeSubstation.setOutlineInt(1);
                costBoxTypeSubstation.setCapacity(630);
                costBoxTypeSubstation.setNum(1.0);
                costTypes.add(costBoxTypeSubstation);
            }
        }

        //统计箱变2数量以及造价查询实体
        List<HashMap<String, Object>> xb2List = groupedProperties.get(XB2);
        if (CollectionUtils.isNotEmpty(xb2List)) {
            for (HashMap<String, Object> objectHashMap : xb2List) {
                xbCount++;
                CostBoxTypeSubstation costBoxTypeSubstation = new CostBoxTypeSubstation();
                costBoxTypeSubstation.setVoltageLevel("10kV");
                costBoxTypeSubstation.setBusConnection("单母线");
                costBoxTypeSubstation.setCurrentNumber(1);
                costBoxTypeSubstation.setEndNumber(1);
                costBoxTypeSubstation.setInlineInt(1);
                costBoxTypeSubstation.setOutlineInt(1);
                costBoxTypeSubstation.setCapacity(630);
                costBoxTypeSubstation.setNum(1.0);
                costTypes.add(costBoxTypeSubstation);
            }
        }
        planCost.setXbCount(xbCount);


        //统计配电室数量以及造价查询实体
        int pdsCount = 0;
        List<HashMap<String, Object>> pdsList = groupedProperties.get(PDS);
        if (CollectionUtils.isNotEmpty(pdsList)) {
            for (HashMap<String, Object> objectHashMap : pdsList) {
                pdsCount++;
                CostPowerDistributionRoom costPowerDistributionRoom = new CostPowerDistributionRoom();
                costPowerDistributionRoom.setVoltageLevel("10kV");
                costPowerDistributionRoom.setBusConnection("单母线");
                costPowerDistributionRoom.setCurrentNumber(1);
                costPowerDistributionRoom.setEndNumber(1);
                costPowerDistributionRoom.setInlineInt(1);
                costPowerDistributionRoom.setOutlineInt(1);
                costPowerDistributionRoom.setCapacity(630);
                costPowerDistributionRoom.setNum(1.0);
                costTypes.add(costPowerDistributionRoom);
            }
        }
        planCost.setPdsCount(pdsCount);

        //统计柱上变造价查询实体
        int zsbCount = 0;
        List<HashMap<String, Object>> zsbList = groupedProperties.get(ZSB);
        if (CollectionUtils.isNotEmpty(zsbList)) {
            for (HashMap<String, Object> objectHashMap : zsbList) {
                zsbCount++;
                CostPoleUbstation costPoleUbstation = new CostPoleUbstation();
                costPoleUbstation.setVoltageLevel("10kV");
                costPoleUbstation.setCapacity(30);
                costPoleUbstation.setNum(1.0);
                costTypes.add(costPoleUbstation);
            }
        }
        planCost.setZsbCount(zsbCount);

        double jkLength = 0.0;
        //统计架空线造价查询实体
        List<Geometry> jkList = feederMap.get(JK);
        if (CollectionUtils.isNotEmpty(jkList)) {

            double sphericalDistance = getSphericalDistance(jkList);
            jkLength = jkLength + sphericalDistance;
            if (jkLength > 0) {
                CostTrollyWire costTrollyWire = new CostTrollyWire();
                costTrollyWire.setVoltageLevel("10kV");
                costTrollyWire.setSection(70);
                costTrollyWire.setLoopNumber("四回");
                costTrollyWire.setMethod("新开");
                costTrollyWire.setNum(DoubleFormatter.formatToThreeDecimals2(jkLength / 1000));
                costTypes.add(costTrollyWire);
            }

        }
        planCost.setJkLength(DoubleFormatter.formatToThreeDecimals2(jkLength / 1000));

        // 电缆线长度
        double cableLength = 0.0;
        //统计电缆线造价查询实体
        List<Geometry> cableList = feederMap.get(CABLE);
        if (CollectionUtils.isNotEmpty(cableList)) {
            double sphericalDistance = getSphericalDistance(cableList);
            cableLength = cableLength + sphericalDistance;

            if (cableLength > 0) {
                CostCableLine costCableLine = new CostCableLine();
                costCableLine.setVoltageLevel("10kV");
                costCableLine.setSection(120);
                costCableLine.setLoopNumber("四回");
                costCableLine.setNum(DoubleFormatter.formatToThreeDecimals2(cableLength / 1000));
                costTypes.add(costCableLine);
            }

        }
        planCost.setCableLength(DoubleFormatter.formatToThreeDecimals2(cableLength / 1000));

        Cost cost = selectCost(costTypes);
        planCost.setTotalCost(cost.getTotalCost());
        planCost.setPurchaseCost(cost.getPurchaseCost());
        planCost.setBuildingCost(cost.getBuildingCost());
        planCost.setInstallCost(cost.getInstallCost());
        planCost.setOtherCost(cost.getOtherCost());
        return planCost;


    }

    /**
     * 坐标转换成线路去计算长度
     * @param geometryList
     * @return
     */
    private static Double getSphericalDistance(List<Geometry> geometryList) {

        // 方式2: 使用ArrayList动态批量添加
        List<Coordinate> coordList = new ArrayList<>();
        // 创建GeometryFactory实例（使用默认精度）
        GeometryFactory factory = new GeometryFactory();

        for (Geometry geometry : geometryList) {
            coordList.add(geometry.getCoordinate());
        }
        Coordinate[] coords = coordList.toArray(new Coordinate[0]);
        try {
            // 创建LineString对象
            LineString lineString = factory.createLineString(coords);

            // 计算长度（单位取决于坐标系，例如经纬度坐标系下单位是度，需转换为米）
            double length = haversineLength(lineString);
            return length;
        } catch (Exception e) {
            // TODO 异常
            return 0.0;
        }
    }


    /**
     * 计算线路长度
     * @param line
     * @return
     */
    public static double haversineLength(LineString line) {
        final double R = 6371000; // 地球半径（米）
        double totalDistance = 0.0;

        for (int i = 0; i < line.getNumPoints() - 1; i++) {
            Coordinate c1 = line.getCoordinateN(i);
            Coordinate c2 = line.getCoordinateN(i + 1);

            double dLat = Math.toRadians(c2.y - c1.y);
            double dLon = Math.toRadians(c2.x - c1.x);
            double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                    Math.cos(Math.toRadians(c1.y)) * Math.cos(Math.toRadians(c2.y)) *
                            Math.sin(dLon / 2) * Math.sin(dLon / 2);
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            totalDistance += R * c;
        }
        return totalDistance;
    }
}
