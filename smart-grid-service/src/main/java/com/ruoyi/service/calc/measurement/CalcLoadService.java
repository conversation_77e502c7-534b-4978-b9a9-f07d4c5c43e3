package com.ruoyi.service.calc.measurement;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.bo.PsrIdAndPsrType;
import com.ruoyi.entity.device.vo.LoadCapacityChange;
import com.ruoyi.graph.Node;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class CalcLoadService {

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    /**
     * 根据线路id和不同的nodes计算负载和容量变化
     */
    public LoadCapacityChange computedFeederLoad(String feederId, List<Node> addNode, List<Node> removeNode) {
        if (StringUtils.isBlank(feederId) || (CollectionUtils.isEmpty(addNode) && CollectionUtils.isEmpty(removeNode))) {
            return null;
        }
        //查询该线路的装机容量
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectById(feederId);
        double totalCapacity = deviceFeeder.getFeederRateCapacity();
        Double load = feederDeviceMapper.selectLoad(feederId);

        //如果装机容量为空，且新增的设备也为空，返回null
        if (totalCapacity == 0.0 || load == null) {
            return null;
        }

        //查询新增的配变容量
        double addCapacity = 0.0;
        if (CollectionUtils.isNotEmpty(addNode)) {
            List<Double> addCapacityList = queryDeviceInfo.selectDeviceRatedCapacity(addNode.stream().map(node -> new PsrIdAndPsrType(node.getPsrId(), node.getPsrType())).collect(Collectors.toList()));
            addCapacity = addCapacityList.stream().filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
        }
        //查询移除的配变容量
        double removeCapacity = 0.0;
        if (CollectionUtils.isNotEmpty(removeNode)) {
            List<Double> removeCapacityList = queryDeviceInfo.selectDeviceRatedCapacity(removeNode.stream().map(node -> new PsrIdAndPsrType(node.getPsrId(), node.getPsrType())).collect(Collectors.toList()));
            removeCapacity = removeCapacityList.stream().filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
        }

        //变化的容量
        double changeCapacity = addCapacity - removeCapacity;

        LoadCapacityChange loadCapacityChange = new LoadCapacityChange();
        loadCapacityChange.setCurrentLoad(load);
        loadCapacityChange.setCurrentCapacity(totalCapacity);
        loadCapacityChange.setChangeCapacity(totalCapacity + addCapacity - removeCapacity);

        double changeLoad = calcFeederLoad(load, totalCapacity, changeCapacity);
        loadCapacityChange.setChangeLoad(changeLoad);

        return loadCapacityChange;
    }

    /**
     * 计算线路更改后的负载率
     * 变化的负载率 = （当前负载率 + （变化容量/当前容量）* 当前负载）
     *
     * @param feederLoad 线路负载率
     * @param feederCap  线路装机容量
     * @param changeCap  更改后的容量
     * @return
     */
    public static Double calcFeederLoad(Double feederLoad, Double feederCap, Double changeCap) {
        if (feederCap == null || feederLoad == 0.0) {
            return null;
        }
        return DoubleFormatter.formatToThreeDecimals3(feederLoad + (changeCap / feederCap * feederLoad));
    }
}
