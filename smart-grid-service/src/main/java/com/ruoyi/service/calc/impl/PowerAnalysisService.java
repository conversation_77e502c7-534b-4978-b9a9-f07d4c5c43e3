package com.ruoyi.service.calc.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.constant.DeviceTypeEnum;
import com.ruoyi.dto.*;
import com.ruoyi.entity.calc.*;
import com.ruoyi.entity.simulation.SimRetPfDmsBreaker;
import com.ruoyi.entity.simulation.SimRetPfDmsBusbar;
import com.ruoyi.entity.simulation.SimRetPfDmsSegment;
import com.ruoyi.entity.simulation.SimRetPfEmsBreaker;
import com.ruoyi.graph.utils.ZnapUtils;
import com.ruoyi.mapper.calc.*;
import com.ruoyi.service.simulation.ISimulationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 电力分析服务类
 * 提供潮流计算结果的各种分析功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PowerAnalysisService {

    // 注入各个Mapper
    private final SimPfRetMapper simPfRetMapper;
    private final SimRetPfNodeMapper simRetPfNodeMapper;
    private final SimRetPfDmsBreakerMapper simRetPfDmsBreakerMapper;
    private final SimRetPfDmsSegmentMapper simRetPfDmsSegmentMapper;
    private final SimRetPfDmsDtMapper simRetPfDmsDtMapper;
    private final SimRetScNodeMapper simRetScNodeMapper;
    private final SimRetPfDmsBusbarMapper simRetPfDmsBusbarMapper;
    private final SimRetPfEmsBreakerMapper simRetPfEmsBreakerMapper;
    private final TrendCalcMapper trendCalcMapper;
    private final CalcAlarmInfoMapper calcAlarmInfoMapper;
    private final ISimulationService iSimulationService;
    private final CalcRelationShipMapper calcRelationShipMapper;
    private final CalcInstanceInfoMapper calcInstanceInfoMapper;

    // 电压限值常量 (单位: kV)
    private static final double VOLTAGE_UPPER_LIMIT = 11.6;  // 电压上限
    private static final double VOLTAGE_LOWER_LIMIT = 9.5;   // 电压下限

    // 负载率阈值常量
    private static final double LOAD_RATE_WARNING = 20;     // 负载率告警阈值 20%
    private static final double LOAD_RATE_CRITICAL = 0.95;   // 负载率严重阈值 95%
    private static final double CURRENT_UPPER_LIMIT = 2;     // 电流

    // 配变容量阈值
    private static final double TRANSFORMER_WARNING = 0.288;   // 配变告警阈值 85%
    private static final double TRANSFORMER_CRITICAL = 0.5;   // 配变严重阈值 100%
    private static final double TRANSFORMER_RATED_CAPACITY = 100.0; // 配变额定容量 100kVA

    // 电流阈值
    // 配网开关电流
    private static final double CURRENT_DMS_BREAKER_WARNING = 11;
    // 主网开关电流
    private static final double CURRENT_EMS_BREAKER_WARNING = 11;

    /**
     * 潮流计算状态查询
     * @param problemId
     * @return
     */
    public ProblemCalcStatusDto calcStatus(Long problemId, String date) {
        ProblemCalcStatusDto problemCalcStatusDto = new ProblemCalcStatusDto();
        LambdaQueryWrapper<CalcInstanceInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CalcInstanceInfo::getProblemId, problemId);
        queryWrapper.eq(CalcInstanceInfo::getDate, date);
        // 并且网格和网格名词为空
        queryWrapper.isNull(CalcInstanceInfo::getGridCode);
        List<CalcInstanceInfo> calcInstanceInfos = calcInstanceInfoMapper.selectList(queryWrapper);
        if (calcInstanceInfos.isEmpty()) {
            problemCalcStatusDto.setStatus("2");
            return problemCalcStatusDto;
        }
        //选择最新计算的实例id
        CalcInstanceInfo calcInstanceInfo = calcInstanceInfos.stream().max(Comparator.comparing(CalcInstanceInfo::getStartTime)).get();
        // 先查询对应的instanceId
        LambdaQueryWrapper<CalcRelationShip> calcRelationShipLambdaQueryWrapper = new LambdaQueryWrapper<>();
        calcRelationShipLambdaQueryWrapper.eq(CalcRelationShip::getInstanceId, calcInstanceInfo.getInstanceId());
        CalcRelationShip calcRelationShip = calcRelationShipMapper.selectOne(calcRelationShipLambdaQueryWrapper);
        problemCalcStatusDto.setProblemId(problemId);
        problemCalcStatusDto.setInstanceId(calcInstanceInfo.getInstanceId());
        if (calcInstanceInfo.getEndTime() == null) {
            problemCalcStatusDto.setStatus("0");
            return problemCalcStatusDto;
        }
        if (calcRelationShip == null) {
            throw new RuntimeException("未找到对应的计算结果");
        }
        String msgId = calcRelationShip.getMsgId();
        if (StringUtils.isEmpty(msgId)) {
            throw new RuntimeException("未找到对应的计算结果");
        }
        // 查询潮流计算结果
        SimPfRet simPfRet = simPfRetMapper.findByMsgId(msgId);
        if (simPfRet == null) {
            throw new RuntimeException("未找到对应的计算结果");
        }
        Integer pfRet = simPfRet.getPfRet();
        // 如果==1,则计算完成，-1计算失败，为0正在计算
        if (pfRet == 1) {
            problemCalcStatusDto.setStatus("1");
        } else if (pfRet == -1) {
            problemCalcStatusDto.setStatus("-1");
        } else {
            problemCalcStatusDto.setStatus("0");
        }
        return problemCalcStatusDto;
    }

    /**
     * 分析电压越线情况
     * 检查所有节点的电压是否在正常范围内
     */
    public List<VoltageViolationDto> analyzeVoltageViolations(Long retId) {
        log.debug("开始电压越线分析, retId: {}", retId);
        List<VoltageViolationDto> violations = new ArrayList<>();
        Set<Long> processedDeviceIds = new HashSet<>();
        // 检查配网开关电压
        List<SimRetPfDmsBreaker> breakers = iSimulationService.selectSimRetPfDmsBreaker(retId, null);
        for (SimRetPfDmsBreaker breaker : breakers) {
            if (breaker.getVValue() != null && isVoltageViolation(breaker.getVValue()) && !processedDeviceIds.contains(breaker.getId())) {
                violations.add(createVoltageViolationFromBreaker(breaker));
                processedDeviceIds.add(breaker.getId());
            }
        }

        // 线路段
        List<SimRetPfDmsSegment> segments = simRetPfDmsSegmentMapper.findByRetIdAndRateValue(retId, LOAD_RATE_WARNING);
        for (SimRetPfDmsSegment segment : segments) {
            if (!processedDeviceIds.contains(segment.getId())) {
                violations.add(createVoltageViolationFromSegment(segment));
                processedDeviceIds.add(segment.getId());
            }
        }

        // 检查主网开关电压
        List<SimRetPfEmsBreaker> emsBreakers = iSimulationService.selectSimRetPfEmsBreaker(retId, null);
        for (SimRetPfEmsBreaker emsBreaker : emsBreakers) {
            if (emsBreaker.getVValue() != null && isVoltageViolation(emsBreaker.getVValue()) && !processedDeviceIds.contains(emsBreaker.getId())) {
                violations.add(createVoltageViolationFromEmsBreaker(emsBreaker));
                processedDeviceIds.add(emsBreaker.getId());
            }
        }

        return violations;
    }

    /**
     * 分析电流越线情况
     * 检查开关设备和线路段的电流负载情况
     */
    public List<CurrentViolationDto> analyzeCurrentViolations(Long retId) {
        log.debug("开始电流越线分析, retId: {}", retId);
        List<CurrentViolationDto> violations = new ArrayList<>();
        Set<Long> processedDeviceIds = new HashSet<>();
        // 根据设备类型选择性查询
        // 配网开关
        List<SimRetPfDmsBreaker> breakers = simRetPfDmsBreakerMapper.findByRetId(retId);
        for (SimRetPfDmsBreaker breaker : breakers) {
            if (breaker.getIValue() != null && breaker.getIValue() > CURRENT_DMS_BREAKER_WARNING && !processedDeviceIds.contains(breaker.getId())) {
                violations.add(createCurrentViolationFromBreaker(breaker));
                processedDeviceIds.add(breaker.getId());
            }
        }

        // 线路段
        List<SimRetPfDmsSegment> segments = iSimulationService.selectSimRetPfSegment(retId, null);
        for (SimRetPfDmsSegment segment : segments) {
            if (segment.getLoadRateValue() != null && segment.getLoadRateValue() > LOAD_RATE_WARNING && !processedDeviceIds.contains(segment.getId())) {
                violations.add(createCurrentViolationFromSegment(segment));
                processedDeviceIds.add(segment.getId());
            }
        }

        // 主网开关
        List<SimRetPfEmsBreaker> emsBreakers = iSimulationService.selectSimRetPfEmsBreaker(retId, null);
        for (SimRetPfEmsBreaker emsBreaker : emsBreakers) {
            if (emsBreaker.getIValue() != null && emsBreaker.getIValue() > CURRENT_EMS_BREAKER_WARNING && !processedDeviceIds.contains(emsBreaker.getId())) {
                violations.add(createCurrentViolationFromEmsBreaker(emsBreaker));
                processedDeviceIds.add(emsBreaker.getId());
            }
        }


        return violations;
    }


    /**
     * 分析配变超容情况
     * 检查配电变压器的负载情况
     */
    public List<TransformerOverloadDto> analyzeTransformerOverloads(Long retId) {
        log.debug("开始配变超容分析, retId: {}", retId);

        List<Map<String, Object>> transformers = simRetPfDmsDtMapper.findTransformersWithNames(retId);
        if (CollectionUtils.isEmpty(transformers)) {
            return Collections.emptyList();
        }

        return transformers.stream().map(this::createTransformerOverloadDto).filter(dto -> dto.getLoadRate() > TRANSFORMER_WARNING).collect(Collectors.toList());
    }

    /**
     * 计算变压器是否超容
     * @param vValue 电压值(KV)
     * @param iValue 电流值(A)
     * @return 超容比例(> 1表示超容)，如果数据无效返回-1
     */
    public double calculateOverload(double vValue, double iValue) {
        // 检查数据有效性
        if (vValue <= 0 || iValue <= 0) {
            return -1;
        }
        // 计算视在功率 S = V × I (单位VA)
        double apparentPower = vValue * iValue;
        // 计算负载率
        return apparentPower / TRANSFORMER_RATED_CAPACITY;
    }

    /**
     * 分析短路电流越线情况
     * 检查短路电流是否超过设备承受能力
     */
    public List<ShortCircuitAnalysisDto> analyzeShortCircuitViolations(Long retId) {
        log.debug("开始短路电流分析, retId: {}", retId);

        List<SimRetScNode> scNodes = simRetScNodeMapper.findByRetId(retId);
        if (CollectionUtils.isEmpty(scNodes)) {
            return Collections.emptyList();
        }

        return scNodes.stream().filter(node -> node.getIPeakKa() != null).map(this::createShortCircuitAnalysisDto).collect(Collectors.toList());
    }

    /**
     * 分析线路仿真情况
     * 提供线路段的详细运行参数
     */
    public List<LineSimulationDto> analyzeLineSimulations(Long retId) {
        log.debug("开始线路仿真分析, retId: {}", retId);
        List<SimRetPfDmsSegment> segments = iSimulationService.selectSimRetPfSegment(retId, null);
        if (CollectionUtils.isEmpty(segments)) {
            return Collections.emptyList();
        }

        return segments.stream().map(this::createLineSimulationDto).collect(Collectors.toList());
    }

    /**
     * 获取负载率分析
     * 统计各设备的负载率分布情况
     */
    public Map<String, Object> getLoadRateAnalysis(Long retId) {
        log.debug("开始负载率分析, retId: {}", retId);

        Map<String, Object> result = new HashMap<>();

        // 开关设备负载率统计
        List<SimRetPfDmsBreaker> breakers = simRetPfDmsBreakerMapper.findByRetId(retId);
        Map<String, Long> breakerLoadStats = categorizeLoadRates(breakers.stream().filter(b -> b.getLoadRateValue() != null).map(SimRetPfDmsBreaker::getLoadRateValue).collect(Collectors.toList()));

        // 线路段负载率统计
        List<SimRetPfDmsSegment> segments = simRetPfDmsSegmentMapper.findByRetId(retId);
        Map<String, Long> segmentLoadStats = categorizeLoadRates(segments.stream().filter(s -> s.getLoadRateValue() != null).map(SimRetPfDmsSegment::getLoadRateValue).collect(Collectors.toList()));

        result.put("breakerLoadStats", breakerLoadStats);
        result.put("segmentLoadStats", segmentLoadStats);
        result.put("totalDevices", breakers.size() + segments.size());

        return result;
    }


    /**
     * 判断电压是否越线
     */
    private boolean isVoltageViolation(Double voltage) {
        return voltage < VOLTAGE_LOWER_LIMIT || voltage > VOLTAGE_UPPER_LIMIT;
    }

    /**
     * 创建电压越线DTO
     */
    private VoltageViolationDto createVoltageViolationDto(SimRetPfNode node) {
        String violationType = node.getVValue() > VOLTAGE_UPPER_LIMIT ? "OVER_UPPER" : "UNDER_LOWER";
        double deviation = node.getVValue() > VOLTAGE_UPPER_LIMIT ? node.getVValue() - VOLTAGE_UPPER_LIMIT : VOLTAGE_LOWER_LIMIT - node.getVValue();

        return VoltageViolationDto.builder().deviceId(node.getId()).deviceName("节点-" + node.getId()).deviceType("NODE").voltage(node.getVValue()).upperLimit(VOLTAGE_UPPER_LIMIT).lowerLimit(VOLTAGE_LOWER_LIMIT).violationType(violationType).deviation(deviation).severity(calculateVoltageSeverity(deviation)).build().getTime(node.getIdx());
    }

    /**
     * 从开关设备创建电流越线DTO
     */
    private CurrentViolationDto createCurrentViolationFromBreaker(SimRetPfDmsBreaker breaker) {
        String[] idAndType = ZnapUtils.parsePsrStr(breaker.getPsrId());
        return CurrentViolationDto.builder().deviceId(breaker.getId()).deviceName("开关-" + breaker.getId()).
                deviceType(DeviceTypeEnum.DMS_BREAKER.getCode()).
                current(breaker.getIValue()).
                loadRate(breaker.getLoadRateValue()).
                violationType(breaker.getLoadRateValue() >= CURRENT_DMS_BREAKER_WARNING ? "CRITICAL" : "WARNING").
                severity(breaker.getLoadRateValue() >= CURRENT_DMS_BREAKER_WARNING ? 3 : 2).psrId(idAndType[1]).psrType(idAndType[0]).build();
    }

    /**
     * 从线路段创建电流越线DTO
     */
    private CurrentViolationDto createCurrentViolationFromSegment(SimRetPfDmsSegment segment) {
        return CurrentViolationDto.builder().
                deviceId(segment.getId()).deviceName("线路段-" + segment.getId()).
                deviceType(DeviceTypeEnum.SEGMENT.getCode()).
                current(segment.getIIndValue()).
                loadRate(segment.getLoadRateValue()).
                violationType(segment.getLoadRateValue() >= LOAD_RATE_CRITICAL ? "CRITICAL" : "WARNING").
                psrId(segment.getPsrId()).
                psrType(segment.getPsrType()).
                severity(segment.getLoadRateValue() >= LOAD_RATE_CRITICAL ? 3 : 2).build();
    }

    /**
     * 创建配变超容DTO
     */
    private TransformerOverloadDto createTransformerOverloadDto(Map<String, Object> transformer) {
        Double pValue = (Double) transformer.get("p_value");
        String deviceName = (String) transformer.get("device_name");
        Long deviceId = (Long) transformer.get("id");
        double iValue = (double) transformer.get("i_value");
        double vValue = (double) transformer.get("v_value");
        String psrId = (String) transformer.get("psrid");
        String psrType = (String) transformer.get("psr_type");
        if (!StringUtils.isEmpty(psrId)) {
            String[] idAndType = ZnapUtils.parsePsrStr(psrId);
            psrId = idAndType[1];
            psrType = idAndType[0];
        }

        double loadRate = calculateOverload(vValue, iValue);

        String overloadLevel = "NORMAL";
        int severity = 1;
        if (loadRate >= TRANSFORMER_CRITICAL) {
            overloadLevel = "CRITICAL";
            severity = 3;
        } else if (loadRate >= TRANSFORMER_WARNING) {
            overloadLevel = "WARNING";
            severity = 2;
        }

        return TransformerOverloadDto.builder().transformerId(deviceId).transformerName(deviceName != null ? deviceName : "配变-" + deviceId).loadRate(loadRate).capacity(TRANSFORMER_RATED_CAPACITY).actualLoad(pValue != null ? Math.abs(pValue) : 0.0).overloadLevel(overloadLevel).severity(severity).psrId(psrId).psrType(psrType).build();
    }

    /**
     * 创建短路电流分析DTO
     */
    private ShortCircuitAnalysisDto createShortCircuitAnalysisDto(SimRetScNode scNode) {
        // TODO 假设开关设备额定短路开断能力为31.5kA
        double breakingCapacity = 31.5;
        boolean isViolation = scNode.getIPeakKa() > breakingCapacity;

        return ShortCircuitAnalysisDto.builder().nodeId(scNode.getId()).nodeName("短路点-" + scNode.getId()).iInitKa(scNode.getIInitKa()).iPeakKa(scNode.getIPeakKa()).iThKa(scNode.getIThKa()).breakingCapacity(breakingCapacity).isViolation(isViolation).violationType(isViolation ? "EXCEEDS_CAPACITY" : "NORMAL").severity(isViolation ? 3 : 1).build();
    }

    /**
     * 创建线路仿真DTO
     */
    private LineSimulationDto createLineSimulationDto(SimRetPfDmsSegment segment) {
        String status = "NORMAL";
        if (segment.getLoadRateValue() != null) {
            if (segment.getLoadRateValue() >= LOAD_RATE_CRITICAL) {
                status = "CRITICAL";
            } else if (segment.getLoadRateValue() >= LOAD_RATE_WARNING) {
                status = "WARNING";
            }
        }

        return LineSimulationDto.builder().segmentId(segment.getId()).segmentName("线路段-" + segment.getId()).pIndValue(segment.getPIndValue()).qIndValue(segment.getQIndValue()).iIndValue(segment.getIIndValue()).vIndValue(segment.getVIndValue()).pJndValue(segment.getPJndValue()).qJndValue(segment.getQJndValue()).iJndValue(segment.getIJndValue()).vJndValue(segment.getVJndValue()).ploss(segment.getPlossValue()).qloss(segment.getQlossValue()).loadRate(segment.getLoadRateValue()).status(status).build();
    }


    /**
     * 计算电压严重程度
     */
    private Integer calculateVoltageSeverity(double deviation) {
        if (deviation > 1.0) return 3; // 严重
        if (deviation > 0.5) return 2; // 警告
        return 1; // 轻微
    }
    /*
    写一个策略模式，我要根据前端传递的设备类型psrType，psrId查询不同的表。展示每个设备的潮流曲线，或者你有更好的想法，可以用你的
     */

    /**
     * 负载率分类统计
     */
    private Map<String, Long> categorizeLoadRates(List<Double> loadRates) {
        Map<String, Long> stats = new HashMap<>();
        stats.put("normal", loadRates.stream().filter(rate -> rate < LOAD_RATE_WARNING).count());
        stats.put("warning", loadRates.stream().filter(rate -> rate >= LOAD_RATE_WARNING && rate < LOAD_RATE_CRITICAL).count());
        stats.put("critical", loadRates.stream().filter(rate -> rate >= LOAD_RATE_CRITICAL).count());
        return stats;
    }

    /**
     * 从配网开关创建电压越线DTO
     */
    private VoltageViolationDto createVoltageViolationFromBreaker(SimRetPfDmsBreaker breaker) {
        String violationType = breaker.getVValue() > VOLTAGE_UPPER_LIMIT ? "OVER_UPPER" : "UNDER_LOWER";
        double deviation = breaker.getVValue() > VOLTAGE_UPPER_LIMIT ? breaker.getVValue() - VOLTAGE_UPPER_LIMIT : VOLTAGE_LOWER_LIMIT - breaker.getVValue();

        return VoltageViolationDto.builder().deviceId(breaker.getId()).deviceName("配网开关-" + breaker.getId()).deviceType(DeviceTypeEnum.DMS_BREAKER.getCode()).voltage(breaker.getVValue()).upperLimit(VOLTAGE_UPPER_LIMIT).lowerLimit(VOLTAGE_LOWER_LIMIT).violationType(violationType).deviation(deviation).severity(calculateVoltageSeverity(deviation)).psrId(breaker.getPsrId()).psrType(breaker.getPsrType()).psrType(breaker.getPsrType()).build().getTime(breaker.getIdx());
    }

    /**
     * 从配网母线创建电压越线DTO
     */
    private VoltageViolationDto createVoltageViolationFromBusbar(SimRetPfDmsBusbar busbar) {
        String violationType = busbar.getVValue() > VOLTAGE_UPPER_LIMIT ? "OVER_UPPER" : "UNDER_LOWER";
        double deviation = busbar.getVValue() > VOLTAGE_UPPER_LIMIT ? busbar.getVValue() - VOLTAGE_UPPER_LIMIT : VOLTAGE_LOWER_LIMIT - busbar.getVValue();

        return VoltageViolationDto.builder().deviceId(busbar.getId()).deviceName("配网母线-" + busbar.getId()).deviceType(DeviceTypeEnum.DMS_BUSBAR.getCode()).voltage(busbar.getVValue()).upperLimit(VOLTAGE_UPPER_LIMIT).lowerLimit(VOLTAGE_LOWER_LIMIT).violationType(violationType).deviation(deviation).severity(calculateVoltageSeverity(deviation)).psrId(busbar.getPsrId()).psrType(busbar.getPsrType()).build().getTime(busbar.getIdx());
    }

    /**
     * 从配网线段创建电压越线DTO
     */
    private VoltageViolationDto createVoltageViolationFromSegment(SimRetPfDmsSegment segment) {
        String[] idAndType = ZnapUtils.parsePsrStr(segment.getPsrId());
        return VoltageViolationDto.builder().
                deviceId(segment.getId()).
                deviceName("线路段-" + segment.getId()).
                deviceType(DeviceTypeEnum.SEGMENT.getCode()).
                voltage(segment.getVIndValue()).
                violationType(segment.getLoadRateValue() >= LOAD_RATE_CRITICAL ? "CRITICAL" : "WARNING").
                psrId(idAndType[1]).
                psrType(idAndType[0]).
                severity(segment.getLoadRateValue() >= LOAD_RATE_CRITICAL ? 3 : 2).
                build();
    }

    /**
     * 从主网开关创建电压越线DTO
     */
    private VoltageViolationDto createVoltageViolationFromEmsBreaker(SimRetPfEmsBreaker emsBreaker) {
        String[] idAndType = ZnapUtils.parsePsrStr(emsBreaker.getPsrId());
        String violationType = emsBreaker.getVValue() > VOLTAGE_UPPER_LIMIT ? "OVER_UPPER" : "UNDER_LOWER";
        double deviation = emsBreaker.getVValue() > VOLTAGE_UPPER_LIMIT ? emsBreaker.getVValue() - VOLTAGE_UPPER_LIMIT : VOLTAGE_LOWER_LIMIT - emsBreaker.getVValue();

        return VoltageViolationDto.builder().
                deviceId(emsBreaker.getId()).
                deviceName("主网开关-" + emsBreaker.getId()).
                deviceType(DeviceTypeEnum.EMS_BREAKER.getCode()).
                voltage(emsBreaker.getVValue()).upperLimit(VOLTAGE_UPPER_LIMIT).
                lowerLimit(VOLTAGE_LOWER_LIMIT).violationType(violationType).
                deviation(deviation).severity(calculateVoltageSeverity(deviation)).
                psrId(emsBreaker.getPsrId()).
                psrType(emsBreaker.getPsrType()).
                build().
                getTime(emsBreaker.getIdx());
    }

    /**
     * 从主网开关创建电流越线DTO
     */
    private CurrentViolationDto createCurrentViolationFromEmsBreaker(SimRetPfEmsBreaker emsBreaker) {
        return CurrentViolationDto.builder().
                deviceId(emsBreaker.getId()).
                deviceName("主网开关-" + emsBreaker.getId()).
                deviceType(DeviceTypeEnum.EMS_BREAKER.getCode()).
                current(emsBreaker.getIValue()).
                loadRate(emsBreaker.getLoadRateValue()).
                violationType(emsBreaker.getLoadRateValue() >= CURRENT_EMS_BREAKER_WARNING ? "CRITICAL" : "WARNING").
                psrId(emsBreaker.getPsrId()).
                psrType(emsBreaker.getPsrType()).
                severity(emsBreaker.getLoadRateValue() >= CURRENT_EMS_BREAKER_WARNING ? 3 : 2).build();
    }
}