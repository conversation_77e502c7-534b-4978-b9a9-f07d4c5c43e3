package com.ruoyi.service.plan.model;

import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 新增联络线 以下如果条件不足 逐个往下走
 * 1、从杆塔出发新增联络线
 * 2、末端是有站房的设备 并且站房有多余的间隔 从备用间隔
 * 3、末端有站房如果站房站房当前使用的间隔低于6个 那么我们可以替换大点的间隔站房  否则 新增新的站房
 * 4、末端没有站房 新增新的环网柜
 */
@Data
public class ProcessContactBo {

    public ProcessContactBo() {
    }

    public ProcessContactBo(String type) {
        this.type = type;
    }

    String type;

    /**
     * 需要设置联络线的杆塔
     */
    List<Node> poles;

    /**
     * 放在的间隔节点
     */
    HwgBayBo hwgBay;

    /**
     * 打断导线段对象
     */
    SegBreakNodeBo segBreakNode;

    /**
     * 替换站房
     */
    ReplaceStationNodeBo replaceStationNode;

    /**
     * 关联LayNodeBo的ID
     */
    String layNodeId;

    /**
     * 放置处理后的结果 每个节点对于的联络对象集合
     */
    Map<String, List<ProcessContactVo>> nodeToContactMap;

    /**
     *  放置处理后的结果所有的联络对象
     */
    List<ProcessContactVo> processContacts;

    // 杆塔新增联络线
    public static String POLE_RANGE = "poleRange";

    // 末端是有站房 并且站房有多余的间隔 从备用间隔
    public static String STATION_BAY = "stationBay";

    // 新增新的环网柜
    public static String SEG_ADD_HWG = "setAddHwg";

    // 站房替换
    public static String HWG_REPLACE = "hwgReplace";


    public static ProcessContactBo createPoles(List<Node> poles) {
        ProcessContactBo processContactBo = new ProcessContactBo(POLE_RANGE);
        processContactBo.setPoles(poles);
        return processContactBo;
    }

    public static ProcessContactBo createHwgBay(HwgBayBo hwgBay) {
        ProcessContactBo processContactBo = new ProcessContactBo(STATION_BAY);
        processContactBo.setHwgBay(hwgBay);
        return processContactBo;
    }

    public static ProcessContactBo createSegBreakNode(SegBreakNodeBo segBreakNode) {
        ProcessContactBo processContactBo = new ProcessContactBo(SEG_ADD_HWG);
        processContactBo.setSegBreakNode(segBreakNode);
        return processContactBo;
    }

    public static ProcessContactBo createStationReplace(ReplaceStationNodeBo replaceStationNode) {
        ProcessContactBo processContactBo = new ProcessContactBo(HWG_REPLACE);
        processContactBo.setReplaceStationNode(replaceStationNode);
        return processContactBo;
    }

}
