package com.ruoyi.service.plan.model;

import com.ruoyi.entity.map.vo.NeedFeederVo;
import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

/**
 * 查找附近线路Bo
 */
@Data
public class NearFeedersBo {
    public NearFeedersBo() {
    }

    public NearFeedersBo(List<Node> nodes, List<List<NeedFeederVo>> coordsToNears, List<NeedFeederVo> nearFeeders) {
        this.nodes = nodes;
        this.coordsToNears = coordsToNears;
        this.nearFeeders = nearFeeders;
    }

    List<Node> nodes;

    /**
     * 每个坐标点集合队友附近线路集合
     */
    List<List<NeedFeederVo>> coordsToNears;

    // 所有的附近线路集合
    List<NeedFeederVo> nearFeeders;

    // 节点对应的附近线路集合
    HashMap<String, List<NeedFeederVo>> nodeToNears;
}
