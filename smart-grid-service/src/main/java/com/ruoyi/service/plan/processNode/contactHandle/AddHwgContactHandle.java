package com.ruoyi.service.plan.processNode.contactHandle;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.AddHwgContactBo;
import com.ruoyi.service.plan.model.NearFeedersBo;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 新增环网柜处理
 */
@Component
public class AddHwgContactHandle extends ContactProcess implements IContactHandle {

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Override
    public void handleContact(Long problemId, List<ProcessContactBo> processContacts, List<DeviceFeeder> feederList, String token) {

        // 获取所有打断节点新增环网柜对象

        List<AddHwgContactBo> addHwgList = new ArrayList<>();

        for (ProcessContactBo processContact : processContacts) {
            SegBreakNodeBo segBreakNode = processContact.getSegBreakNode();
            // 处理放置的中线坐标点
            segBreakNodePoint(segBreakNode);
            addHwgList.add(new AddHwgContactBo(segBreakNode, processContact));
        }

        // 过滤空坐标
        addHwgList = addHwgList.stream().filter(n -> n.getSegBreakNode().getPoint() != null).collect(Collectors.toList());

        List<double[]> coords = new ArrayList<>();
        List<Node> contactNodes = new ArrayList<>();
        HashMap<String, List<Node>> hwgNodesMap = new HashMap<>();

        for (AddHwgContactBo addHwg : addHwgList) {
            SegBreakNodeBo segBreakNode = addHwg.getSegBreakNode();
            // 环网柜节点结合集合
            List<Node> hwgNodes = breakSegAddHwg(segBreakNode);
            for (Node hwgNode : hwgNodes) {
                if (StringUtils.isBlank(hwgNode.getPsrId())) {
                    hwgNode.setPsrId(hwgNode.getId());
                }
            }

            List<Node> nodeNoUses = hwgNodes.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());
            Node noUseNode = nodeNoUses.get(0);
            // 设置需要新增联络线的坐标
            addHwg.setBayContactNode(noUseNode);
            addHwg.setBayContactPoint(noUseNode.getGeometry());

            coords.add(addHwg.toBayContactLngLat());
            contactNodes.add(noUseNode);
            hwgNodesMap.put(noUseNode.getPsrId(), hwgNodes);
        }

        // 当前坐标点附近线路
        NearFeedersBo nearFeeders = getNearFeeders(coords, contactNodes, feederList);

        // 每个设备都对应相关联的联络对象集合
        Map<String, List<ProcessContactVo>> nodeToContactFeederMap = processContactHandle(contactNodes, nearFeeders, ProcessContactBo.SEG_ADD_HWG, token);

        // 加装
        for (AddHwgContactBo addHwgContact : addHwgList) {
            ProcessContactBo processContact = addHwgContact.getProcessContact();
            Node bayContactNode = addHwgContact.getBayContactNode();

            List<Node> hwgNodes = hwgNodesMap.get(bayContactNode.getPsrId());
            List<ProcessContactVo> processContactVos = nodeToContactFeederMap.get(bayContactNode.getPsrId());

            // 加装环网柜节点集合
            if (CollectionUtils.isNotEmpty(hwgNodes)) {
                for (ProcessContactVo processContactVo : processContactVos) {
                    processContactVo.getContactNodeList().addAll(NodeUtils.copyNodes(hwgNodes));
                }
            }

            if (CollectionUtils.isNotEmpty(processContactVos)) {
                processContact.setProcessContacts(processContactVos);
            }
        }
    }

    /**
     * 杆塔获取联络线
     *
     * @param layNodeBo      插入位置实体类
     * @param processContact 加工后的联络节点
     * @param usedContact    已经使用的联络
     */
    @Override
    public ProcessContactVo getContact(LayNodeBo layNodeBo, ProcessContactBo processContact, HashMap<String, ProcessContactVo> usedContact, boolean isAddMainKg) {


        List<ProcessContactVo> processContacts = processContact.getProcessContacts();

        if (CollectionUtils.isEmpty(processContacts)) {
            return null;
        }

        List<ProcessContactVo> allContacts = new ArrayList<>();

        ProcessContactVo result = null;

        // 过滤已经最终使用的
        List<ProcessContactVo> tmpPContacts = processContacts.stream().filter(n -> !usedContact.containsKey(n.getEndPsrId())).collect(Collectors.toList());

        // 生成联络路径  我们这里临时取第0条
        if (tmpPContacts.isEmpty()) {
            result = processContacts.get(0).clone();
        } else {
            allContacts.add(tmpPContacts.get(0).clone());
            result = tmpPContacts.get(0);
        }

        if (result != null) {
            String gtId = result.getEndPsrId();
            if (!usedContact.containsKey(gtId)) {
                usedContact.put(gtId, result);
            }
        }

        return result;
    }
}
