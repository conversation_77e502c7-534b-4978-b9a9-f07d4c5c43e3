package com.ruoyi.service.plan.generatePlan;

import com.alibaba.fastjson.JSON;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.bo.PsrIdAndPsrType;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.device.vo.RunTowerPosition;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.vo.BranchMainPathBo;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.bo.NodeNumModelBo;
import com.ruoyi.graph.utils.CartesianProduct;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.device.DeviceRunTowerMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.plan.ToNodeNumsFunc;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BaseGeneratePlan {

    @Autowired
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    GeometryFactory geometryFactory = new GeometryFactory();

    // TODO 会有重复问题   后面看看你要怎么弄暂时先这样
    private Long randomPlanId() {
        UUID uuid = UUID.randomUUID();
        // 获取UUID的哈希码（转为正数）
        int hashCode = Math.abs(uuid.hashCode());
        // 映射到7位数范围（1000000-9999999）
        return 10000000L + (hashCode % 90000000);
    }

    /**
     * 获取过滤空的lay数据
     */
    public List<List<ArrayList<LayNodeBo>>> getFilterEmptyLayNodes(List<List<ArrayList<LayNodeBo>>> layPositionList) {
        List<List<ArrayList<LayNodeBo>>> result = new ArrayList<>();
        for (List<ArrayList<LayNodeBo>> arrayLists : layPositionList) {
            List<ArrayList<LayNodeBo>> oneResult = new ArrayList<>();
            for (ArrayList<LayNodeBo> arrayList : arrayLists) {
                ArrayList<LayNodeBo> filters = new ArrayList<>(arrayList.stream().filter(LayNodeBo::isNotEmpty).collect(Collectors.toList()));
                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(filters)) {
                    oneResult.add(filters);
                }
            }
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(oneResult)) {
                result.add(oneResult);
            }
        }
        return result;
    }

    // =========================== layNode相关 =======================

    /**
     * 所有可生成的方案组合化
     */
    public List<List<ArrayList<LayNodeBo>>> getCombLayPos(List<List<ArrayList<LayNodeBo>>> layPositionList) {
        // 把主干路就的方案和其他分支点路径合成一个方案
        List<List<ArrayList<LayNodeBo>>> list = layPositionList.stream().filter(ns -> !ns.isEmpty()).collect(Collectors.toList());
        return CartesianProduct.cartesianProduct(list);
    }

    /**
     * 位置集合放置数据扁平化
     */
    public List<ArrayList<LayNodeBo>> flatLayNode(List<List<ArrayList<LayNodeBo>>> combLayPos) {
        List<ArrayList<LayNodeBo>> sureLays = new ArrayList<>();
        for (List<ArrayList<LayNodeBo>> combs : combLayPos) {
            ArrayList<LayNodeBo> list = new ArrayList<>();
            for (ArrayList<LayNodeBo> combLayPo : combs) {
                list.addAll(combLayPo);
            }
            sureLays.add(list);
        }
        return sureLays;
    }

    /**
     * 根据放置的组合进行排序
     */
    public void combLayPosSort(List<List<ArrayList<LayNodeBo>>> combLayPos) {
        // 排序 TODO 先进行新增设备数和联络长度排序 后续我们在通过其他的指标
        for (List<ArrayList<LayNodeBo>> arrayLists : combLayPos) {
            arrayLists.sort((arr1, arr2) -> {
                HashMap<String, Double> totalScope1 = getLayTotalScope(arr1);
                HashMap<String, Double> totalScope2 = getLayTotalScope(arr2);
                Double total1 = totalScope1.get("total");
                Double total2 = totalScope2.get("total");
                if (total1 == total2) {
                    return totalScope1.get("contactLength") > totalScope2.get("contactLength") ? 1 : -1;
                } else {
                    return total1 > total2 ? -1 : 1;
                }
            });
        }
    }

    private HashMap<String, Double> getLayTotalScope(ArrayList<LayNodeBo> layNodeBos) {
        double total = 0;
        double contactLength = 0;
        for (LayNodeBo layNodeBo : layNodeBos) {
            total += layNodeBo.getScope();
            contactLength += layNodeBo.getContactLength();
        }
        double finalTotal = total;
        double finalContactLength = contactLength;
        return new HashMap<String, Double>() {{
            put("total", finalTotal);
            put("contactLength", finalContactLength);
        }};
    }

    // =========================== 方案生成 =======================

    /**
     * 将放置组合转为方案
     */
    public List<Plan> layToPlans(List<ArrayList<LayNodeBo>> sureLays, Long problemId, HashMap<Long, ArrayList<LayNodeBo>> planLaysMap) {

        // 生成方案
        return sureLays.stream().map(lays -> {
            // 将各个放置节点拼接成方案参数数据
            String operateDataStr = toOperateDataStr(lays);

            Plan plan = new Plan(randomPlanId(), operateDataStr, problemId);
            plan.setPlanType("addContrast");
            if (planLaysMap != null) {
                planLaysMap.put(plan.getId(), lays);
            }

            return plan;
        }).collect(Collectors.toList());
    }

    /**
     * 运放调整专供 开关操作转为方案
     */
    public List<Plan> transferCapToPlans(List<FeederTransferCap> runAdjustList, Long problemId, Map<String, Node> nodeMap) {

        // 生成方案
        return runAdjustList.stream().map(trf -> {
            Node fenNode = nodeMap.get(trf.getFenPsrId()).clone();
            fenNode.putProperties("switchOpen", true);
            fenNode.putProperties("psrName", fenNode.getPsrName());
            Node heNode = nodeMap.get(trf.getHePsrId()).clone();
            heNode.putProperties("switchOpen", false);
            heNode.putProperties("psrName", heNode.getPsrName());

            // 将各个放置节点拼接成方案参数数据
            String operateDataStr = NodeUtils.toPlanOperateDataStr(Arrays.asList(fenNode, heNode));
            return new Plan(randomPlanId(), operateDataStr, problemId);
        }).collect(Collectors.toList());
    }

    /**
     * 转成方案操作数据节点
     */
    public String toOperateDataStr(List<LayNodeBo> sureLays) {
        ArrayList<Node> allNodes = new ArrayList<>();
        // 将所有的放置点数据拼接在一起
        for (LayNodeBo sureLay : sureLays) {
            allNodes.addAll(sureLay.getAllNode());
        }
        return NodeUtils.toPlanOperateDataStr(allNodes);
    }

    // =========================== 基本运行数据 =======================

    // 加装放置位置的节点坐标
    public void layPositionCoords(List<List<ArrayList<LayNodeBo>>> layPositionList) {
        ArrayList<Node> poles = new ArrayList<>();
        HashMap<String, Node> poleMap = new HashMap<>();

        for (List<ArrayList<LayNodeBo>> arrayLists : layPositionList) {
            for (ArrayList<LayNodeBo> arrayList : arrayLists) {
                for (LayNodeBo layNodeBo : arrayList) {
                    for (Node node : layNodeBo.getRange()) {
                        if (node.isPole()) {
                            if (node.getPsrId() != null && !poleMap.containsKey(node.getPsrId())) {
                                poles.add(node);
                                poleMap.put(node.getPsrId(), node);
                            }
                        }
                    }
                }
            }
        }

        // 杆塔节点本事没有坐标
        factoryPoleNodes(poles, poleMap);
    }

    /**
     * 杆塔节点本事没有坐标  这里去加工杆塔节点的坐标
     *
     * @param poles   杆塔
     * @param nodeMap 节点对象
     */
    private void factoryPoleNodes(List<Node> poles, Map<String, Node> nodeMap) {
        // 查询杆塔数据
        List<String> polePsrIds = poles.stream().map(Node::getPsrId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(polePsrIds)) {
            return;
        }
        List<RunTowerPosition> deviceRunTowers = deviceRunTowerMapper.selectPosition(polePsrIds);

        for (RunTowerPosition deviceRunTower : deviceRunTowers) {
            Node node = nodeMap.get(deviceRunTower.getPsrId());
            String geoPosition = deviceRunTower.getGeoPositon();
            if (node != null && geoPosition != null) {
                String[] split = geoPosition.split(",");
                Coordinate coordinate = new Coordinate(Double.valueOf(split[0]), Double.valueOf(split[1]));
                node.setGeometry(geometryFactory.createPoint(coordinate));
            }
        }

    }

    /**
     * 获取当前联络线相关的运行值
     */
    public List<FeederNtVo> getContactFeederNts(ZnapTopology topologyMap) {
        List<String> feederIds = topologyMap.getContactFeederIds();

        return feederDeviceMapper.selectFeederNtsByFeederIds(feederIds);
    }

    /**
     * 将所有的配变加工容量
     *
     * @param nodes
     */
    public void processPbNodeCap(List<Node> nodes) {
        List<Node> pbNodes = nodes.stream().filter(Node::isPb).collect(Collectors.toList());

        //计算每个分支的容量
        List<PsrIdAndPsrType> psrIdAndPsrTypes = pbNodes.stream().map(node -> new PsrIdAndPsrType(node.getPsrId(), node.getPsrType())).collect(Collectors.toList());
        List<Double> caps = queryDeviceInfo.selectDeviceRatedCapacity(psrIdAndPsrTypes);

        // 加工容量
        for (int i = 0; i < caps.size(); i++) {
            pbNodes.get(i).setCap(caps.get(i));
        }
    }

    /**
     * 获取当前联络线最大负载率
     */
    public Double getFeederMaxLoad(String feederId) {
        return feederDeviceMapper.selectLoad(feederId);
    }

    /**
     * 方案新增联络线会导致负载率降低  具体怎么计算负载率的更改这里
     * 一、分段内上联络线：
     *  1、所处的分段内都被转走
     *  2、如果上联络线和开关 那么当前上的开关认为分闸状态 去判断
     * 二、分支上联络线：整个分支和分支首开关都会被转走
     * 三、运放调整 可以直接判断
     * 。。。。。其它情况后续再说
     */
}
