package com.ruoyi.service.plan.processNode.contactHandle;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.NearFeedersBo;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 杆塔联络线的处理
 */
@Component
public class PoleContactHandle extends ContactProcess implements IContactHandle {

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Override
    public void handleContact(Long problemId, List<ProcessContactBo> processContacts, List<DeviceFeeder> feederList, String token) {

        List<Node> poleList = new ArrayList<>();
        // 获取所以的杆塔
        for (ProcessContactBo processContact : processContacts) {
            poleList.addAll(processContact.getPoles());
        }

        // 去重
        poleList = NodeUtils.duplicateNodes(poleList);

        List<double[]> coords = poleList.stream().map(Node::toDeviceCoords).collect(Collectors.toList());

        // 当前坐标点附近线路
        NearFeedersBo nearFeeders = getNearFeeders(coords, poleList, feederList);

        // 每个设备都对应相关联的联络对象集合
        Map<String, List<ProcessContactVo>> nodeToContactFeederMap = processContactHandle(poleList, nearFeeders, ProcessContactBo.POLE_RANGE, token);

        // 加装
        for (ProcessContactBo processContact : processContacts) {
            Map<String, List<ProcessContactVo>> nodeToContactMap = new HashMap<>();

            List<Node> poles = processContact.getPoles();
            for (Node pole : poles) {
                List<ProcessContactVo> processContactVos = nodeToContactFeederMap.get(pole.getPsrId());
                if (CollectionUtils.isNotEmpty(processContactVos)) {
                    nodeToContactMap.put(pole.getPsrId(), processContactVos);
                }
                processContact.setNodeToContactMap(nodeToContactMap);
            }
        }


    }

    /**
     * 杆塔获取联络线
     *
     * @param layNodeBo      插入位置实体类
     * @param processContact 加工后的联络节点
     * @param usedContact    已经使用的联络
     */
    @Override
    public ProcessContactVo getContact(LayNodeBo layNodeBo, ProcessContactBo processContact, HashMap<String, ProcessContactVo> usedContact, boolean isAddMainKg) {

        List<Node> range = layNodeBo.getRange();
        Map<String, List<ProcessContactVo>> nodeToContactMap = processContact.getNodeToContactMap();

        // 如果新增联络开关的时候又要新增主干开关   如果当前范围内已经有开关  那么我们就在开关后面新增联络先
        if (isAddMainKg) {
            ArrayList<Node> tmpRange = new ArrayList<>(range);
            OptionalInt first = IntStream.range(0, tmpRange.size())
                    .filter(i -> tmpRange.get(i).isKg("all"))
                    .findFirst();
            int index = first.orElse(-1);
            if (index > -1) {
                range = range.subList(0, index);
            }
        }

        // 当前范围之前的杆塔
        List<Node> poles = range.stream().filter(Node::isPole).collect(Collectors.toList());
        // 当前范围内所有杆塔对应的联络线

        List<ProcessContactVo> allContacts = new ArrayList<>();

        for (int i = 0; i < poles.size(); i++) {
            Node pole = poles.get(i);
            // 单个杆塔对应多个 TODO 后面我们要排序已经过滤
            List<ProcessContactVo> processContacts = nodeToContactMap.get(pole.getPsrId());

            if (CollectionUtils.isEmpty(processContacts)) {
                continue;
            }

            // 过滤已经最终使用的
            List<ProcessContactVo> tmpPContacts = processContacts.stream().filter(n -> !usedContact.containsKey(n.getEndPsrId())).collect(Collectors.toList());
            if (tmpPContacts.isEmpty()) {
                allContacts.add(processContacts.get(0).clone());
            } else {
                allContacts.add(tmpPContacts.get(0).clone());
            }
        }

        // 生成联络路径  所有的杆塔产生的联络线 我们这里临时取第0条
        ProcessContactVo result = allContacts.get(0);
        if (result != null) {
            String gtId = result.getEndPsrId();
            if (!usedContact.containsKey(gtId)) {
                usedContact.put(gtId, result);
            }
        }

        return result;
    }
}
