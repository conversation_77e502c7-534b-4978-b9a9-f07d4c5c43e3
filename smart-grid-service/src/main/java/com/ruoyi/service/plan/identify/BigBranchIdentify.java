package com.ruoyi.service.plan.identify;

import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.plan.IConditionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class BigBranchIdentify {

    @Autowired
    IConditionService iConditionService;

    /**
     * 限制的配变数量
     */
    int minPbNum = -1;

    /**
     * 限制的容量大小
     */
    double minCap = -1;

    /**
     * 获取最小大分子配变数量
     */
    public int getMinPbNum() {
        if (minPbNum == -1) {
            minPbNum = iConditionService.bigBranchSegmentation();
        }
        return minPbNum;
    }

    /**
     * 获取最小大分支配变容量总和
     */
    public double getMinCap() {
        if (minCap == -1) {
            minCap = iConditionService.bigBranchCapacityNum();
        }
        return minCap;  // iConditionService.bigBranchCapacityNum(); // 1600
    }

    public boolean isExceed(int pbNodeNum, double pbNodeCap) {
        return pbNodeNum > getMinPbNum() || pbNodeCap > getMinCap();
    }

    /**
     * 识别获取出问题的大分支节点
     * @param nodePath
     * @return
     */
    public List<BranchNode> getProblemBranchList(NodePath nodePath) {
        ArrayList<BranchNode> result = new ArrayList<>();
        HashMap<String, BranchNode> branchNodeMap = nodePath.getBranchNodeMap();
        // 遍历各个分支节点  如果有满足大分子  那就新增联络线
        for (BranchNode branchNode : branchNodeMap.values()) {
            if (branchNode.isMain()) {
                List<Node> pbNodes = branchNode.getPbNodes();
                // 判断配变数量和总体容量大小 满足大分支 则需要添加联络线专供出去
                double totalCap = pbNodes.stream().mapToDouble(Node::getCap).sum();
                if (isExceed(pbNodes.size(), totalCap)) {
                    result.add(branchNode);
                }
            }
        }
        return result;
    }
}
