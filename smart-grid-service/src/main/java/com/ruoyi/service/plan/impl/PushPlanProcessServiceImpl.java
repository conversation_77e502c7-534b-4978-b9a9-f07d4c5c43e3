package com.ruoyi.service.plan.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.constant.VoltageEnum;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceFeederTransformerVol;
import com.ruoyi.entity.device.DeviceSubstation;
import com.ruoyi.entity.device.PublicSpecializedTransformer;
import com.ruoyi.entity.device.bo.PsrIdAndPsrType;
import com.ruoyi.entity.map.vo.NeedFeederVo;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.enuw.SupplyAreaEnum;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.mapper.device.DeviceFeederTransformerVolMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.plan.PlanProblemDescribeMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.problem.IProblemService;
import com.ruoyi.util.PlanProcessUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 推送处理方案
 */
@Service
@Slf4j
public class PushPlanProcessServiceImpl {

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    PlanProblemDescribeMapper planProblemDescribeMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    DeviceFeederTransformerVolMapper deviceFeederTransformerVolMapper;
    @Autowired
    IProblemService iProblemService;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    /**
     * 基础信息推送
     */
    public void pushInfo(Long problemId, DeviceFeeder feeder, List<Node> allPb) {
        try {
            HashMap<String, String> data = new HashMap<>();
            DeviceSubstation bdz = planProblemDescribeMapper.SubstationName(feeder.getStartStation());
            //枚举获取对应的区域
            String supplyArea = SupplyAreaEnum.fromCode(feeder.getSupplyArea()).getGrade();
            //查询load
            Double load = feederDeviceMapper.selectLoad(feeder.getPsrId());
            //将load转成double
            Double doubleLoad = DoubleFormatter.formatToThreeDecimals3(load * 100);

            data.put("feederName", feeder.getName());
            data.put("substationName", bdz.getName());
            data.put("substationId", bdz.getPsrId());
            data.put("supplyArea", supplyArea);
            data.put("feederLength", String.valueOf(feeder.getLength()));
            data.put("voltageLevel", VoltageEnum.getByValue(feeder.getVoltageLevel())
                    .map(VoltageEnum::getLabel)  // 获取label
                    .orElse("未知电压"));
            data.put("instCap", feeder.getFeederRateCapacity().toString() + "kVA");
            data.put("load", doubleLoad + "%");
            data.put("supplyRadius", String.valueOf(feeder.getSupplyRadius()));
            data.put("pbNum", String.valueOf(allPb.size()));
            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.ANALYSIS_MODULE, "基本信息", null, "baseDesc", data);
        } catch (Exception e) {
            log.error("推送基本信息失败", e);
        }
    }

    /**
     * 推送陪伴
     */
    public void pushPbList(Long problemId, List<Node> allPb) {
        try {
            List<PsrIdAndPsrType> psrIdAndPsrTypes = allPb.stream().map(value -> new PsrIdAndPsrType(value.getPsrId(), value.getPsrType())).collect(Collectors.toList());
            //查询所有配变
            List<PublicSpecializedTransformer> vols = queryDeviceInfo.selectDevice(psrIdAndPsrTypes);
            ArrayList<Map<String, Object>> tableData = new ArrayList<>();

            for (PublicSpecializedTransformer vol : vols) {
                tableData.add(new HashMap<String, Object>() {{
                    put("devId", vol.getPsrId());
                    put("devType", vol.getPsrType());
                    put("devName", vol.getName());
                    put("vlevelName", VoltageEnum.getByValue(vol.getVoltageLevel())
                            .map(VoltageEnum::getLabel)  // 获取label
                            .orElse("未知电压"));// 电压等级
                    put("cap", vol.getRatedCapacity());// 容量
                    put("coordinate", vol.getCoords());
                }});
            }

            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.ANALYSIS_MODULE, "配变列表", null, "pbTable", tableData);
        } catch (Exception e) {
            log.error("推送配变列表失败", e);
        }
    }

    // 问题解义
    public void pushExplain(Long problemId, int maxNum) {
        // 未根据用户数量、通道环境及架空线路长度合理设置分段开关，导致分段内接入配变过多（原则上不超过10户，含用户专变）
        try {
            planProcessService.pushStrStu(problemId, ProblemSchemeAnalysis.ANALYSIS_MODULE, "问题释义", null, "未根据用户数量、通道环境及架空线路长度合理设置分段开关，导致分段内接入配变过多（原则上不超过", PlanProcessUtils.toStrikingStu(String.valueOf(maxNum)), "户，含用户专变）");
        } catch (Exception e) {
            log.error("推送问题解义失败", e);
        }
    }

    /**
     * 大分子问题解义
     */
    public void pushBigExplain(Long problemId, int minPbNum, double minCap) {
        // 大分支无联络，支线装接配变数量≥10台(含用户专变)或装机容量≥3000kVA或最大负荷≥2MW。
        try {
            planProcessService.pushStrStu(problemId, ProblemSchemeAnalysis.ANALYSIS_MODULE, "问题释义", null, "分支无联络，支线装接配变数量≥", PlanProcessUtils.toStrikingStu(String.valueOf(minPbNum)), "台(含用户专变)或装机容量≥", PlanProcessUtils.toStrikingStu(String.valueOf(minCap)), "kVA");
        } catch (Exception e) {
            log.error("推送问题解义失败", e);
        }
    }

    /**
     * 单辐射无联络问题释义
     */
    public void pushSingleRadiationExplain(Long problemId) {
        try {
            planProcessService.pushStrStu(problemId, ProblemSchemeAnalysis.ANALYSIS_MODULE, "问题释义", null, "线路无联络，检修或故障时停电时户数过多。");
        } catch (Exception e) {
            log.error("推送问题解义失败", e);
        }
    }

    /**
     * 分段识别
     * 别出当前分段的主干路径为？ 当前分段开关？
     */
    public void pushSegIdentify(Long problemId, SegBetween segBetween) {
        try {
            // 别出当前分段的主干路径为？ 当前分段主干路径开关？
            ArrayList<Object> list = new ArrayList<>();
            // 起始分段开关？
            List<Object> item1 = PlanProcessUtils.toList("起始分段：",
                    PlanProcessUtils.toTextBtnStu(segBetween.getStartPsrId(), segBetween.getStartPsrType(), segBetween.getStartPsrName(),
                            PlanProcessUtils.SINGMAP_POPUP, "起始分段：【" + segBetween.getStartPsrName() + "】开关"),
                    "开关。", "至分段终点：",
                    PlanProcessUtils.toTextBtnStu(segBetween.getEndPsrId(), segBetween.getEndPsrType(), segBetween.getEndPsrName(), PlanProcessUtils.SINGMAP_POPUP, "分段终点：【" + segBetween.getEndPsrName() + "】开关"));

            // 导线段集合
            List<Node> segNodes = segBetween.getNodes();
            segNodes = segNodes.stream().filter(Node::isSegFeeder).collect(Collectors.toList());
            List<HashMap<String, String>> segList = segNodes.stream().map(n -> new HashMap<String, String>() {{
                put("devId", n.getPsrId());
                put("devType", n.getPsrType());
            }}).collect(Collectors.toList());
            HashMap<String, Object> segData = new HashMap<String, Object>() {{
                put("name", "分段主干路径");
                put("list", segList);
            }};

            List<Object> item2 = PlanProcessUtils.toList("分段途径：", PlanProcessUtils.toStu(PlanProcessUtils.TEXT_BTN, segData, PlanProcessUtils.SINGMAP_SEG_HIGHLIGHT, segList));

            list.add(item1);
            list.add(item2);

            HashMap<String, Object> dataMap = PlanProcessUtils.getComponent("list", list);
            ProblemSchemeAnalysis problemSchemeAnalysis = new ProblemSchemeAnalysis(problemId, ProblemSchemeAnalysis.ANALYSIS_MODULE, ProblemSchemeAnalysis.COMPONENT, ProblemSchemeAnalysis.SEG_IDENTIFY_OP_TYPE, JSON.toJSONString(dataMap), "分段识别");

            // 步入渲染
//            HashMap<Object, Object> renderMap = new HashMap<>();
//            ArrayList<Object> dataList = new ArrayList<>();
//            dataList.add(new HashMap<String, String>() {{
//                put("label", "起始分段开关" + segBetween.getStartPsrId());
//                put("psrId", segBetween.getStartPsrId());
//                put("psrType", segBetween.getStartPsrType());
//                put("type", PlanProcessUtils.SINGMAP_POPUP);
//            }});
//            dataList.add(new HashMap<String, String>() {{
//                put("label", "末尾分段开关" + segBetween.getEndPsrId());
//                put("psrId", segBetween.getEndPsrId());
//                put("psrType", segBetween.getEndPsrType());
//                put("type", PlanProcessUtils.SINGMAP_POPUP);
//            }});
//            dataList.add(new HashMap<String, Object>() {{
//                put("label", "分段主干路径");
//                put("list", segList);
//                put("type", PlanProcessUtils.SINGMAP_SEG_HIGHLIGHT);
//            }});
//            renderMap.put("type", "segIdentify");
//            renderMap.put("data", dataList);
//
//            problemSchemeAnalysis.setStepInRender(JSON.toJSONString(renderMap));

            planProcessService.pushProcess(problemSchemeAnalysis);
        } catch (Exception e) {
            log.error("推送分段识别异常", e);
        }
    }

    /**
     * 大分子识别
     */
    public void pushBigBranchIdentify(Long problemId, BranchNode branchNode) {
        try {
            ArrayList<Object> list = new ArrayList<>();

            Node startNode = branchNode.getStartNode();


            List<Object> item1 = PlanProcessUtils.toList("大分子线路起始点：",
                    PlanProcessUtils.toTextBtnStu(startNode.getPsrId(), startNode.getPsrType(), startNode.getPsrName(),
                            PlanProcessUtils.SINGMAP_POPUP, "大分子线路起始点：" + startNode.getPsrName()));

            // 导线段集合

            // 大分子路径
            List<HashMap<String, String>> branchList = branchNode.getBranchPath().stream().map(n -> new HashMap<String, String>() {{
                put("devId", n.getPsrId());
                put("devType", n.getPsrType());
            }}).collect(Collectors.toList());

            // 大分子路径
            HashMap<String, Object> segData = new HashMap<String, Object>() {{
                put("name", "大分支路径");
                put("list", branchList);
            }};

            List<Object> item2 = PlanProcessUtils.toList("大分子路径：", PlanProcessUtils.toStu(PlanProcessUtils.TEXT_BTN, segData, PlanProcessUtils.SINGMAP_SEG_HIGHLIGHT, branchList));

            list.add(item1);
            list.add(item2);

            HashMap<String, Object> dataMap = PlanProcessUtils.getComponent("list", list);
            ProblemSchemeAnalysis problemSchemeAnalysis = new ProblemSchemeAnalysis(problemId, ProblemSchemeAnalysis.ANALYSIS_MODULE, ProblemSchemeAnalysis.COMPONENT, ProblemSchemeAnalysis.BIG_BRANCH_IDENTIFY_OP_TYPE, JSON.toJSONString(dataMap), "大分支识别");

            // 步入渲染
//            HashMap<Object, Object> renderMap = new HashMap<>();
//            ArrayList<Object> dataList = new ArrayList<>();
//            dataList.add(new HashMap<String, String>() {{
//                put("label", "起始分段开关" + segBetween.getStartPsrId());
//                put("psrId", segBetween.getStartPsrId());
//                put("psrType", segBetween.getStartPsrType());
//                put("type", PlanProcessUtils.SINGMAP_POPUP);
//            }});
//            dataList.add(new HashMap<String, String>() {{
//                put("label", "末尾分段开关" + segBetween.getEndPsrId());
//                put("psrId", segBetween.getEndPsrId());
//                put("psrType", segBetween.getEndPsrType());
//                put("type", PlanProcessUtils.SINGMAP_POPUP);
//            }});
//            dataList.add(new HashMap<String, Object>() {{
//                put("label", "分段主干路径");
//                put("list", segList);
//                put("type", PlanProcessUtils.SINGMAP_SEG_HIGHLIGHT);
//            }});
//            renderMap.put("type", "segIdentify");
//            renderMap.put("data", dataList);
//
//            problemSchemeAnalysis.setStepInRender(JSON.toJSONString(renderMap));

            planProcessService.pushProcess(problemSchemeAnalysis);
        } catch (Exception e) {
            log.error("推送大分支识别异常", e);
        }
    }

    /**
     * 附近相关联问题
     */
    public void pushNearProblem(Long problemId) {
        try {
            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.NEAR_PROBLEM_MODULE,
                    "附近相关联问题", null, "nearTable",
                    iProblemService.byDevices(problemId, 2000));
        } catch (Exception e) {
            log.error("推送附近相关联问题异常", e);
        }
    }

    /**
     * 合并策略预结果
     */
    public void pushMergeSolve(Long problemId) {
        try {
            ArrayList<Map<String, Object>> tableData = new ArrayList<>();
            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.NEAR_PROBLEM_MODULE, "合并策略预结果", null, "mergeSolveTable", tableData);
        } catch (Exception e) {
            log.error("推送附近相关联问题异常", e);
        }
    }

    /**
     * 预方案生成
     */
    public void pushPlans(Long problemId, List<Plan> plans, List<ArrayList<LayNodeBo>> sureLays) {
        // 优先推送将所有的方案给推送出去
        ProblemSchemeAnalysis planProcess = new ProblemSchemeAnalysis(problemId, null, ProblemSchemeAnalysis.PLANS, null, JSON.toJSONString(plans));
        planProcessService.pushProcess(planProcess);

        planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.ADVANCE_PLAN_MODULE, null, null, "titleBox", new HashMap<String, String>() {{
            put("title", "预方案列表");
        }});

        try {
            for (int i = 0; i < plans.size(); i++) {
                Plan plan = plans.get(i);
                ArrayList<LayNodeBo> layNodeBos = sureLays.get(i);
                // 拼接语句
                ArrayList<Object> stuList = new ArrayList<>();
                for (LayNodeBo layNodeBo : layNodeBos) {
                    List<Object> stuStr = layNodeBo.toStuStr();
                    if (stuStr != null) {
                        stuList.addAll(stuStr);
                    }
                }

                ProblemSchemeAnalysis process = new ProblemSchemeAnalysis(problemId, ProblemSchemeAnalysis.ADVANCE_PLAN_MODULE, ProblemSchemeAnalysis.STRUCTURED, ProblemSchemeAnalysis.PLAN_OP_TYPE, JSON.toJSONString(stuList));
                process.setBtnTitle("方案" + (i + 1));
                process.setPlanId(plan.getId());

//                HashMap<Object, Object> renderMap = new HashMap<>();
//                renderMap.put("type", "plan");
//                renderMap.put("planId", plan.getId());
//                process.setStepInRender(JSON.toJSONString(renderMap));
                process.setStepOutDestroy(true);

                planProcessService.pushProcess(process);
            }
        } catch (Exception e) {
            log.error("推送预方案生成异常", e);
        }
    }

    /**
     * 推送经济维度分析
     */
    public void pushBudgetDim(Long problemId) {
        try {
            ArrayList<Map<String, Object>> tableData = new ArrayList<>();
            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.COMPARE_PLAN_MODULE, "经济维度分析", null, "budgetDimTable", tableData);
        } catch (Exception e) {
            log.error("推送经济维度分析失败", e);
        }
    }

    /**
     * 推送施工与周期维度
     */
    public void pushConstrCycleDim(Long problemId) {
        try {
            ArrayList<Map<String, Object>> tableData = new ArrayList<>();
            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.COMPARE_PLAN_MODULE, "施工与周期维度", null, "constrCycleDimTable", tableData);
        } catch (Exception e) {
            log.error("推送施工与周期维度失败", e);
        }
    }

    /**
     * 约束条件匹配性
     */
    public void pushConstraintMatchDim(Long problemId) {
        try {
            ArrayList<Map<String, Object>> tableData = new ArrayList<>();
            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.COMPARE_PLAN_MODULE, "约束条件匹配性", null, "constraintMatchDimTable", tableData);
        } catch (Exception e) {
            log.error("推送约束条件匹配性失败", e);
        }
    }

    /**
     * 综合推荐方案
     */
    public void pushRecommendPlans(Long problemId, List<Plan> plans, HashMap<Long, ArrayList<LayNodeBo>> planLaysMap) {
        try {
            ArrayList<Object> list = new ArrayList<>();

            for (int i = 0; i < plans.size(); i++) {
                Plan plan = plans.get(i);

                ArrayList<LayNodeBo> layNodeBos = planLaysMap.get(plan.getId());

                ArrayList<Object> stuList = new ArrayList<>();
                for (LayNodeBo layNodeBo : layNodeBos) {
                    List<Object> stuStr = layNodeBo.toStuStr();
                    if (stuStr != null) {
                        stuList.addAll(stuStr);
                    }
                }

                list.add(new HashMap<String, Object>() {{
                    put("label", "方案");
                    put("planId", plan.getId());
                    put("content", stuList);
                }});
            }
            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.COMPARE_PLAN_MODULE, "综合推荐方案", null, "recommendPlans", list);
        } catch (Exception e) {
            log.error("推送预方案生成异常", e);
        }
    }


    // 当前可以放置开关和联络开关位置
    public void pushLayPosition(Long problemId, List<Node> poleList) {
        try {
            List<HashMap<String, Object>> stus = poleList.stream().map(node -> PlanProcessUtils.toTextBtnStu(node.getPsrId(), node.getPsrType(), node.getPsrName(), null, null)).collect(Collectors.toList());

            // 推送：当前可以放置开关和联络开关位置
            planProcessService.pushStrStu(problemId, ProblemSchemeAnalysis.ADVANCE_PLAN_MODULE, "放置开关或者联络开关处", ProblemSchemeAnalysis.LAY_POSITION_OP_TYPE, PlanProcessUtils.addListPartiStr(stus, "、").toArray());
        } catch (Exception e) {
            log.error("推送可以放置开关和联络开关位置失败", e);
        }
    }

    /**
     * 附近线路（负载率）
     */
    public void pushNearFeeder(Long problemId, List<DeviceFeeder> nearFeeders) {
        try {
            ArrayList<Map<String, Object>> tableData = new ArrayList<>();

            for (DeviceFeeder feeder : nearFeeders) {
                // TODO 报错 '10DKX-456993'
                Double load = feederDeviceMapper.selectLoad(feeder.getPsrId());
                if (load == null) {
                    load = 0.0;
                }
                Double finalLoad = load;
                tableData.add(new HashMap<String, Object>() {{
                    put("devId", feeder.getPsrId());
                    put("devType", "dkx");
                    put("devName", feeder.getName());
                    put("load", DoubleFormatter.formatToThreeDecimals3(finalLoad * 100) + "%");// 负载率
                }});
            }

            planProcessService.pushComponent(problemId, ProblemSchemeAnalysis.ADVANCE_PLAN_MODULE, "附近线路", ProblemSchemeAnalysis.NEAR_FEEDER_OP_TYPE, "nearFeederTable", tableData);
        } catch (Exception e) {
            log.error("推送可附近线路（负载率）失败", e);
        }

    }
}
