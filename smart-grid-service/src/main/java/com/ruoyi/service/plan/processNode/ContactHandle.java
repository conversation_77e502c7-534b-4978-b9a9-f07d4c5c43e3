package com.ruoyi.service.plan.processNode;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.SegFeeder;
import com.ruoyi.entity.device.StationConfiguration;
import com.ruoyi.entity.map.vo.CalcRouteVo;
import com.ruoyi.entity.map.vo.NeedFeederVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.utils.StationNodeUtils;
import com.ruoyi.mapper.device.SegFeederMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.map.impl.MapServiceImpl;
import com.ruoyi.service.plan.model.NearFeedersBo;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import com.ruoyi.util.ListUtils;
import com.ruoyi.util.coordinates.CoordinateConverter;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.constant.NodeConstants.LINE_TYPE_LINEAR;
import static com.ruoyi.constant.NodeConstants.SHAPE_KEY_FEEDER_DL;
import static com.ruoyi.graph.Node.TYPE_SELF;

/**
 * 联络线处理类
 */
@Component
public class ContactHandle {

    @Autowired
    MapServiceImpl mapService;

    @Autowired
    NodeFactory nodeFactory;

    @Autowired
    ProcessUtils processUtils;

    @Autowired
    SegFeederMapper segFeederMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    /**
     * 获取附近线路
     */
    NearFeedersBo getNearFeeders(List<double[]> coords, List<Node> nodes, List<DeviceFeeder> feederList) {
        // 所以附近的线路集合（每个坐标对应的线路集合）
        List<List<NeedFeederVo>> coordsToNears = mapService.pointByParallel(coords, 3, feederList, null, null);

        // 当前所有的附近线路集合
        List<NeedFeederVo> nearFeeders = new ArrayList<>();
        for (List<NeedFeederVo> list : coordsToNears) {
            nearFeeders.addAll(list);
        }
        // 去重
        nearFeeders = ListUtils.distinctByKey(nearFeeders, NeedFeederVo::getPsrId);
        NearFeedersBo nearFeedersBo = new NearFeedersBo(nodes, coordsToNears, nearFeeders);

        HashMap<String, List<NeedFeederVo>> nodeToNears = new HashMap<>();
        // 将设备集合和坐标一一对应
        for (int i = 0; i < nodes.size(); i++) {
            List<NeedFeederVo> needFeederVos = coordsToNears.get(i);
            nodeToNears.put(nodes.get(i).getPsrId(), needFeederVos);
        }
        nearFeedersBo.setNodeToNears(nodeToNears);

        return nearFeedersBo;
    }

    /**
     * 加工处理联络
     *
     * @param nodes        加工的
     * @param nearFeeders
     * @param constantType 联络类型
     * @param token
     */
    Map<String, List<ProcessContactVo>> processContactHandle(List<Node> nodes, NearFeedersBo nearFeeders, String constantType, String token) {


        HashMap<String, List<NeedFeederVo>> nodeToNears = nearFeeders.getNodeToNears();

        // TODO 过滤负载率高的线路 以及 专供过去 必须保持负载率通过

        Map<String, List<ProcessContactVo>> result = new HashMap<>();

        // 每个节点都单独处理
        for (Node node : nodes) {
            // 根据开始和结束节点 调用产生路由
            List<double[]> starts = new ArrayList<>();
            List<double[]> ends = new ArrayList<>();
            List<NeedFeederVo> needFeederVos = nodeToNears.get(node.getPsrId());
            for (NeedFeederVo feeder : needFeederVos) {
                starts.add(node.toDeviceCoords());
                ends.add(feeder.getRecentlyPoint());
            }
            List<ProcessContactVo> processContactVos = processContactRoute(node, needFeederVos, starts, ends, constantType, token);
            result.put(node.getPsrId(), processContactVos);
        }

        // TODO 最后考虑 距离最近（我们这里应该都生成路径判断最近路径长度，而不是两点最短）

        return result;
        //  List<List<NeedFeederVo>> coordsToNears = nearFeeders.getCoordsToNears();
        //        HashMap<Object, NeedFeederVo> priorityFeederMap = new HashMap<>();
//
//        for (Node node : nodes) {
//            List<NeedFeederVo> needFeederVos = nodeToNears.get(node.getPsrId());
//            // TODO 附近线这里临时取第0条
//            priorityFeederMap.put(node.getPsrId(), needFeederVos.get(0));
//        }

        // 根据开始和结束节点 调用产生路由
//        List<double[]> starts = new ArrayList<>();
//        List<double[]> ends = new ArrayList<>();
//        for (int i = 0; i < nodes.size(); i++) {
//            Node node = nodes.get(i);
//            NeedFeederVo feeder = priorityFeederMap.get(node.getPsrId());
//            starts.add(node.toDeviceCoords());
//            ends.add(feeder.getRecentlyPoint());
//        }
//        List<CalcRouteVo> routes = new ArrayList<>();
//        // 批量根据开始和结束生产路径
//        if (!starts.isEmpty()) {
//            routes = mapService.calculateRouteAsync(starts, ends, token);
//        }
//        Map<String, ProcessContactVo> nodeToContactFeederMap = new HashMap<>();
//        // 根据路由参数联络对象
//        for (int i = 0; i < routes.size(); i++) {
//            CalcRouteVo calcRouteVo = routes.get(i);
//            Node node = nodes.get(i);
//            if (calcRouteVo != null) {
//                // 路由路径节点集合
//                List<Node> pathNodes = calcRouteVo.getNodeList();
//                // 关联的线路
//                NeedFeederVo nearestFeeder = priorityFeederMap.get(node.getPsrId());
//                ProcessContactVo processContactVo = new ProcessContactVo();
//
//                // 联络线路径节点加工
//                List<Node> newPathNodes = processContactPaths(pathNodes, node, constantType, nearestFeeder);
//
//                // 生产加工路径对象
//                processContactVo.setTotalLength(calcRouteVo.getTotalLength());
//                processContactVo.setContactNodeList(newPathNodes);
//                processContactVo.setStartPsrId(node.getPsrId());
//                processContactVo.setEndPsrId(nearestFeeder.getDeviceId());
//                processContactVo.setContactFeederId(nearestFeeder.getPsrId());
//                processContactVo.setContactFeederName(nearestFeeder.getFeedName());
//                processContactVo.setContactNode(node);
//
//                nodeToContactFeederMap.put(node.getPsrId(), processContactVo);
//            }
//        }
//        return nodeToContactFeederMap;
    }


    /**
     * 加工联络路由
     */
    List<ProcessContactVo> processContactRoute(Node node, List<NeedFeederVo> needFeederVos, List<double[]> starts, List<double[]> ends, String constantType, String token) {
        List<CalcRouteVo> routes = new ArrayList<>();
        // 批量根据开始和结束生产路径
        if (!starts.isEmpty()) {
            routes = mapService.calculateRouteAsync(starts, ends, token);
        }
        List<ProcessContactVo> processContacts = new ArrayList<>();
        // 根据路由参数联络对象
        for (int i = 0; i < routes.size(); i++) {
            CalcRouteVo calcRouteVo = routes.get(i);
            if (calcRouteVo != null) {
                // 路由路径节点集合
                List<Node> pathNodes = calcRouteVo.getNodeList();
                // 关联的线路
                NeedFeederVo nearestFeeder = needFeederVos.get(i);
                ProcessContactVo processContactVo = new ProcessContactVo();

                // 联络线路径节点加工
                List<Node> newPathNodes = processContactPaths(pathNodes, node, constantType, nearestFeeder);

                // 生产加工路径对象
                processContactVo.setTotalLength(calcRouteVo.getTotalLength());
                processContactVo.setContactNodeList(newPathNodes);
                processContactVo.setStartPsrId(node.getPsrId());
                processContactVo.setEndPsrId(nearestFeeder.getDeviceId());
                processContactVo.setContactFeederId(nearestFeeder.getPsrId());
                processContactVo.setContactFeederName(nearestFeeder.getFeedName());
                processContactVo.setContactNode(node);

                processContacts.add(processContactVo);
            }
        }
        return processContacts;
    }

    /**
     * 加工联络线的路径节点集合
     *
     * @param nodes         路由路径集合节点
     * @param constantType  联络类型 参考ProcessContactBo里面的type
     * @param startNode     结束杆塔节点
     * @param nearestFeeder 附近线路
     */
    List<Node> processContactPaths(List<Node> nodes, Node startNode, String constantType, NeedFeederVo nearestFeeder) {
        if (nodes == null) {
            return null;
        }

        ArrayList<Node> result = new ArrayList<>(nodes);
        // 移除路由路径首节点
        result.remove(0);
        Node startEdge = result.get(0);

        Node linkNode = null;

        // 1、处理首部
        if (StringUtils.equals(constantType, ProcessContactBo.POLE_RANGE)) {
            // 处理首节点新增联络开关
            List<Node> startNodes = processUtils.generatePoleKg(startNode, startEdge, true);
            List<Node> kgs = startNodes.subList(0, startNodes.size() - 1);
            linkNode = kgs.get(kgs.size() - 1);

            result.addAll(0, kgs);
        } else if (StringUtils.equals(constantType, ProcessContactBo.STATION_BAY)) {
            // 剩余间隔的开始节点 我们需要重新设置
            linkNode = startNode.clone();
            result.add(0, linkNode);
        } else {
            linkNode = startNode;
        }

        HashMap properties = linkNode.getProperties();
        properties.put("contactFeederId", nearestFeeder.getPsrId());
        properties.put("contactFeederName", nearestFeeder.getFeedName());
        // 与线路联络线建立连接关联关系
        linkNode.addEdge(startEdge, true);

        // 2、处理末尾
        if (nodes.size() > 2) {
            // 表示从杆塔处新建联络线
            Node endPole = processUtils.toWlgtNode(nearestFeeder.getDeviceId(), nearestFeeder.getRecentlyPoint());
            result.remove(result.size() - 1);
            Node edge = result.get(result.size() - 1);
            endPole.addEdge(edge, false);
            result.add(endPole);
        }
        return result;
    }

    /**
     * 加工导线段打断的中心坐标
     */
    public void segBreakNodePoint(SegBreakNodeBo segBreakNode) {
        Node segEdge = segBreakNode.getSegEdge();

        //1、使用开始和结束
        List<Double> startPoint = queryDeviceInfo.selectDeviceCoords(segBreakNode.getStartNode().getPsrId(), segBreakNode.getStartNode().getPsrType());
        List<Double> endPoint = queryDeviceInfo.selectDeviceCoords(segBreakNode.getEndNode().getPsrId(), segBreakNode.getEndNode().getPsrType());

        if (CollectionUtils.isEmpty(startPoint) || CollectionUtils.isEmpty(endPoint)) {
            SegFeeder segFeeder = segFeederMapper.selectSegFeeder(segEdge.getPsrId());
            if (segFeeder != null && StringUtils.isNotBlank(segFeeder.getCoordinate())) {
                List<List<Double>> coords = CoordinateConverter.parseCoordinates(segFeeder.getCoordinate());
                startPoint = coords.get(0);
                endPoint = coords.get(coords.size() - 1);
            }
        }
        if (CollectionUtils.isNotEmpty(startPoint) && CollectionUtils.isNotEmpty(endPoint)) {
            LineString lineString = CoordinateConverter.toLineString(Arrays.asList(startPoint, endPoint));
            Point centroid = lineString.getCentroid();

            segBreakNode.setStartPoint(startPoint);
            segBreakNode.setEndPoint(endPoint);
            segBreakNode.setPoint(centroid);
        }
    }

    /**
     * 导线段在中间打断 并且在中间上新的环网柜
     *
     * @param segBreakNode 导线段对线
     */
    List<Node> breakSegAddHwg(SegBreakNodeBo segBreakNode) {

        // 设备类型zf07 获取站房以及以下的node
        List<Double> coordinateList = segBreakNode.toLngLat();
        List<Node> stationNodes = StationNodeUtils.generateStationAndChildren("环网柜", "zf07", coordinateList, 6, new StationConfiguration());

        Node startNode = segBreakNode.getStartNode().clone();
        Node endNode = segBreakNode.getEndNode().clone();
        Node segEdge = segBreakNode.getSegEdge().clone();

        // =========================== 创建两边导线段 =======================

        List<Node> nodeNoKgs = stationNodes.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());
        Node firstBayNode = nodeNoKgs.get(0);
        Node lastBayNode = nodeNoKgs.get(nodeNoKgs.size() - 1);

        LineString firstLineString = CoordinateConverter.toLineString(Arrays.asList(segBreakNode.getStartPoint(), CoordinateConverter.pointToLngLat(firstBayNode.getGeometry().getInteriorPoint())));

        LineString endLineString = CoordinateConverter.toLineString(Arrays.asList(segBreakNode.getEndPoint(), CoordinateConverter.pointToLngLat(lastBayNode.getGeometry().getInteriorPoint())));

        String shapeKey = StringUtils.equals(segEdge.getPsrType(), "dxd") ? NodeConstants.SHAPE_KEY_FEEDER_JK : NodeConstants.SHAPE_KEY_FEEDER_DL;

        Node startEdge = segEdge.clone();
        startEdge.setPsrName("起始导线段");
        startEdge.setEdge(true);
        startEdge.setId(UUID.randomUUID().toString());
        startEdge.setPsrId(null);

        startEdge.setLineType(LINE_TYPE_LINEAR);
        startEdge.setShapeKey(shapeKey);
        startEdge.setType(TYPE_SELF);
        startEdge.setGeometry(firstLineString);
        startEdge.putProperties("type", shapeKey);

        Node endEdge = segEdge.clone();
        endEdge.setPsrName("结束导线段");
        endEdge.setEdge(true);
        endEdge.setId(UUID.randomUUID().toString());
        startEdge.setPsrId(null);
        endEdge.setLineType(LINE_TYPE_LINEAR);
        endEdge.setShapeKey(SHAPE_KEY_FEEDER_DL);
        endEdge.setType(TYPE_SELF);
        endEdge.setGeometry(endLineString);
        endEdge.putProperties("type", shapeKey);

        // =========================== 建立链接关系 =======================
        // TODO 并且将开关的名称改为”已使用“（后面有空再改为”有XXXX供，至XXXXXX变“）

        //增加新增的两条线的sourcePort和targetPort以及间隔的Port中的第二个point
        //新增起始点和起始导线段的关系
        startNode.addEdge(startEdge, true);
        //新增第一个间隔和起始导线段的关系
        firstBayNode.addEdge(startEdge, false);
        firstBayNode.setPsrName("已使用");
        //新增第二个间隔和结束导线段的关系
        endNode.addEdge(endEdge, false);
        //结束点和起始结束导线段
        lastBayNode.addEdge(endEdge, true);
        lastBayNode.setPsrName("已使用");

        //将修改的间隔node进行替换
        stationNodes.add(startEdge);
        stationNodes.add(endEdge);
        stationNodes.add(startNode);
        stationNodes.add(endNode);

        //删除中间导线段,新增导线段的properties属性"remove", "true"
        segEdge.putProperties("remove", "true");
        stationNodes.add(segEdge);

        //新增起始导线段的targetPort
//        PortItem targetPortItem = (PortItem)firstNode .getPorts().getItems().get(0);
//        startEdge.setTargetPort(targetPortItem.getId());

        //新增结束导线段的sourcePort
//        PortItem sourcePortItem = (PortItem)lastNode .getPorts().getItems().get(0);
//        startEdge.setSourcePort(sourcePortItem.getId());
        //修改起始间隔和结束间隔的第二个point，顺便把备用改成已使用

        return stationNodes;
    }

}
