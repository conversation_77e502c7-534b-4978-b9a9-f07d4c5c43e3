package com.ruoyi.service.plan.processNode;

import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.processNode.contactHandle.ContactLayHandle;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.IntStream;

/**
 * 处理加工节点放置位置实体类
 */
@Component
public class ProcessLayNodeHandle {

    @Autowired
    ProcessUtils processUtils;

    @Autowired
    ContactLayHandle contactLayHandle;

    /**
     * 处理加工节点放置位置列表
     * @param layPositionList 需要设置的位置集合
     * @param processContactMap 处理好的联络映射
     */
    public void processLayList(List<List<ArrayList<LayNodeBo>>> layPositionList, Map<String, ProcessContactBo> processContactMap) {
        // 位置数据加工
        for (List<ArrayList<LayNodeBo>> arrayLists : layPositionList) {
            // 多个方案
            for (ArrayList<LayNodeBo> arrayList : arrayLists) {
                // 缓存已使用的杆塔ID集合
                HashMap<String, ProcessContactVo> usedContact = new HashMap<>();

                // 单个方案
                handlePlanOperate(arrayList, processContactMap, usedContact);
            }
        }
    }

    /**
     * 分别处理加装放置位置对象数据
     *
     * @param layNodeBoList     需要设置的放置位置对象
     * @param processContactMap 加工联络对象map
     * @param usedContact       已经使用的联络对象
     */
    private void handlePlanOperate(ArrayList<LayNodeBo> layNodeBoList,
                                  Map<String, ProcessContactBo> processContactMap,
                                  HashMap<String, ProcessContactVo> usedContact) {
        for (LayNodeBo layNodeBo : layNodeBoList) {
            List<Node> range = layNodeBo.getRange();
            // 获取当前出现开关的首位置下标
            OptionalInt first = IntStream.range(0, range.size())
                    .filter(i -> range.get(i).isKg("all"))
                    .findFirst();
            int index = first.orElse(-1);
            ProcessContactBo processContact = processContactMap.get(layNodeBo.getId());

            // 首开关
            if (StringUtils.equals(layNodeBo.getType(), LayNodeBo.KG_HEAD_TYPE)) {
                if (index == -1) {
                    List<Node> nodes = processUtils.generatePoleKg(range.get(0), range.get(1), true);
                    layNodeBo.setLayKgNodes(nodes);
                }
            } else if (StringUtils.equals(layNodeBo.getType(), LayNodeBo.KG_TYPE)) {  // 开关
                if (index == -1) {
                    List<Node> nodes = processUtils.generatePoleKg(range, true);
                    layNodeBo.setLayKgNodes(nodes);
                }
            } else if (StringUtils.equals(layNodeBo.getType(), LayNodeBo.CONTACT_TYPE)) {
                // 先查找最近路径的联络线的杆塔   然后在生成联络线
                processLayNodeContact(layNodeBo, processContact, usedContact, false);
            } else if (StringUtils.equals(layNodeBo.getType(), LayNodeBo.KG_AND_CONTACT_TYPE)) {
                // 表示当前线上已经有开关了我们就不需要上开关了 并且上联络线的位置在当前开关之前  否则去
                processLayNodeContact(layNodeBo, processContact, usedContact, true);
            } else if (StringUtils.equals(layNodeBo.getType(), LayNodeBo.END_CONTACT_TYPE)) {
                // 末端新增联络线
                processLayNodeContact(layNodeBo, processContact, usedContact, false);
            }
        }
    }


    /**
     * 加工 放置节点的联络线
     */
    private void processLayNodeContact(LayNodeBo layNodeBo, ProcessContactBo processContact, HashMap<String, ProcessContactVo> usedContact, boolean isAddMainKg) {

        ProcessContactVo contactVo = getContact(layNodeBo, processContact, usedContact, isAddMainKg);

        if (contactVo != null) {
            List<Node> contactNodeList = contactVo.getContactNodeList();
            List<Node> range = layNodeBo.getRange();

            // 表示有新增联络线
            if (contactNodeList != null) {
                Node node = contactVo.getContactNode();
                // 新增开关
                if (isAddMainKg && node != null) {
                    for (int i = 0; i < range.size(); i++) {
                        if (range.get(i).equals(node)) {
                            contactNodeList.addAll(processUtils.generatePoleKg(node, range.get(i + 1), true));
                        }
                    }
                }

                layNodeBo.setContactNodes(contactVo.getContactNodeList());
                layNodeBo.setContactLength(contactVo.getTotalLength());
                layNodeBo.setContactStartPsrId(contactVo.getStartPsrId());
                layNodeBo.setContactEndPsrId(contactVo.getEndPsrId());
                layNodeBo.setContactFeederId(contactVo.getContactFeederId());
                layNodeBo.setContactFeederName(contactVo.getContactFeederName());
            }
        }
    }

    // 生产联络线节点
    private ProcessContactVo getContact(LayNodeBo layNodeBo, ProcessContactBo processContactBo,
                                       HashMap<String, ProcessContactVo> usedContact, boolean isAddMainKg) {

        // 当前范围之前的杆塔

        String type = processContactBo.getType();
        IContactHandle contactHandle = contactLayHandle.getContactHandle(type);

        return contactHandle.getContact(layNodeBo, processContactBo, usedContact, isAddMainKg);
    }

}
