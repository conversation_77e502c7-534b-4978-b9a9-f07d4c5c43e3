package com.ruoyi.service.plan.processNode;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.map.impl.MapServiceImpl;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.processNode.contactHandle.ContactLayHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProcessNodeService implements IProcessNodeService {

    @Autowired
    NodeFactory nodeFactory;

    @Autowired
    MapServiceImpl mapService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ContactLayHandle contactLayHandle;

    @Autowired
    ProcessUtils processUtils;

    @Autowired
    ContactHandleService contactHandleService;

    @Autowired
    ProcessLayNodeHandle processLayNodeHandle;

    @Override
    public void handleLayOperateNode(Long problemId, List<List<ArrayList<LayNodeBo>>> layPositionList, String token, String feederId) {
        // =========================== 获取附近线路 =======================
        List<DeviceFeeder> nearFeederList = mapService.selectNearFeeder(feederId, 3.0);
        if (nearFeederList == null) {
            nearFeederList = feederDeviceMapper.selectList();
            nearFeederList = nearFeederList.stream()
                    .filter(device -> !feederId.equals(device.getPsrId()))
                    .collect(Collectors.toList());
        } else {
            // 推送：附近线路（负载率）
            pushPlanProcessService.pushNearFeeder(problemId, nearFeederList);
            // 过滤重过载线路
            nearFeederList = contactHandleService.filterOverLoadFeeder(nearFeederList);
        }

        // 提前处理附近联络对象
        Map<String, ProcessContactBo> processContactMap = contactLayHandle.handleNearLayContact(problemId, token, layPositionList, nearFeederList);

        // 处理加工节点放置位置列表
        processLayNodeHandle.processLayList(layPositionList, processContactMap);
    }


}
