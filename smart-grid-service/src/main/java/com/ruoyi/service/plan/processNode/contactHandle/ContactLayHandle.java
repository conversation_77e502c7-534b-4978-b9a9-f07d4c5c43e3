package com.ruoyi.service.plan.processNode.contactHandle;

import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.model.HwgBayBo;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理相关联络相关单句柄
 */
@Component
public class ContactLayHandle {

    @Autowired
    PoleContactHandle poleContactHandle;

    @Autowired
    AddHwgContactHandle addHwgContactHandle;

    @Autowired
    BayContactHandle bayContactHandle;

    /**
     * 不同类型联络类型对应不同的处理service
     */
    public HashMap<String, IContactHandle> getConcatHandleMap() {
        return new HashMap<String, IContactHandle>() {{
            put(ProcessContactBo.POLE_RANGE, poleContactHandle);
            put(ProcessContactBo.SEG_ADD_HWG, addHwgContactHandle);
            put(ProcessContactBo.STATION_BAY, bayContactHandle);
        }};
    }

    /**
     * 根据联络类型获取联络处理service
     */
    public IContactHandle getContactHandle(String type) {
        HashMap<String, IContactHandle> concatHandleMap = getConcatHandleMap();
        return concatHandleMap.get(type);
    }

    // =========================== 处理附近联络lay放置位置 =======================

    /**
     * 处理附近放置 联络线联络单问题
     */
    public Map<String, ProcessContactBo> handleNearLayContact(Long problemId, String token,
                                                           List<List<ArrayList<LayNodeBo>>> layPositionList,
                                                           List<DeviceFeeder> nearFeederList) {

        // 处理联络线的实体类
        ArrayList<ProcessContactBo> processContacts = new ArrayList<>();

        // 获取需要放在位置的杆塔节点集合
        for (List<ArrayList<LayNodeBo>> arrayLists : layPositionList) {
            // 多个方案
            for (ArrayList<LayNodeBo> arrayList : arrayLists) {
                for (LayNodeBo layNodeBo : arrayList) {
                    ProcessContactBo processContactBo = getProcessContactBo(layNodeBo);
                    if (processContactBo != null) {
                        processContactBo.setLayNodeId(layNodeBo.getId());
                        processContacts.add(processContactBo);
                    }
                }
            }
        }

        // 根据不同类型的ProcessContactBo 进行分组处理
        // 按自定义类型字段分组
        Map<String, List<ProcessContactBo>> groupedByCustomType = processContacts.stream()
                .collect(Collectors.groupingBy(ProcessContactBo::getType));

        // 根据不同的联络类型分别对应相应的处理类
        HashMap<String, IContactHandle> handleMap = getConcatHandleMap();

        for (Map.Entry<String, IContactHandle> entry : handleMap.entrySet()) {
            String type = entry.getKey();
            List<ProcessContactBo> pContacts = groupedByCustomType.get(type);
            IContactHandle handle = entry.getValue();

            // 当前类型有
            if (!CollectionUtils.isEmpty(pContacts)) {
                handle.handleContact(problemId, pContacts, nearFeederList, token);
            }
        }
        // 1、推送：当前可以放置开关和联络开关位置
        //  pushPlanProcessService.pushLayPosition(problemId, poleList);
        return processContacts.stream().collect(Collectors.toMap(ProcessContactBo::getLayNodeId, n -> n));
    }

    /**
     * 根据位置获取联络先对象
     */
    private ProcessContactBo getProcessContactBo(LayNodeBo layNodeBo) {
        String type = layNodeBo.getType();
        List<Node> range = layNodeBo.getRange();
        List<Node> poles = null;

        // 根据放置位置获取队友都杆塔集合
        if (StringUtils.equals(type, LayNodeBo.CONTACT_TYPE) ||
                StringUtils.equals(type, LayNodeBo.KG_AND_CONTACT_TYPE)) {
            poles = range.stream().filter(Node::isPole).collect(Collectors.toList());
        } else if (StringUtils.equals(type, LayNodeBo.END_CONTACT_TYPE)) {
            Node node = range.get(range.size() - 1);
            if (node.isPole()) {
                poles = Arrays.asList(node);
            }
        } else {
            return null;
        }

        // 表示不是在杆塔新增联络线  需要特别处理
        if (CollectionUtils.isEmpty(poles)) {
            // 获取最后一个节点
            // 当前路径下的所有环网柜集合
            List<HwgBayBo> hwgStations = NodeUtils.getHwgStation(range);

            if (!CollectionUtils.isEmpty(hwgStations)) {
                // 获取站房剩余间隔大于0的

                Optional<HwgBayBo> optional = hwgStations.stream()
                        .filter(n -> n.getBayNodes() != null && n.getBayNodes().size() >= 2).findFirst();
                HwgBayBo hwg = optional.orElse(null);

                // 没有剩余间隔 获取剩余间隔必须要大于二 因为我们使用该间隔之后 必须还要剩余一个
                if (hwg == null) {
                    // TODO 替换先不做  后面再说
//                    Node hwgNode = hwgStations.get(0).getHwg();
//                    // 需要替换环网柜
//                    if (isHwgReplace(hwgNode)) {
//                        // 替换站房
//                        return ProcessContactBo.createStationReplace(new ReplaceStationNodeBo(hwgNode, range));
//                    } else {
                    // 导线段新增新的环网柜
                    SegBreakNodeBo segBreakNode = getSegBreakNode(range);
                    if (segBreakNode != null) {
                        return ProcessContactBo.createSegBreakNode(segBreakNode);
                    }
                    //   }
                } else {
                    // 剩余间隔的新增连接线
                    return ProcessContactBo.createHwgBay(hwg);
                }
            } else {
                // 新增环网柜
                // 导线段新增新的环网柜
                SegBreakNodeBo segBreakNode = getSegBreakNode(range);
                if (segBreakNode != null) {
                    return ProcessContactBo.createSegBreakNode(segBreakNode);
                }
            }
        } else {
            // 杆塔新增联络线
            return ProcessContactBo.createPoles(poles);
        }
        return null;
    }


    /**
     * 获取导线段打断节点
     */
    public static SegBreakNodeBo getSegBreakNode(List<Node> paths) {
        // 我们从末尾段往里走
        if (paths.size() < 3) {
            return null;
        }
        // 从环网柜开始
        int startIndex = -1;
        for (int i = 0; i < paths.size() - 1; i++) {
            Node node = paths.get(i);
            Node parent = node.getParent();
            if (parent != null && parent.isHwg() && i > 0 && i < paths.size() - 1) {
                startIndex = i;
            }
        }

        // 环网柜开始 没有 就从开开始知道没有开关或者杆塔后面
        if (startIndex == -1) {
            for (int i = 1; i < paths.size() - 2; i++) {
                Node node = paths.get(i);
                // 不等于杆塔或者开关就开始设置
                if (!node.isEdge() && !(node.isPole() || node.isKg("all"))) {
                    startIndex = i;
                    break;
                }
            }
        }

        if (startIndex > -1) {
            for (int i = startIndex; i < paths.size() - 1; i++) {
                Node node = paths.get(i);
                if (node.isSegFeeder()) {
                    Node end = paths.get(i + 1);
                    Node start = paths.get(i - 1);
                    // TODO 由于 10DKX-290577 线路的图模文件和单线图 有一块完全不一样 我们这里临时改
                    if (StringUtils.equals("2785eb8e-da51-408a-8603-ed1957ac2004", start.getPsrId())) {
                        //  end.setId("40a80eeb-88e0-4ee4-b2dd-ee675a98192b");
                        end.setPsrId("40a80eeb-88e0-4ee4-b2dd-ee675a98192b");
                        end.putProperties("psrId", "40a80eeb-88e0-4ee4-b2dd-ee675a98192b");
                    }

                    return new SegBreakNodeBo(paths.get(i - 1), node, end);
                }
            }
        } else {
            // 从后面开始
            for (int i = paths.size() - 2; i > 0; i--) {
                Node node = paths.get(i);
                if (node.isSegFeeder()) {
                    return new SegBreakNodeBo(paths.get(i - 1), node, paths.get(i + 1));
                }
            }
        }

        return null;
    }

    /**
     * 判断当前环网柜是否可以替换
     *
     * @param hwgNode
     * @return
     */
    boolean isHwgReplace(Node hwgNode) {
        List<Node> children = hwgNode.getChildren();

        if (!CollectionUtils.isEmpty(children)) {
            // 这里需要判断当前的间隔是否大于6个 如果大于6个 那么还是需要需要新增环网柜
            List<Node> backupKgs = children.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());
            if (backupKgs.size() < 6) {
                return true;
            }
        }
        return false;
    }
}
