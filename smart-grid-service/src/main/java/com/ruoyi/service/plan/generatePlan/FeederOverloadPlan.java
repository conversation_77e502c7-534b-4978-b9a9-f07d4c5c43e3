package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.StationPb;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.vo.BranchMainPathBo;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.calc.measurement.CalcLoadService;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.identify.BigBranchIdentify;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.CanRunAdjustFeeder;
import com.ruoyi.service.plan.model.contact.BranchTransfer;
import com.ruoyi.service.plan.utils.BranchPathLayNodeUtil;
import com.ruoyi.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.units.qual.A;
import org.dom4j.Branch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 线路重过载方案生产
 * 1、重过载线路部分负荷运方调整至已有联络线路。
 * 2、针对重过载线路负载率超80%情况，可考虑部分负荷切转至新出线路，切转点宜设置在重过载线路末端。
 * 3、针对重过载线路重要连接位置缺乏联络情况，考虑与临近线路新上联络，重过载线路部分负荷运方调整至该线路。
 */
@Service
@Slf4j
public class FeederOverloadPlan implements IGeneratePlan {

    @Autowired
    ISingMapService singMapService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    BigBranchIdentify bigBranchIdentify;

    @Autowired
    ContactHandleService contactHandleService;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    BigBranchPlan bigBranchPlan;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) {

        // =========================== 基础信息准备 =======================
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);
        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(feederId);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        Map<String, Node> nodeMap = topologyMap.getNodeMap();

        double maxLoad = 0.8; // (double) iConditionService.lineOverloadNum() / 100;   // 当前配置最大负载率
        List<Node> allPb = topologyMap.getAllPb();

        // 配变节点的容量加工
        baseGeneratePlan.processPbNodeCap(allPb);

        if (feederNt.getHisMaxLoadRate() < maxLoad) {
            throw new RuntimeException("当前历史线路最大负载率为" + feederNt.getHisMaxLoadRate() * 100 + "%，并未大于" + maxLoad * 100 + "%，该线路不是线路重过载问题！");
        }

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, allPb);

        // =========================== 联络线和运放调整组合 =======================

        // 获取所有下联络线
        List<FeederNtVo> contactFeederNts = baseGeneratePlan.getContactFeederNts(topologyMap);

        //  获取可以运方调整的联络线开关
        List<ContactFeederKg> contactFeederKgs = contactHandleService.getCanContactFeederKg(topologyMap, contactFeederNts, maxLoad);

        // 获取能运放调整开关的所有可能组合
        CanRunAdjustFeeder canRunAdjust = contactHandleService.getCanRunAdjust(feederNt, contactFeederKgs, contactFeederNts, nodePath, maxLoad);
        // TODO 后续在考虑多个联络互相开关组合的形势 如果多个组合在一起还有考虑组合的供电范围不能有重叠 因为会回环了
        // List<List<FeederTransferCap>> onlySubResult = canRunAdjust.getOnlySubResult();
        // 只考虑能单个转供出去
        List<List<FeederTransferCap>> allCanTransfer = canRunAdjust.getAllCanResult();
        List<FeederTransferCap> runAdjustList = priorRunAdjust(allCanTransfer);

        // (3)、推送：问题释义

        // (4)、推送：大分子识别

        // TODO （5）、推送联络线（包括负载率）

        List<Plan> plans = new ArrayList<>();

        // 有可以运放调整的方案开关
        if (CollectionUtils.isNotEmpty(runAdjustList)) {
            plans = baseGeneratePlan.transferCapToPlans(runAdjustList, problemId, nodeMap);
        }

        // TODO 变电站新出线（变电站里的很近的情况或者以上的方案都没满足 可以考虑）

        // 如果方案数量小于3 我们这里再添加联络线的
        if (CollectionUtils.isEmpty(plans) || plans.size() < 3) {
            List<List<BranchTransfer>> contactLays = contactHandleService.findContactLay(feederNt, nodePath, maxLoad);
            System.out.println(contactLays);

            List<List<ArrayList<LayNodeBo>>> layPositionList = getLayPositionList(contactLays, nodeMap, nodePath.getBranchAllNodeMap());

            // 给杆塔加装坐标
            baseGeneratePlan.layPositionCoords(layPositionList);

            try {
                processNodeService.handleLayOperateNode(problemId, layPositionList, token, feederId);
            } catch (Exception e) {
                log.error("生产方案异常！", e);
                throw new RuntimeException("生产方案异常！", e);
            }

            // 所有的组合在一起
            List<List<ArrayList<LayNodeBo>>> combLayPos = baseGeneratePlan.getCombLayPos(layPositionList);

            planProcessService.pushLoadingProcess(problemId, "方案集合按照开关数和联络距离排序");

            // 排序
            baseGeneratePlan.combLayPosSort(combLayPos);

            // 每种方案的所有操作集合扁平化
            List<ArrayList<LayNodeBo>> sureLays = baseGeneratePlan.flatLayNode(combLayPos);

            HashMap<Long, ArrayList<LayNodeBo>> planLaysMap = new HashMap<>();

            // 生成方案
            List<Plan> contactPlans = baseGeneratePlan.layToPlans(sureLays, problemId, planLaysMap);

            plans.addAll(contactPlans);
        }

        // (7)、推送：预方案生成
        //  pushPlanProcessService.pushPlans(problemId, plans, sureLays);

        // 提取前三条方案
        List<Plan> resultPlans = plans.stream().limit(3).collect(Collectors.toList());

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        //  pushPlanProcessService.pushRecommendPlans(problemId, resultPlans, planLaysMap);

        // 获取各个分支末端
        return resultPlans;
    }

    /**
     * 更具运方调整的组合（单个开关有多个操作）
     * 我们按照一定的优先级 提取其中的一个即可
     */
    List<FeederTransferCap> priorRunAdjust(List<List<FeederTransferCap>> allCanTransfer) {
        if (CollectionUtils.isEmpty(allCanTransfer)) {
            return null;
        }

        ArrayList<FeederTransferCap> result = new ArrayList<>();

        for (List<FeederTransferCap> transferCaps : allCanTransfer) {
            if (CollectionUtils.isEmpty(transferCaps)) {
                continue;
            }
            // 将当前联络开关的多个操作 我们取其中一个
            // 先排序(两条线专供之后的差值越小越优先) 在提取第一个
            transferCaps.sort((trf1, trf2) -> {
                double val1 = Math.abs(trf1.getSourceChangeLoad() - trf1.getTransferChangeLoad());
                double val2 = Math.abs(trf2.getSourceChangeLoad() - trf2.getTransferChangeLoad());
                return Double.compare(val1, val2);
            });
            result.add(transferCaps.get(0));
        }
        return result;
    }

    int getScope(BranchTransfer contactLay) {
        int scope = 1;
        // 长度越长分数越高
        scope = scope + contactLay.getHeadPaths().size();
        BranchNode branchNode = contactLay.getBranchNode();
        // 主干路径
        if (branchNode.isMain()) {
            scope = scope + 20;
        }
        // 有杆塔
        for (Node node : branchNode.getNodes()) {
            if (node.isPole()) {
                scope = scope + 30;
                break;
            }
        }
        return scope;
    }

    List<List<ArrayList<LayNodeBo>>> getLayPositionList(List<List<BranchTransfer>> contactLayList, Map<String, Node> nodeMap, HashMap<String, BranchNode> branchNodeMap) {

        List<ArrayList<LayNodeBo>> singLays = new ArrayList<>();

        for (List<BranchTransfer> contactLays : contactLayList) {
            // 我们排序
            contactLays.sort((a, b) -> getScope(b) - getScope(a));
            BranchTransfer branchTransfer = contactLays.get(0);
            BranchNode branchNode = branchTransfer.getBranchNode();

            List<BranchMainPathBo> mainPaths = new ArrayList<>();
            Node startNode = nodeMap.get(branchNode.getPsrId());
            Node nextNode = nodeMap.get(branchNode.getNextPsrId());
            BranchPathLayNodeUtil.loopBranchPaths(
                    startNode, mainPaths, branchNodeMap, new HashMap<>(), nextNode, true,
                    (List<Node> a, Node b, HashMap<String, BranchNode> c) -> bigBranchPlan.toNodeNums(a, b, c), null);

            ArrayList<LayNodeBo> layPositionByPaths = BranchPathLayNodeUtil.getLayPositionByPaths(mainPaths.get(0).getPaths());
            singLays.add(layPositionByPaths);
        }

        return Arrays.asList(singLays);
    }

    /**
     * 获取末端放置联络线的所有集合
     */
//    private List<List<ArrayList<LayNodeBo>>> getLayPositionList() {
//        List<List<ArrayList<LayNodeBo>>> result = new ArrayList<>();
//
//    }

}
