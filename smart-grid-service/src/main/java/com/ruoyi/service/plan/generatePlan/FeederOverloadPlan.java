package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.StationPb;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.vo.BranchMainPathBo;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.calc.measurement.CalcLoadService;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.identify.BigBranchIdentify;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.CanRunAdjustFeeder;
import com.ruoyi.service.plan.model.contact.BranchTransfer;
import com.ruoyi.service.plan.utils.BranchPathLayNodeUtil;
import com.ruoyi.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 线路重过载方案生产
 * 1、重过载线路部分负荷运方调整至已有联络线路。
 * 2、针对重过载线路负载率超80%情况，可考虑部分负荷切转至新出线路，切转点宜设置在重过载线路末端。
 * 3、针对重过载线路重要连接位置缺乏联络情况，考虑与临近线路新上联络，重过载线路部分负荷运方调整至该线路。
 */
@Service
@Slf4j
public class FeederOverloadPlan implements IGeneratePlan {

    @Autowired
    ISingMapService singMapService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    BigBranchIdentify bigBranchIdentify;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) {

        // =========================== 基础信息准备 =======================
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);
        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(feederId);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        Map<String, Node> nodeMap = topologyMap.getNodeMap();

        double maxLoad = (double) iConditionService.lineOverloadNum() / 100;   // 当前配置最大负载率
        List<Node> allPb = topologyMap.getAllPb();

        // 配变节点的容量加工
        baseGeneratePlan.processPbNodeCap(allPb);

        if (feederNt.getHisMaxLoadRate() < maxLoad) {
            throw new RuntimeException("当前历史线路最大负载率为" + feederNt.getHisMaxLoadRate() * 100 + "%，并未大于" + maxLoad * 100 + "%，该线路不是线路重过载问题！");
        }

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, allPb);

        // =========================== 联络线和运放调整组合 =======================

        // 获取所有下联络线
        List<FeederNtVo> contactFeederNts = baseGeneratePlan.getContactFeederNts(topologyMap);

        //  获取可以运方调整的联络线开关
        List<ContactFeederKg> contactFeederKgs = getCanContactFeederKg(topologyMap, contactFeederNts, maxLoad);

        // 获取能运放调整开关的所有可能组合
        CanRunAdjustFeeder canRunAdjust = getCanRunAdjust(feederNt, contactFeederKgs, contactFeederNts, nodePath, maxLoad);
        // TODO 后续在考虑多个联络互相开关组合的形势 如果多个组合在一起还有考虑组合的供电范围不能有重叠 因为会回环了
        // List<List<FeederTransferCap>> onlySubResult = canRunAdjust.getOnlySubResult();
        // 只考虑能单个转供出去
        List<List<FeederTransferCap>> allCanTransfer = canRunAdjust.getAllCanResult();
        List<FeederTransferCap> runAdjustList = priorRunAdjust(allCanTransfer);

        // (3)、推送：问题释义

        // (4)、推送：大分子识别

        // TODO （5）、推送联络线（包括负载率）

        // 有可以运放调整的
        if (CollectionUtils.isNotEmpty(runAdjustList)) {
            List<Plan> plans = baseGeneratePlan.transferCapToPlans(runAdjustList, problemId, nodeMap);
            return plans;
        } else {
            // TODO 只能通过新增联络线的操作
        }
        findContactLay(feederNt, nodePath, maxLoad);

        return null;
    }

    /**
     * 更具运方调整的组合（单个开关有多个操作）
     * 我们按照一定的优先级 提取其中的一个即可
     */
    List<FeederTransferCap> priorRunAdjust(List<List<FeederTransferCap>> allCanTransfer) {
        if (CollectionUtils.isEmpty(allCanTransfer)) {
            return null;
        }

        ArrayList<FeederTransferCap> result = new ArrayList<>();

        for (List<FeederTransferCap> transferCaps : allCanTransfer) {
            if (CollectionUtils.isEmpty(transferCaps)) {
                continue;
            }
            // 将当前联络开关的多个操作 我们取其中一个
            // 先排序(两条线专供之后的差值越小越优先) 在提取第一个
            transferCaps.sort((trf1, trf2) -> {
                double val1 = Math.abs(trf1.getSourceChangeLoad() - trf1.getTransferChangeLoad());
                double val2 = Math.abs(trf2.getSourceChangeLoad() - trf2.getTransferChangeLoad());
                return Double.compare(val1, val2);
            });
            result.add(transferCaps.get(0));
        }
        return result;
    }

    /**
     * 查找当前线路可以放置的联络开关的位置，考虑因素：
     * 一、考虑主干上分支情况：
     * 1、主干分支上的负荷很少或者很多情况下、导致单主干不够转供合太多转供
     * 二、考虑各个分叉点情况：
     * 优点：这样可以考虑所有支路径情况，并且能优先大分子末端新增联络线 整个分支转供出去
     * 缺点：这种考虑不到 主路径上 某个分段内的很多小分支的情况
     * 三、最长主干路径逐个遍历
     * 优点：能保证路径上递归总能找到合适转供出去的
     * 缺点：不能考虑到大分子情况
     */
    ArrayList<BranchTransfer> findContactLay(FeederNtVo mainFeederNt, NodePath nodePath, double maxLoad) {
        // =========================== 考虑大分子 =======================

        maxLoad = 0.8;

        // 出问题的大分子
        // List<BranchNode> bigBranchList = bigBranchIdentify.getProblemBranchList(nodePath);
        List<BranchTransfer> branchTransfers = new ArrayList<>();

        // 遍历所有分支节点
        for (BranchNode branchNode : nodePath.getBranchAllNodeMap().values()) {
            // 判断和满足的
            List<Node> pbs = branchNode.getPbNodes();

            // 没有配变或者
            if (CollectionUtils.isEmpty(pbs)) {
                continue;
            }

            Double currentLoad = mainFeederNt.getHisMaxLoadRate();
            double totalPbCap = pbs.stream().mapToDouble(Node::getCap).sum();

            // 当前线路降低之后的负载率
            Double changeLoad = CalcLoadService.calcFeederLoad(mainFeederNt.getHisMaxLoadRate(), mainFeederNt.getFeederRateCapacity(), -totalPbCap);

            if (changeLoad < maxLoad && changeLoad > 0.0) {
                branchTransfers.add(new BranchTransfer(currentLoad, changeLoad, branchNode));
            }
        }
        if (CollectionUtils.isEmpty(branchTransfers)) {
            return null;
        }

        // 过滤一些太小的值 需要过滤掉最小值
        List<Double> minList = Arrays.asList(0.3, 0.25, 0.2, 0.15);
        for (Double min : minList) {
            List<BranchTransfer> tmps = branchTransfers.stream().filter(n -> n.getChangeLoad() >= min).collect(Collectors.toList());
            // 过滤之后还有值 那么就使用当前的
            if (CollectionUtils.isNotEmpty(tmps)) {
                branchTransfers = tmps;
            }
        }

        // =========================== 将出现的在同一路径的分组(路径完全重叠在另一个的) =======================

        // 电源起点到分叉路径截取组装
        HashMap<String, List<Node>> pathsMap = nodePath.subPathByEndPsrIds(null, branchTransfers.stream().map(n -> n.getBranchNode().getPsrId()).collect(Collectors.toList()));
        for (BranchTransfer branchTransfer : branchTransfers) {
            List<Node> paths = pathsMap.get(branchTransfer.getBranchNode().getPsrId());
            branchTransfer.setHeadPaths(paths);
        }
        // TODO 根据路径进行分组 判断两个路径之间 只要有互相完全重叠部分 那么就是同组
        List<List<BranchTransfer>> result = new ArrayList<>();
        // result.add(Arrays.asList(branchTransfers.get(0)));

        for (int i = 0; i < branchTransfers.size(); i++) {
            BranchTransfer sourceBraTrf = branchTransfers.get(i);
            // 先判断是已经否存在
            boolean exist = result.stream().anyMatch(btrList ->
                    btrList.stream().anyMatch(btr -> StringUtils.equals(btr.getId(), sourceBraTrf.getId()))
            );
            if (exist) {
                continue;
            }
            ArrayList<BranchTransfer> group = new ArrayList<>();
            // 逐个取遍历拼接在一起
            group.add(sourceBraTrf);
            // 组个比较进行分组
            for (int j = 0; j < branchTransfers.size(); j++) {
                BranchTransfer btr = branchTransfers.get(j);
                if (NodeUtils.equalPathSingleOverlap(sourceBraTrf.getHeadPaths(), btr.getHeadPaths())) {
                    group.add(btr);
                }
            }
            result.add(group);
        }


        // 同一路径组 里面取一个（优先有杆塔的和路径最长的）
        System.out.println(branchTransfers);
        return null;
    }


    /**
     * 获取可以专供联络线开关
     */
    private List<ContactFeederKg> getCanContactFeederKg(ZnapTopology topologyMap, List<FeederNtVo> contactFeederNts, double maxLoad) {

        // 这里过滤超过最大值的（联络线本身就已经负载不能在转至该线路）
        List<FeederNtVo> meetFeeders = contactFeederNts.stream().filter(n ->
                // 线路负载率和装机容量 不为空 并且负载率小于最大值
                n.getHisMaxLoadRate() != null && n.getFeederRateCapacity() != null && n.getHisMaxLoadRate() < maxLoad
        ).collect(Collectors.toList());

        List<ContactFeederKg> contactFeederKgs = topologyMap.getContactFeederKgs();

        return contactFeederKgs.stream().filter(n ->
                // 当前联络开关在满足的线路里
                meetFeeders.stream().anyMatch(f -> StringUtils.equals(f.getPsrId(), n.getFeederPsrId()))
        ).collect(Collectors.toList());
    }

    /**
     * 获取可以运放调整的所有组合
     */
    private CanRunAdjustFeeder getCanRunAdjust(FeederNtVo mainFeederNt, List<ContactFeederKg> contactFeederKgs, List<FeederNtVo> contactFeederNts, NodePath nodePath, double maxLoad) {

        Map<String, FeederNtVo> contactFeederNtMap = contactFeederNts.stream().collect(Collectors.toMap(FeederNtVo::getPsrId, d -> d));

        // 仅仅减少一部分的
        List<List<FeederTransferCap>> onlySubResult = new ArrayList<>();

        // 能直接满足的 当前线都减少小于最大 并且 联络新增的也小于最大
        List<List<FeederTransferCap>> allCanResult = new ArrayList<>();

        // 联络联络开关
        for (ContactFeederKg feederKg : contactFeederKgs) {

            List<FeederTransferCap> onlySubs = new ArrayList<>();
            List<FeederTransferCap> allCans = new ArrayList<>();

            // 当前开关可能组成的所有分段组合 并且计算
            List<SegBetween> contactKgAllSeg = nodePath.getContactKgAllSeg(feederKg.getKgPsrId());
            if (CollectionUtils.isEmpty(contactKgAllSeg)) {
                continue;
            }

            List<Node> beforePbs = null;

            // 将所有可能组成的分段组合 计算负载率的变化
            for (SegBetween segBetween : contactKgAllSeg) {
                // 这些时将要转供走的的配变
                List<Node> pbs = segBetween.getAllPb();

                // 没有配变或者
                if (CollectionUtils.isEmpty(pbs)) {
                    continue;
                }

                // 表示分段和上一个分段的配变一样我们就不需要相同的在重新操作
                if (beforePbs != null && beforePbs.size() == pbs.size()) {
                    continue;
                }

                // 联络线相关运行数据
                FeederNtVo contactFeederNt = contactFeederNtMap.get(feederKg.getFeederPsrId());

                FeederTransferCap feederTransferCap = calcFeederTransferCap(mainFeederNt, contactFeederNt, pbs);
                // 设置分位和合为开关
                feederTransferCap.setHePsrId(segBetween.getEndPsrId());
                feederTransferCap.setHePsrType(segBetween.getEndPsrType());
                feederTransferCap.setHePsrName(segBetween.getEndPsrName());
                feederTransferCap.setFenPsrId(segBetween.getStartPsrId());
                feederTransferCap.setFenPsrType(segBetween.getStartPsrType());
                feederTransferCap.setFenPsrName(segBetween.getStartPsrName());
                feederTransferCap.setPaths(segBetween.getMainOtherNodes());

                // 专供线路负载率小于最大 并且  当前线全转供的也不需要
                if (feederTransferCap.getTransferChangeLoad() < maxLoad && feederTransferCap.getSourceChangeLoad() > 0.0) {
                    // 表示当前线路和转供的联络线都满足了最大负载率
                    if (feederTransferCap.getSourceChangeLoad() < maxLoad) {
                        allCans.add(feederTransferCap);
                    } else {
                        onlySubs.add(feederTransferCap);
                    }
                }
                beforePbs = pbs;
            }

            if (!onlySubs.isEmpty()) {
                onlySubResult.add(onlySubs);
            }
            if (!allCans.isEmpty()) {
                allCanResult.add(allCans);
            }
        }

        return new CanRunAdjustFeeder(onlySubResult, allCanResult);
    }

    /**
     * 计算当前线路转供至另一条线路的负载率变化
     *
     * @param mainFeederNt    当前主线路
     * @param contactFeederNt 专供的联络线
     * @param removePbs       减少点陪伴集合
     * @return
     */
    private FeederTransferCap calcFeederTransferCap(FeederNtVo mainFeederNt, FeederNtVo contactFeederNt, List<Node> removePbs) {
        double totalPbCap = removePbs.stream().mapToDouble(Node::getCap).sum();
        // 当前线路降低之后的负载率
        Double subLoad = CalcLoadService.calcFeederLoad(mainFeederNt.getHisMaxLoadRate(), mainFeederNt.getFeederRateCapacity(), -totalPbCap);

        // 专供的线路上升的负载率
        Double addLoad = CalcLoadService.calcFeederLoad(contactFeederNt.getHisMaxLoadRate(), contactFeederNt.getFeederRateCapacity(), totalPbCap);

        FeederTransferCap result = new FeederTransferCap(mainFeederNt.getPsrId(), mainFeederNt.getName(), contactFeederNt.getPsrId(), contactFeederNt.getName());
        // 设置负载率更改
        result.setSourceLoad(mainFeederNt.getHisMaxLoadRate());
        result.setSourceChangeLoad(subLoad);

        result.setTransferLoad(contactFeederNt.getHisMaxLoadRate());
        result.setTransferChangeLoad(addLoad);
        return result;
    }

    /**
     * 获取末端放置联络线的所有集合
     */
//    private List<List<ArrayList<LayNodeBo>>> getLayPositionList() {
//        List<List<ArrayList<LayNodeBo>>> result = new ArrayList<>();
//
//    }

}
