package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.bo.PsrIdAndPsrType;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.vo.BranchMainPathBo;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.bo.NodeNumModelBo;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.identify.BigBranchIdentify;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.utils.BranchPathLayNodeUtil;
import com.ruoyi.service.znap.IZnapTopologyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 大分子无联络线网架方案生成
 * 解决措施：分支首位置添加一个首开开关，在分支末端添加联络线
 * （那条线都可以，但是优先考虑240线径的，240可以做主干路径）
 * （为什么不推荐中间呢：减少环网中的环流）
 */
@Service
@Slf4j
public class BigBranchPlan implements IGeneratePlan {

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ISingMapService singMapService;

    @Autowired
    BigBranchIdentify bigBranchIdentify;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) {

        // 在结尾末端
        planProcessService.pushLoadingProcess(problemId, "解析网架拓扑结构中");

        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();

        // znap拓扑节点构建
        Map<String, Node> nodeMap = topologyMap.getNodeMap();

        // 节点路径分析
        HashMap<String, BranchNode> branchNodeMap = nodePath.getBranchNodeMap();
        List<ArrayList<Node>> allNodePaths = nodePath.getAllNodePaths();

        planProcessService.pushLoadingProcess(problemId, "问题结构化中");

        // 获取当前方案对应的分支线
        BranchNode branchNode = segBranchService.getMainBranch(nodePath, deviceId);
        if (branchNode == null) {
            throw new RuntimeException("暂未获取当前的大分支分段，请确定数据正确性！");
        }

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, branchNode.getPbNodes());

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, branchNode.getPbNodes());


        // (3)、推送：问题释义
        pushPlanProcessService.pushBigExplain(problemId, bigBranchIdentify.getMinPbNum(), bigBranchIdentify.getMinCap());

        // 确定是 配变过多 还是 容量过大 或者 负荷 TODO 暂不考虑负荷过多
        List<Node> branchNodes = branchNode.getNodes();

        // (4)、推送：大分子识别
        pushPlanProcessService.pushBigBranchIdentify(problemId, branchNode);

        // 配变节点的容量加工
        baseGeneratePlan.processPbNodeCap(branchNodes);

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        // 大分支开始节点
        Node startBranchNode = nodeMap.get(branchNode.getPsrId());

        // 获取所有分支节点集合
        List<BranchMainPathBo> branchMainPathBos = getBranchPaths(branchNodes, startBranchNode, branchNodeMap, allNodePaths);

        // 计算放置开关或者联络线路径范围组合
        List<List<ArrayList<LayNodeBo>>> layPositionList = getLayPositionList(branchMainPathBos);

        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(layPositionList);

        try {
            processNodeService.handleLayOperateNode(problemId, layPositionList, token, feederId);
        } catch (Exception e) {
            log.error("生产方案异常！", e);
            throw new RuntimeException("生产方案异常！", e);
        }

        //  去空
        //  layPositionList = baseGeneratePlan.getFilterEmptyLayNodes(layPositionList);

        // TODO 去重

        // 所有的组合在一起
        List<List<ArrayList<LayNodeBo>>> combLayPos = baseGeneratePlan.getCombLayPos(layPositionList);

        planProcessService.pushLoadingProcess(problemId, "方案集合按照开关数和联络距离排序");

        // 排序
        baseGeneratePlan.combLayPosSort(combLayPos);

        // 每种方案的所有操作集合扁平化
        List<ArrayList<LayNodeBo>> sureLays = baseGeneratePlan.flatLayNode(combLayPos);

        HashMap<Long, ArrayList<LayNodeBo>> planLaysMap = new HashMap<>();

        // 生成方案
        List<Plan> plans = baseGeneratePlan.layToPlans(sureLays, problemId, planLaysMap);

        // (7)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, sureLays);

        // 提取前三条方案
        List<Plan> resultPlans = plans.stream().limit(3).collect(Collectors.toList());

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, resultPlans, planLaysMap);

        // 获取各个分支末端
        return resultPlans;
    }

    // 当前分叉节点  转为分叉节点数值
    public List<NodeNumModelBo> toNodeNums(List<Node> edges, Node node, HashMap<String, BranchNode> branchNodeMap) {
        List<NodeNumModelBo> nodeNums = edges.stream().map(edge -> {
            String branchId = BranchNode.processId(node, edge);
            BranchNode branchNode = branchNodeMap.get(branchId);
            if (branchNode == null) {
                return null;
            }
            NodeNumModelBo result = new NodeNumModelBo(branchNode.getPbNodeNum(), node, branchNode);
            result.setNextNode(edge);
            List<Node> pbNodes = branchNode.getPbNodes();
            // 容量值
            if (result != null) {
                double sum = pbNodes.stream().mapToDouble(Node::getCap).sum();
                result.setTotalCap(sum);
                result.setBranchNode(branchNode);
            }
            return result;
        }).filter(n -> n != null).collect(Collectors.toList());

        // 从大到小排序
        nodeNums.sort((n1, n2) -> n2.getNum() - n1.getNum());
        return nodeNums;
    }

    /**
     * 获取所有的分支路径组合
     */
    public List<BranchMainPathBo> getBranchPaths(List<Node> nodes, Node startNode, HashMap<String, BranchNode> branchNodeMap, List<ArrayList<Node>> allNodePaths) {
        // 当前线路的下一个节点
        List<Node> edges = startNode.getEdges();
        Node nextEdge = null;
        for (Node edge : edges) {
            if (nodes.stream().anyMatch(n -> n.equals(edge.getId()))) {
                nextEdge = edge;
                break;
            }
        }

        // 条件获取所有的分支
        return BranchPathLayNodeUtil.conditionSplitPaths(startNode, branchNodeMap, nextEdge, allNodePaths, this::toNodeNums, this::judgeNodeNumModel);
    }

    /**
     * 获取末端放置联络线的所有集合
     */
    private List<List<ArrayList<LayNodeBo>>> getLayPositionList(List<BranchMainPathBo> branchMainPathBos) {
        // 使用默认排序再做放置节点数值 并且我们取前三条
        return BranchPathLayNodeUtil.getLayPositionList(branchMainPathBos, null, 3);
    }

    // 判断当前数字模型节点是否超过有问题的
    private boolean judgeNodeNumModel(NodeNumModelBo nodeNum) {
        int pbNodeNum = nodeNum.getNum();
        double pbNodeCap = nodeNum.getTotalCap();
        return bigBranchIdentify.isExceed(pbNodeNum, pbNodeCap);
    }

}
