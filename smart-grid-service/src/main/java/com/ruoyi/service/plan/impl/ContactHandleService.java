package com.ruoyi.service.plan.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.plan.vo.BranchMainPathBo;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.bo.NodeNumModelBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.calc.measurement.CalcLoadService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.ToNodeNumsFunc;
import com.ruoyi.service.plan.model.CanRunAdjustFeeder;
import com.ruoyi.service.plan.model.contact.BranchTransfer;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ContactHandleService {

    @Autowired
    IConditionService iConditionService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    /**
     * 获取可以转供联络线开关
     */
    public List<ContactFeederKg> getCanContactFeederKg(ZnapTopology topologyMap, List<FeederNtVo> contactFeederNts, double maxLoad) {

        // 这里过滤超过最大值的（联络线本身就已经负载不能在转至该线路）
        List<FeederNtVo> meetFeeders = filterLimitFeederNt(contactFeederNts, maxLoad);

        List<ContactFeederKg> contactFeederKgs = topologyMap.getContactFeederKgs();

        return contactFeederKgs.stream().filter(n ->
                // 当前联络开关在满足的线路里
                meetFeeders.stream().anyMatch(f -> StringUtils.equals(f.getPsrId(), n.getFeederPsrId()))
        ).collect(Collectors.toList());
    }

    /**
     * 获取可以运放调整的所有组合
     */
    public CanRunAdjustFeeder getCanRunAdjust(FeederNtVo mainFeederNt, List<ContactFeederKg> contactFeederKgs, List<FeederNtVo> contactFeederNts, NodePath nodePath, double maxLoad) {

        Map<String, FeederNtVo> contactFeederNtMap = contactFeederNts.stream().collect(Collectors.toMap(FeederNtVo::getPsrId, d -> d));

        // 仅仅减少一部分的
        List<List<FeederTransferCap>> onlySubResult = new ArrayList<>();

        // 能直接满足的 当前线都减少小于最大 并且 联络新增的也小于最大
        List<List<FeederTransferCap>> allCanResult = new ArrayList<>();

        // 联络联络开关
        for (ContactFeederKg feederKg : contactFeederKgs) {

            List<FeederTransferCap> onlySubs = new ArrayList<>();
            List<FeederTransferCap> allCans = new ArrayList<>();

            // 当前开关可能组成的所有分段组合 并且计算
            List<SegBetween> contactKgAllSeg = nodePath.getContactKgAllSeg(feederKg.getKgPsrId());
            if (CollectionUtils.isEmpty(contactKgAllSeg)) {
                continue;
            }

            List<Node> beforePbs = null;

            // 将所有可能组成的分段组合 计算负载率的变化
            for (SegBetween segBetween : contactKgAllSeg) {
                // 这些时将要转供走的的配变
                List<Node> pbs = segBetween.getAllPb();

                // 没有配变或者
                if (CollectionUtils.isEmpty(pbs)) {
                    continue;
                }

                // 表示分段和上一个分段的配变一样我们就不需要相同的在重新操作
                if (beforePbs != null && beforePbs.size() == pbs.size()) {
                    continue;
                }

                // 联络线相关运行数据
                FeederNtVo contactFeederNt = contactFeederNtMap.get(feederKg.getFeederPsrId());

                FeederTransferCap feederTransferCap = calcFeederTransferCap(mainFeederNt, contactFeederNt, pbs);
                // 设置分位和合为开关
                feederTransferCap.setHePsrId(segBetween.getEndPsrId());
                feederTransferCap.setHePsrType(segBetween.getEndPsrType());
                feederTransferCap.setHePsrName(segBetween.getEndPsrName());
                feederTransferCap.setFenPsrId(segBetween.getStartPsrId());
                feederTransferCap.setFenPsrType(segBetween.getStartPsrType());
                feederTransferCap.setFenPsrName(segBetween.getStartPsrName());
                feederTransferCap.setPaths(segBetween.getMainOtherNodes());

                // 专供线路负载率小于最大 并且  当前线全转供的也不需要
                if (feederTransferCap.getTransferChangeLoad() < maxLoad && feederTransferCap.getSourceChangeLoad() > 0.0) {
                    // 表示当前线路和转供的联络线都满足了最大负载率
                    if (feederTransferCap.getSourceChangeLoad() < maxLoad) {
                        allCans.add(feederTransferCap);
                    } else {
                        onlySubs.add(feederTransferCap);
                    }
                }
                beforePbs = pbs;
            }

            if (!onlySubs.isEmpty()) {
                onlySubResult.add(onlySubs);
            }
            if (!allCans.isEmpty()) {
                allCanResult.add(allCans);
            }
        }

        return new CanRunAdjustFeeder(onlySubResult, allCanResult);
    }

    /**
     * 电流流过的供电路径（电流流过的节点集合）
     *
     * @param heNode   合闸刀联络开关节点
     * @param nextNode 下一个边  主要为了辨别方向
     * @param fenNodes 分闸开关节点集合
     */
    public List<Node> powerSupplyNodes(Node heNode, Node nextNode, List<Node> fenNodes) {
        ArrayList<Node> result = new ArrayList<>();
        NodeUtils.loopNode(heNode, new HashMap<>(), Arrays.asList(nextNode), node -> {
            result.add(node);
            if (CollectionUtils.isNotEmpty(fenNodes) && fenNodes.stream().anyMatch(n -> n.equals(node.getId()))) {
                return true;
            }
            return false;
        });
        return result;
    }

    /**
     * 计算当前线路转供至另一条线路的负载率变化
     *
     * @param mainFeederNt    当前主线路
     * @param contactFeederNt 专供的联络线
     * @param removePbs       减少点陪伴集合
     * @return
     */
    public FeederTransferCap calcFeederTransferCap(FeederNtVo mainFeederNt, FeederNtVo contactFeederNt, List<Node> removePbs) {
        double totalPbCap = removePbs.stream().mapToDouble(Node::getCap).sum();
        // 当前线路降低之后的负载率
        Double subLoad = CalcLoadService.calcFeederLoad(mainFeederNt.getHisMaxLoadRate(), mainFeederNt.getFeederRateCapacity(), -totalPbCap);

        // 专供的线路上升的负载率
        Double addLoad = CalcLoadService.calcFeederLoad(contactFeederNt.getHisMaxLoadRate(), contactFeederNt.getFeederRateCapacity(), totalPbCap);

        FeederTransferCap result = new FeederTransferCap(mainFeederNt.getPsrId(), mainFeederNt.getName(), contactFeederNt.getPsrId(), contactFeederNt.getName());
        // 设置负载率更改
        result.setSourceLoad(mainFeederNt.getHisMaxLoadRate());
        result.setSourceChangeLoad(subLoad);

        result.setTransferLoad(contactFeederNt.getHisMaxLoadRate());
        result.setTransferChangeLoad(addLoad);
        return result;
    }

    /**
     * 查找当前线路可以放置的联络开关的位置，考虑因素：
     * 一、考虑主干上分支情况：
     * 1、主干分支上的负荷很少或者很多情况下、导致单主干不够转供合太多转供
     * 二、考虑各个分叉点情况：
     * 优点：这样可以考虑所有支路径情况，并且能优先大分子末端新增联络线 整个分支转供出去
     * 缺点：这种考虑不到 主路径上 某个分段内的很多小分支的情况
     * 三、最长主干路径逐个遍历
     * 优点：能保证路径上递归总能找到合适转供出去的
     * 缺点：不能考虑到大分子情况
     */
    public List<List<BranchTransfer>> findContactLay(FeederNtVo mainFeederNt, NodePath nodePath, double maxLoad) {
        // =========================== 考虑大分子 =======================

        // 出问题的大分子
        // List<BranchNode> bigBranchList = bigBranchIdentify.getProblemBranchList(nodePath);
        List<BranchTransfer> branchTransfers = new ArrayList<>();

        // 遍历所有分支节点
        for (BranchNode branchNode : nodePath.getBranchAllNodeMap().values()) {
            // 判断和满足的
            List<Node> pbs = branchNode.getPbNodes();

            // 没有配变或者
            if (CollectionUtils.isEmpty(pbs)) {
                continue;
            }

            Double currentLoad = mainFeederNt.getHisMaxLoadRate();
            double totalPbCap = pbs.stream().mapToDouble(Node::getCap).sum();

            // 当前线路降低之后的负载率
            Double changeLoad = CalcLoadService.calcFeederLoad(mainFeederNt.getHisMaxLoadRate(), mainFeederNt.getFeederRateCapacity(), -totalPbCap);

            if (changeLoad < maxLoad && changeLoad > 0.0) {
                branchTransfers.add(new BranchTransfer(currentLoad, changeLoad, branchNode));
            }
        }
        if (CollectionUtils.isEmpty(branchTransfers)) {
            return null;
        }

        // 过滤一些太小的值 需要过滤掉最小值
        List<Double> minList = Arrays.asList(0.3, 0.25, 0.2, 0.15);
        for (Double min : minList) {
            List<BranchTransfer> tmps = branchTransfers.stream().filter(n -> n.getChangeLoad() >= min).collect(Collectors.toList());
            // 过滤之后还有值 那么就使用当前的
            if (CollectionUtils.isNotEmpty(tmps)) {
                branchTransfers = tmps;
            }
        }

        // =========================== 将出现的在同一路径的分组(路径完全重叠在另一个的) =======================

        // 电源起点到分叉路径截取组装
        HashMap<String, List<Node>> pathsMap = nodePath.subPathByEndPsrIds(null, branchTransfers.stream().map(n -> n.getBranchNode().getNextPsrId()).collect(Collectors.toList()));
        for (BranchTransfer branchTransfer : branchTransfers) {
            List<Node> paths = pathsMap.get(branchTransfer.getBranchNode().getNextPsrId());
            branchTransfer.setHeadPaths(paths);
        }
        // TODO 根据路径进行分组 判断两个路径之间 只要有互相完全重叠部分 那么就是同组
        List<List<BranchTransfer>> result = new ArrayList<>();

        for (int i = 0; i < branchTransfers.size(); i++) {
            BranchTransfer sourceBraTrf = branchTransfers.get(i);
            // 先判断是已经否存在
            boolean exist = result.stream().anyMatch(btrList ->
                    btrList.stream().anyMatch(btr -> StringUtils.equals(btr.getId(), sourceBraTrf.getId()))
            );
            if (exist) {
                continue;
            }
            ArrayList<BranchTransfer> group = new ArrayList<>();
            // 逐个取遍历拼接在一起
            group.add(sourceBraTrf);
            // 组个比较进行分组
            for (int j = 0; j < branchTransfers.size(); j++) {
                BranchTransfer btr = branchTransfers.get(j);
                if (NodeUtils.equalPathSingleOverlap(sourceBraTrf.getHeadPaths(), btr.getHeadPaths())) {
                    group.add(btr);
                }
            }
            result.add(group);
        }

        // 同一路径组 里面取一个（优先有杆塔的和路径最长的）
        System.out.println(branchTransfers);
        return result;
    }

    /**
     * 过滤掉重过载线路
     */
    public List<DeviceFeeder> filterOverLoadFeeder(List<DeviceFeeder> feederList) {
        double maxLoad = (double) iConditionService.lineOverloadNum() / 100;   // 当前配置最大负载率

        List<FeederNtVo> feederNtVos = feederDeviceMapper.selectFeederNtsByFeederIds(feederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));

        // 过滤掉重过载
        List<FeederNtVo> meetFeeders = filterLimitFeederNt(feederNtVos, maxLoad);

        return feederList.stream().filter(n ->
                // 当前线路在满足的线路里
                meetFeeders.stream().anyMatch(f -> StringUtils.equals(f.getPsrId(), n.getPsrId()))
        ).collect(Collectors.toList());
    }

    /**
     * 过滤不重过载的线路集合
     *
     * @param feederNtVos 线路最大量测对象
     * @param maxLoad     最大重过载
     */
    public List<FeederNtVo> filterLimitFeederNt(List<FeederNtVo> feederNtVos, double maxLoad) {
        return feederNtVos.stream().filter(n ->
                // 线路负载率和装机容量 不为空 并且负载率小于最大值
                n.getHisMaxLoadRate() != null && n.getFeederRateCapacity() != null && n.getHisMaxLoadRate() < maxLoad
        ).collect(Collectors.toList());
    }

//    /**
//     * 给定一个开始联络的节点查找联络所提供 供电段所有路径
//     */
//    private static void flowPastNodes(Node startNode, List<Node> result, Map<String, Boolean> useNodeMap, Node nextEdge, List<Node> stopNodes) {
//
//        Node currentNode = startNode;
//
//        while (currentNode != null) {
//            result.add(currentNode);
//            useNodeMap.put(currentNode.getId(), true);
//            // 如果遇到终止的 那么就停止
//            Node finalCurrentNode = currentNode;
//            if (CollectionUtils.isNotEmpty(stopNodes) && stopNodes.stream().anyMatch(n -> n.equals(finalCurrentNode))) {
//                break;
//            }
//
//            Node nextNode = null;
//            List<Node> edges = null;
//
//            if (currentNode.isEdge()) { // 边
//                Node source = currentNode.getLink(true);
//                Node target = currentNode.getLink(false);
//
//                // 下一个节点
//                nextNode = source == null || useNodeMap.containsKey(source.getId()) ? target : source;
//
//                if (nextNode == null) {
//                    edges = currentNode.getEdges();
//                }
//            } else { // 设备节点
//                // 当前设备有那些边 我们继续往下递归
//                edges = currentNode.getEdges();
//            }
//
//            if (edges != null) {
//                // 过滤仅仅需要的下一个边
//                if (nextEdge != null) {
//                    Node tmpNode = nextEdge;
//                    // 在当前的边里面
//                    if (edges.stream().anyMatch(n -> n.equals(tmpNode.getId()))) {
//                        edges = Collections.singletonList(nextEdge);
//                    }
//                }
//
//                // 过滤已经存在遍历过的路径
//                edges = edges.stream().filter(n -> !useNodeMap.containsKey(n.getId())).collect(Collectors.toList());
//
//                for (Node edge : edges) {
//                    flowPastNodes(edge, result, useNodeMap, null, stopNodes);
//                }
//                nextNode = null;
//            }
//            currentNode = nextNode;
//        }
//    }

}
