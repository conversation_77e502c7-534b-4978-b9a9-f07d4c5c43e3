package com.ruoyi.service.plan.model.contact;

import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class BranchTransfer {
    public BranchTransfer() {
        this.id = UUID.randomUUID().toString();
    }

    public BranchTransfer(double load, double changeLoad, BranchNode branchNode) {
        this.id = UUID.randomUUID().toString();
        this.load = load;
        this.changeLoad = changeLoad;
        this.branchNode = branchNode;
    }

    /**
     * 唯一ID
     */
    String id;

    /**
     * 原线路负载率
     */
    double load;

    /**
     * 专供之后原线路的负载率
     */
    double changeLoad;

    /**
     * 所处的分支
     */
    BranchNode branchNode;

    /**
     * 电源开关到当前分支点的路径
     */
    List<Node> headPaths;
}
