package com.ruoyi.service.plan.processNode.contactHandle;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.DeviceHWG;
import com.ruoyi.entity.device.StationKg;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.device.DeviceHwgMapper;
import com.ruoyi.mapper.device.KgMapper;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.*;
import com.ruoyi.util.coordinates.CoordinateConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 间隔出线的处理
 */
@Component
public class BayContactHandle extends ContactProcess implements IContactHandle {

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    KgMapper kgMapper;

    @Autowired
    DeviceHwgMapper deviceHwgMapper;

    @Override
    public void handleContact(Long problemId, List<ProcessContactBo> processContacts, List<DeviceFeeder> feederList, String token) {

        // 获取所有打断节点新增环网柜对象

        List<BayContactBo> bayList = new ArrayList<>();
        HashMap<String, Boolean> useBayMap = new HashMap<>();

        // 开关坐标集合
        List<double[]> coords = new ArrayList<>();
        // 联络开关节点集合
        List<Node> contactNodes = new ArrayList<>();
        // 临时已使用开关集合
        HashMap<String, Node> tmpUseNode = new HashMap<>();
        for (ProcessContactBo processContact : processContacts) {
            HwgBayBo hwgBay = processContact.getHwgBay();
            BayContactBo bayContact = new BayContactBo(hwgBay, processContact);

            // 获取设置备用间隔设备的开关
            processBayContact(bayContact, hwgBay, useBayMap);

            // 必须有坐标
            if (bayContact.getBayContactPoint() != null) {
                Node node = bayContact.getBayContactNode();

                if (!tmpUseNode.containsKey(node.getPsrId())) {
                    contactNodes.add(node);
                    coords.add(bayContact.toBayContactLngLat());
                    tmpUseNode.put(node.getPsrId(), node);
                }

                bayList.add(bayContact);
            }
        }

        // 当前坐标点附近线路
        NearFeedersBo nearFeeders = getNearFeeders(coords, contactNodes, feederList);

        // 每个设备都对应相关联的联络对象集合
        Map<String, List<ProcessContactVo>> nodeToContactFeederMap = processContactHandle(contactNodes, nearFeeders, ProcessContactBo.STATION_BAY, token);

        // 加装
        for (BayContactBo bayContact : bayList) {
            ProcessContactBo processContact = bayContact.getProcessContact();
            Node bayContactNode = bayContact.getBayContactNode();
            List<ProcessContactVo> processContactVos = nodeToContactFeederMap.get(bayContactNode.getPsrId());

            if (CollectionUtils.isNotEmpty(processContactVos)) {
                processContact.setProcessContacts(processContactVos);
            }
        }
    }

    /**
     * 杆塔获取联络线
     *
     * @param layNodeBo      插入位置实体类
     * @param processContact 加工后的联络节点
     * @param usedContact    已经使用的联络
     */
    @Override
    public ProcessContactVo getContact(LayNodeBo layNodeBo, ProcessContactBo processContact, HashMap<String, ProcessContactVo> usedContact, boolean isAddMainKg) {


        List<ProcessContactVo> processContacts = processContact.getProcessContacts();

        if (CollectionUtils.isEmpty(processContacts)) {
            return null;
        }

        List<ProcessContactVo> allContacts = new ArrayList<>();

        ProcessContactVo result = null;

        // 过滤已经最终使用的
        List<ProcessContactVo> tmpPContacts = processContacts.stream().filter(n -> !usedContact.containsKey(n.getEndPsrId())).collect(Collectors.toList());

        // 生成联络路径  我们这里临时取第0条
        if (tmpPContacts.isEmpty()) {
            result = processContacts.get(0).clone();
        } else {
            allContacts.add(tmpPContacts.get(0).clone());
            result = tmpPContacts.get(0);
        }

        if (result != null) {
            String gtId = result.getEndPsrId();
            if (!usedContact.containsKey(gtId)) {
                usedContact.put(gtId, result);
            }
        }

        return result;
    }

    /**
     * 加装设置具体要设置的间隔节点
     */
    void processBayContact(BayContactBo bayContact, HwgBayBo hwgBay, HashMap<String, Boolean> useBayMap) {
        List<Node> bayNodes = hwgBay.getBayNodes();
        Node hwg = hwgBay.getHwg();
        if (CollectionUtils.isEmpty(bayNodes)) {
            return;
        }

        // 加装站内开关（间隔设备）
        List<StationKg> stationKgs = kgMapper.selectStationKgByPsrIds(bayNodes.stream().map(Node::getPsrId).collect(Collectors.toList()));

        // 过滤空坐标
        stationKgs = stationKgs.stream().filter(StationKg::isNotEmptyLngLat).collect(Collectors.toList());

        // 空的坐标
        if (CollectionUtils.isEmpty(stationKgs)) {
            // 使用环网柜
            DeviceHWG deviceHwg = deviceHwgMapper.selectByPsrId(hwg.getPsrId());
            if (deviceHwg != null && StringUtils.isNotBlank(deviceHwg.getGeoPositon())) {
                List<Double> doubles = CoordinateConverter.parseCommaCoordinates(deviceHwg.getGeoPositon(), ",");

                bayContact.setBayContactNode(bayNodes.get(0));
                bayContact.setBayContactPoint(CoordinateConverter.toPoint(doubles));
            }
        } else {
            // 取第0个即可
            StationKg stationKg = stationKgs.get(0);

            // 过滤已经使用的
            List<StationKg> notUseKgs = stationKgs.stream().filter(n -> !useBayMap.containsKey(n.getPsrId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notUseKgs)) {
                stationKg = notUseKgs.get(0);
            }
            StationKg tmp = stationKg;
            Node node = NodeUtils.findNode(bayNodes, n -> StringUtils.equals(tmp.getPsrId(), n.getPsrId()));
            Point point = CoordinateConverter.toPoint(stationKg.getLongitude(), stationKg.getLatitude());

            if (node != null) {
                // 重新复制一个新的节点 以防当前节点关联之前很多的节点
                node = node.clone();

                useBayMap.put(node.getPsrId(), true);
                node.setGeometry(point);
                bayContact.setBayContactNode(node);
                bayContact.setBayContactPoint(point);
            }
        }

        Node bayContactNode = bayContact.getBayContactNode();
        //  设置父级节点 环网柜
        if (bayContact != null) {
            bayContactNode.setParentWhg(hwg.getPsrId(), hwg.getPsrType(), hwg.getPsrName());
        }
    }
}
