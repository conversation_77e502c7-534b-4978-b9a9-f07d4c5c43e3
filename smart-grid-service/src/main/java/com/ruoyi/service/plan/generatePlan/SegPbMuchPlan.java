package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.vo.BranchMainPathBo;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.model.SegPbMuchPlan.SegLayPathBo;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.bo.NodeNumModelBo;
import com.ruoyi.mapper.device.DeviceRunTowerMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.problem.ProblemRuleMapper;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.utils.BranchPathLayNodeUtil;
import com.ruoyi.service.plan.utils.SegPathLayNodeUtil;
import com.ruoyi.service.znap.IZnapTopologyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 生成分段内配变数量不合理方案
 */
@Component
@Slf4j
public class SegPbMuchPlan implements IGeneratePlan {

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    ProblemRuleMapper problemRuleMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    ISingMapService singMapService;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) {

        //  planProcessService.pushLoadingProcess(problemId, "解析网架拓扑结构中");

        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        List<Node> kgContactNodes = topologyMap.getKgContactNodes();

        // 节点路径分析
        //   planProcessService.pushLoadingProcess(problemId, "问题结构化中");

        // 主干分段对象
        SegBetween segBetween = segBranchService.getMainSegBetween(nodePath, deviceId, kgContactNodes);

        if (segBetween == null) {
            throw new RuntimeException("暂未获取当前的分段，请确定数据正确性！");
        }

        // 分支对象
        HashMap<String, BranchNode> branchNodeMap = nodePath.getBranchNodeMap();
        List<Node> betweenNodes = segBetween.getBetweenNodes(true);
        List<Node> allPb = segBetween.getAllPb();

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, allPb);

        // 最大限制
        int maxNums = iConditionService.unreasonablePBNum(deviceFeeder.getSupplyArea()); // iConditionService.unreasonablePBNum(deviceFeeder.getSupplyArea());

        // (3)、推送：问题释义
        pushPlanProcessService.pushExplain(problemId, maxNums);

        // (4)、推送：分段识别
        pushPlanProcessService.pushSegIdentify(problemId, segBetween);

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        // 主干路径
        List<SegLayPathBo> mainPathsBos = getSegLayPaths(betweenNodes, branchNodeMap, maxNums);

        // 计算放置开关或者联络线路径范围组合
        List<List<ArrayList<LayNodeBo>>> layPositionList = getLayPositionList(mainPathsBos, branchNodeMap, maxNums, nodePath.getAllNodePaths());

        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(layPositionList);

        //  planProcessService.pushLoadingProcess(problemId, "方案加装开关或者联络中");
        //  临时只取第一个方案的
//            List<ArrayList<LayNodeBo>> collect = layPositionList.get(0).stream().limit(1).collect(Collectors.toList());
//            List<LayNodeBo> collect1 = collect.get(0).stream().limit(1).collect(Collectors.toList());
//            collect.remove(0);
//            collect.add(0, new ArrayList<>(collect1));
//            layPositionList.remove(0);
//            layPositionList.add(0, collect);

        // 处理放在位置操作节点

        // 加装开关和联络线
        try {
            processNodeService.handleLayOperateNode(problemId, layPositionList, token, feederId);
        } catch (Exception e) {
            log.error("生产方案异常！", e);
            throw new RuntimeException("生产方案异常！", e);
        }

        // 所有的组合在一起
        List<List<ArrayList<LayNodeBo>>> combLayPos = baseGeneratePlan.getCombLayPos(layPositionList);

        // 排序
        baseGeneratePlan.combLayPosSort(combLayPos);

        // 每种方案的所有操作集合扁平化
        List<ArrayList<LayNodeBo>> sureLays = baseGeneratePlan.flatLayNode(combLayPos);

        HashMap<Long, ArrayList<LayNodeBo>> planLaysMap = new HashMap<>();
        // 生成方案
        List<Plan> plans = baseGeneratePlan.layToPlans(sureLays, problemId, planLaysMap);

        // (7)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, sureLays);

        // TODO 方案排序

        // TODO 分别提取最好的前三条方案（我们这里优先联络线的距离，后续还要考虑多个约束条件）
      //  boolean isOnlyMain = mainPathsBos.size() == 1 && mainPathsBos.get(0).isSegPath();
        List<Plan> resultPlans = new ArrayList<>();
//        if (isOnlyMain) {
//            if (!plans.isEmpty()) {
//                // resultPlans = plans.stream().limit(3).collect(Collectors.toList());
//                resultPlans.add(plans.get(0));
//                int contactIndex = plans.size() / 2;
//                for (int i = contactIndex; i < contactIndex + 2; i++) {
//                    if (plans.size() > i) {
//                        resultPlans.add(plans.get(i));
//                    }
//                }
//            }
//        } else {
        resultPlans = plans.stream().limit(3).collect(Collectors.toList());
        //  }

        // TODO 过滤负载率高的线路 以及 专供过去 必须保持负载率通过

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, resultPlans, planLaysMap);

        System.out.println(layPositionList);
        System.out.println(combLayPos);
        return resultPlans; // plans
    }


    /**
     * 获取需要递归查找分段的路径
     * 按道理当前主干路径即可，但是 有一种特殊的线路 当前主干线路的大分子配变数量超过最大限定值，那么该大分子需要末端新增联络线
     */
    private List<SegLayPathBo> getSegLayPaths(List<Node> betweenNodes, HashMap<String, BranchNode> branchNodeMap, int maxNums) {

        List<SegLayPathBo> results = new ArrayList<>();

        ArrayList<Node> nodes = new ArrayList<>();

        for (int i = 0; i < betweenNodes.size(); i++) {
            Node node = betweenNodes.get(i);
            List<Node> edges = node.isEdge() ? null : node.getEdges();
            nodes.add(node);
            // 杆塔
            if (node.isPole()) {
                // 杆塔上可能会有分支线 && i < betweenNodes.size() - 1
                if (edges != null && edges.size() > 2) {
                    // 过滤已经主干边 其它的分叉边
                    List<Node> branchEdges = edges.stream().filter(n -> !betweenNodes.contains(n)).collect(Collectors.toList());
                    List<NodeNumModelBo> nodeNums = toNodeNums(branchEdges, node, branchNodeMap);
                    for (NodeNumModelBo nodeNum : nodeNums) {
                        // 单个分支超过最大数量
                        if (judgeNodeNumModel(nodeNum, maxNums)) {
                            results.add(SegLayPathBo.createBranchPath(nodeNum.getNode(), nodeNum.getNextNode()));
                        }
                    }
                }
            }
        }

        results.add(SegLayPathBo.createSegPath(nodes));
        return results;
    }

    /**
     * 获取放置节点集合
     */
    private List<List<ArrayList<LayNodeBo>>> getLayPositionList(List<SegLayPathBo> mainPathsBos, HashMap<String, BranchNode> branchNodeMap, int maxNum, List<ArrayList<Node>> allNodePaths) {
        List<List<ArrayList<LayNodeBo>>> result = new ArrayList<>();

        // 主干路路径放在分段开关或者联络线
        List<SegLayPathBo> mainSegPathLays = mainPathsBos.stream().filter(SegLayPathBo::isSegPath).collect(Collectors.toList());

        // 大分子路径
        List<SegLayPathBo> branchPathLays = mainPathsBos.stream().filter(n -> !n.isSegPath()).collect(Collectors.toList());

        // =========================== 主干分段开关组合生成 =======================
        // 分支线的开始边集合
        List<Node> branchStartEdges = new ArrayList<>();
        for (SegLayPathBo branchPathLay : branchPathLays) {
            Node branchNextNode = branchPathLay.getBranchNextNode();
            if (branchNextNode != null) {
                branchStartEdges.add(branchNextNode);
            }
        }

        List<List<ArrayList<LayNodeBo>>> segLayPositions = SegPathLayNodeUtil.getLayPositionList(mainSegPathLays, branchNodeMap, maxNum, branchStartEdges);
        result.addAll(segLayPositions);

        // =========================== 大分子末端组合 =======================
        for (SegLayPathBo branchPathLay : branchPathLays) {
            Node startNode = branchPathLay.getBranchStartNode();
            Node nextEdge = branchPathLay.getBranchNextNode();

            // 当前条件条件路径获取所有的分支
            List<BranchMainPathBo> branchMainPathBos = BranchPathLayNodeUtil.conditionSplitPaths(startNode, branchNodeMap, nextEdge, allNodePaths, this::toNodeNums, (nodeNum) -> judgeNodeNumModel(nodeNum, maxNum));

            List<List<ArrayList<LayNodeBo>>> layPositionLists = BranchPathLayNodeUtil.getLayPositionList(branchMainPathBos, null, 3);
            result.addAll(layPositionLists);
        }

        result = result.stream().filter(n -> !n.isEmpty()).collect(Collectors.toList());

        return result;
    }

    // 当前分叉节点 将各个分叉边 转为分叉节点数值
    public List<NodeNumModelBo> toNodeNums(List<Node> edges, Node node, HashMap<String, BranchNode> branchNodeMap) {
        List<NodeNumModelBo> nodeNums = edges.stream().map(edge -> {
            String branchId = BranchNode.processId(node, edge);
            BranchNode branchNode = branchNodeMap.get(branchId);
            NodeNumModelBo nodeNumModelBo = new NodeNumModelBo(branchNode.getPbNodeNum(), node, edge);
            nodeNumModelBo.setBranchNode(branchNode);
            return nodeNumModelBo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 从大到小排序
        nodeNums.sort((n1, n2) -> n2.getNum() - n1.getNum());
        return nodeNums;
    }

    // 判断当前数字模型节点是否超过有问题的
    private boolean judgeNodeNumModel(NodeNumModelBo nodeNum, int maxNum) {
        int pbNodeNum = nodeNum.getNum();
        return pbNodeNum >= maxNum;
    }

    HashMap<String, Double> getTotalScope(ArrayList<LayNodeBo> layNodeBos) {
        double total = 0;
        double contactLength = 0;
        for (LayNodeBo layNodeBo : layNodeBos) {
            total += layNodeBo.getScope();
            contactLength += layNodeBo.getContactLength();
        }
        double finalTotal = total;
        double finalContactLength = contactLength;
        return new HashMap<String, Double>() {{
            put("total", finalTotal);
            put("contactLength", finalContactLength);
        }};
    }
}
