package com.ruoyi.service.plan.model;

import com.ruoyi.entity.calc.FeederTransferCap;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 能运放调整开关操作组合
 */
@Data
public class CanRunAdjustFeeder {
    public CanRunAdjustFeeder() {
    }

    public CanRunAdjustFeeder(List<List<FeederTransferCap>> onlySubResult, List<List<FeederTransferCap>> allCanResult) {
        this.onlySubResult = onlySubResult;
        this.allCanResult = allCanResult;
    }

    // 仅仅减少一部分的
    List<List<FeederTransferCap>> onlySubResult = new ArrayList<>();

    // 能直接满足的 当前线都减少小于最大 并且 联络新增的也小于最大
    List<List<FeederTransferCap>> allCanResult = new ArrayList<>();
}
