package com.ruoyi.service.plan.utils;

import com.ruoyi.entity.plan.vo.BranchMainPathBo;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.bo.NodeNumModelBo;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.ToNodeNumsFunc;
import com.ruoyi.service.plan.model.HwgBayBo;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大分子路径放置分段开关或者联络线工具类
 */
public class BranchPathLayNodeUtil {

    /**
     * 获取末端放置联络线的所有集合
     */
    public static List<List<ArrayList<LayNodeBo>>> getLayPositionList(List<BranchMainPathBo> branchMainPathBos, Function<List<List<Node>>, Void> sortFunc, int maxSize) {

        List<List<ArrayList<LayNodeBo>>> result = new ArrayList<>();

        for (BranchMainPathBo branchMainPathBo : branchMainPathBos) {
            // 所有的分支线
            List<List<Node>> branchPaths = branchMainPathBo.getBranchPaths();

            List<ArrayList<LayNodeBo>> planLays = getLayPosition(branchPaths, sortFunc, maxSize);
            result.add(planLays);
        }

        return result;
    }

    static List<ArrayList<LayNodeBo>> getLayPosition(List<List<Node>> branchPaths, Function<List<List<Node>>, Void> sortFunc, int maxSize) {

        // 排序
        if (sortFunc != null) {
            sortFunc.apply(branchPaths);
        } else {
            sortBranchPaths(branchPaths);
        }

        // 表示需要截取
        if (maxSize > -1) {
            branchPaths = branchPaths.stream().limit(maxSize).collect(Collectors.toList());
        }

        List<ArrayList<LayNodeBo>> planLays = new ArrayList<>();

        for (List<Node> paths : branchPaths) {
            ArrayList<LayNodeBo> lays = new ArrayList<>();

            // 获取放置首开关 从第0个开始直到遇到杆塔
            ArrayList<Node> headKgPaths = new ArrayList<>();
            for (int i = 0; i < paths.size(); i++) {
                Node node = paths.get(i);
                headKgPaths.add(node);
                if (node.isPole() && i > 0) {
                    break;
                }
            }
            // 放在
            LayNodeBo headKgLay = new LayNodeBo(headKgPaths, LayNodeBo.KG_HEAD_TYPE);
            LayNodeBo contactLay = new LayNodeBo(paths, LayNodeBo.END_CONTACT_TYPE);

            lays.add(headKgLay);
            lays.add(contactLay);
            planLays.add(lays);
        }

        return planLays;
    }


    /**
     * 所有的分支线通过放置的排序
     */
    static void sortBranchPaths(List<List<Node>> branchPaths) {
        branchPaths.sort((paths1, paths2) -> getSortScore(paths2) - getSortScore(paths1));
    }

    /**
     * 各个分支路径排序
     * 优先末端 优先站房并且有剩余的间隔 > 末端是杆塔 > 其它（配变什么的）
     */
    static int getSortScore(List<Node> paths) {
        Node lastNode = paths.get(paths.size() - 1);
        int result = paths.size();
        List<HwgBayBo> hwgStations = NodeUtils.getHwgStation(paths);

        // 杆塔
        if (lastNode.isPole()) {
            result = result + 50;
        } else if (!CollectionUtils.isEmpty(hwgStations)) {
            // 表示有环网柜
            Optional<HwgBayBo> optional = hwgStations.stream()
                    .filter(n -> n.getBayNodes() != null && n.getBayNodes().size() >= 2).findFirst();
            HwgBayBo hwg = optional.orElse(null);

            // 环网柜有剩余的间隔可以新增联络
            if (hwg != null) {
                result = result + 40;
            }
        }


        return result;
    }


    /**
     * 按照一定条件拆分大分子获取大分子路径
     */
    public static List<BranchMainPathBo> conditionSplitPaths(
            Node currentNode, HashMap<String, BranchNode> branchNodeMap,
            Node nextEdge, List<ArrayList<Node>> allNodePaths,
            ToNodeNumsFunc<List<Node>, Node, HashMap<String, BranchNode>, List<NodeNumModelBo>> toNodeNums,
            Function<NodeNumModelBo, Boolean> judgeNodeNum) {
        List<BranchMainPathBo> result = new ArrayList<>();
        loopBranchPaths(currentNode, result, branchNodeMap, new HashMap<>(), nextEdge, true, toNodeNums, judgeNodeNum);

        HashMap<String, List<Node>> endPathMap = new HashMap<>();

        for (ArrayList<Node> allNodePath : allNodePaths) {
            Node node = allNodePath.get(allNodePath.size() - 1);
            endPathMap.put(node.getId(), allNodePath);
        }

        // 处理分支节点集合
        for (BranchMainPathBo branchPathBo : result) {
            List<Node> paths = branchPathBo.getPaths();
            List<List<Node>> branchPaths = new ArrayList<>();
            branchPaths.add(paths);
            Node branchStartNode = paths.get(0);
            for (BranchNode otherBranchNode : branchPathBo.getOtherBranchNodes()) {
                // 当前分支点所有节点  过来所有可能是末端的节点
                List<Node> endNodes = otherBranchNode.getNodes().stream().filter(n -> !n.isEdge() && n.getEdges().size() == 1).collect(Collectors.toList());
                for (Node endNode : endNodes) {
                    List<Node> tmpPaths = endPathMap.get(endNode.getId());
                    if (CollectionUtils.isNotEmpty(tmpPaths)) {
                        List<Node> subPaths = NodeUtils.subLiceNode(tmpPaths, branchStartNode, null);
                        if (CollectionUtils.isNotEmpty(subPaths)) {
                            // 截取到开始
                            branchPaths.add(subPaths);
                        }
                    }
                }
            }

            // 排序

            branchPathBo.setBranchPaths(branchPaths);
        }

        return result;
    }

    /**
     * 由于一个分支下由无数分支构成 获取满足规则的所有主分支
     * 实现逻辑：从开始节点逐步往里遍历此时就当成主分支，
     * 如果遇到分叉路 如果分叉路有超过两个是满足条件的 那么此时一条还是主 另一条重新开始的主分支
     */
    private static void loopBranchPaths(Node currentNode, List<BranchMainPathBo> result,
                                        HashMap<String, BranchNode> branchNodeMap,
                                        Map<String, Boolean> useNodeMap, Node nextEdge, boolean isBoolean,
                                        ToNodeNumsFunc<List<Node>, Node, HashMap<String, BranchNode>, List<NodeNumModelBo>> toNodeNums,
                                        Function<NodeNumModelBo, Boolean> judgeNodeNum) {

        ArrayList<Node> paths = new ArrayList<>();
        ArrayList<BranchNode> otherBranchNodes = new ArrayList<>();

        Node startNode = currentNode;
        while (currentNode != null) {
            // 递归获取查找  // 新增当前的线路

            useNodeMap.put(currentNode.getId(), true);
            paths.add(currentNode);
            Node nextNode = null;
            List<Node> edges = null;
            // 中压用户接入点 直接结束
            if (currentNode.isUserPoint()) {
                break;
            }
            if (currentNode.isEdge()) { // 边
                Node source = currentNode.getLink(true);
                Node target = currentNode.getLink(false);

                // 下一个节点
                nextNode = source == null || useNodeMap.containsKey(source.getId()) ? target : source;

                if (nextNode == null) {
                    edges = currentNode.getEdges();
                }
            } else { // 设备节点
                // 当前设备有那些边 我们继续往下递归
                edges = currentNode.getEdges();
            }
            if (edges != null) {
                // 过滤仅仅需要的下一个边
                // 起始边
                if (nextEdge != null) {
                    Node tmpNode = nextEdge;
                    // 在当前的边里面
                    if (edges.stream().anyMatch(n -> n.equals(tmpNode.getId()))) {
                        edges = Collections.singletonList(nextEdge);
                        nextEdge = null;
                    }
                }

                // 过滤已经存在遍历过的路径
                edges = edges.stream().filter(n -> !useNodeMap.containsKey(n.getId())).collect(Collectors.toList());

                if (edges.size() == 1) {
                    nextNode = edges.get(0);
                } else {  // 多条分叉路

                    // 当前对象是按照配变数量不合理排序
                    List<NodeNumModelBo> nodeNums = toNodeNums.apply(edges, currentNode, branchNodeMap);
                    boolean isNextMain = false;
                    for (NodeNumModelBo nodeNum : nodeNums) {
                        if (judgeNodeNum.apply(nodeNum)) {
                            if (isNextMain) {
                                // // 其它有问题超过数量的分支线
                                loopBranchPaths(currentNode, result, branchNodeMap, useNodeMap, nodeNum.getNextNode(), false, toNodeNums, judgeNodeNum);
                                continue;
                            }
                            isNextMain = true;
                        }
                        if (nextNode == null) {
                            nextNode = nodeNum.getNextNode();
                        } else {
                            BranchNode branchNode = nodeNum.getBranchNode();
                            if (branchNode != null) {
                                otherBranchNodes.add(branchNode);
                            }
                        }
                    }
                }
            }

            currentNode = nextNode;
        }
        result.add(new BranchMainPathBo(paths, startNode, isBoolean, otherBranchNodes));
    }
}
