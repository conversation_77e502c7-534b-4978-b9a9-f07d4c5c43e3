package com.ruoyi.service.plan.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.entity.exception.PlanGenerationException;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.PlanAnalysisState;
import com.ruoyi.entity.plan.bo.PlanBo;
import com.ruoyi.entity.plan.vo.PlanCost;
import com.ruoyi.entity.plan.vo.PlanVo;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import com.ruoyi.entity.problem.TaskInfo;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.mapper.plan.PlanAnalysisStateMapper;
import com.ruoyi.mapper.plan.PlanMapper;
import com.ruoyi.mapper.problem.ProblemMapper;
import com.ruoyi.service.cost.ICostService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.IPlanService;
import com.ruoyi.service.plan.generatePlan.BigBranchPlan;
import com.ruoyi.service.plan.generatePlan.FeederOverloadPlan;
import com.ruoyi.service.plan.generatePlan.SegPbMuchPlan;
import com.ruoyi.service.plan.generatePlan.SingleRadiationPlan;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import static com.ruoyi.entity.plan.PlanAnalysisState.*;

/**
 * 故障解决方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PlanServiceImpl implements IPlanService {

    @Autowired
    ICostService costService;
    // 分段配变不合理生产方案
    @Autowired
    SegPbMuchPlan segPgMuchPlan;

    @Autowired
    private ProblemMapper problemMapper;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    private final PlanMapper planMapper;

    @Autowired
    PlanAnalysisStateMapper planAnalysisStateMapper;

    @Autowired
    SingleRadiationPlan singleRadiationPlan;

    @Autowired
    BigBranchPlan bigBranchPlan;

    @Autowired
    FeederOverloadPlan feederOverloadPlan;

    private final Map<String, TaskInfo> taskMap = new ConcurrentHashMap<>();
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    // =========================== 方案基本增删改查 =======================

    /**
     * 根据问题ID 查询方案列表
     */
    @Override
    public List<Plan> queryByProblemId(Long problemId) {
        return planMapper.queryByProblemId(problemId);
    }

    /**
     * 查询故障解决方案
     */
    @Override
    public PlanVo queryById(Long id) {
        return planMapper.selectVoById(id);
    }

    /**
     * 查询故障解决方案列表
     */
    @Override
    public TableDataInfo<PlanVo> queryPageList(PlanBo bo) {
        LambdaQueryWrapper<Plan> lqw = buildQueryWrapper(bo);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(bo.getPageSize());
        pageQuery.setPageNum(bo.getPageNum());
        Page<PlanVo> result = planMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询故障解决方案列表
     */
    @Override
    public List<PlanVo> queryList(PlanBo bo) {
        LambdaQueryWrapper<Plan> lqw = buildQueryWrapper(bo);
        return planMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Plan> buildQueryWrapper(PlanBo bo) {
        LambdaQueryWrapper<Plan> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getLoad()), Plan::getLoad, bo.getLoad());
        lqw.eq(StringUtils.isNotBlank(bo.getEconomy()), Plan::getEconomy, bo.getEconomy());
        lqw.eq(StringUtils.isNotBlank(bo.getReliability()), Plan::getReliability, bo.getReliability());
        lqw.eq(StringUtils.isNotBlank(bo.getExe()), Plan::getExe, bo.getExe());
        lqw.eq(StringUtils.isNotBlank(bo.getAdvantage()), Plan::getAdvantage, bo.getAdvantage());
        lqw.eq(StringUtils.isNotBlank(bo.getDisadvantage()), Plan::getDisadvantage, bo.getDisadvantage());
        lqw.le(bo.getBudgetMax() != null, Plan::getBudget, bo.getBudgetMax());
        lqw.ge(bo.getBudgetMin() != null, Plan::getBudget, bo.getBudgetMin());
        lqw.eq(StringUtils.isNotBlank(bo.getN1()), Plan::getN1, bo.getN1());
        lqw.eq(StringUtils.isNotBlank(bo.getPlanType()), Plan::getPlanType, bo.getPlanType());
        lqw.eq(StringUtils.isNotBlank(bo.getOperateData()), Plan::getOperateData, bo.getOperateData());
        lqw.eq(StringUtils.isNotBlank(bo.getOperate()), Plan::getOperate, bo.getOperate());
        lqw.eq(bo.getRequireMents() != null, Plan::getRequireMents, bo.getRequireMents());

        return lqw;
    }

    /**
     * 新增解决方案
     */
    @Override
    public Boolean insertByBo(PlanBo bo) {
        Plan add = BeanUtil.toBean(bo, Plan.class);
        validEntityBeforeSave(add);
        boolean flag = planMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    /**
     * 修改故障解决方案
     */
    @Override
    public Boolean updateByBo(PlanBo bo) {
        Plan update = BeanUtil.toBean(bo, Plan.class);
        if (planMapper.selectCount(new QueryWrapper<Plan>()
                .eq("id", bo.getId())) > 0) {
            validEntityBeforeSave(update);
            return planMapper.updateById(update) > 0;
        }
        return false;
    }


    /**
     * 按问题id查询故障解决方案
     */

    @Override
    public List<PlanVo> byProblemId(Long id) {
        LambdaQueryWrapper<Plan> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Plan::getProblemId, id);
        return planMapper.selectVoList(lqw);
    }

    /**
     * 批量删除故障解决方案
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return planMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Plan entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    // =========================== 方案生成 =======================

    /**
     * 网架方案生产过程
     */
    public List<Plan> generateGridPlan(GeneratePlanBo generatePlanBo) {
        Long problemId = generatePlanBo.getProblemId();
        String token = generatePlanBo.getToken();

        ProblemVo problemVo = problemMapper.selectVoById(problemId);
        String feederId = problemVo.getFeederId();

        Integer categoryLevel2Code = problemVo.getCategoryLevel2Code();
        String deviceId = problemVo.getDeviceId();

        IGeneratePlan gridPlantHandle = getGridPlantHandle(categoryLevel2Code);
        if (gridPlantHandle == null) {
            throw new RuntimeException("暂无当前问题分类的处理程序 ！");
        }

        return gridPlantHandle.generatePlan(problemId, deviceId, feederId, token);
    }

    /**
     * 根据问题类型获取网架方案的处理程序
     */
    private IGeneratePlan getGridPlantHandle(Integer type) {
        HashMap<Integer, IGeneratePlan> hashMap = new HashMap<Integer, IGeneratePlan>() {{
            put(PlanConstants.SEG_PB_MUSH_LEVEL, segPgMuchPlan); // 分段内配变不合理
            put(PlanConstants.SEG_DFSXL_MUSH_LEVEL, singleRadiationPlan); // 单辐射无联络
            put(PlanConstants.SEG_DFZWLN_MUSH_LEVEL, bigBranchPlan);// 大分子无联络
            put(PlanConstants.SEG_XLZGZ_MUSH_LEVEL, feederOverloadPlan);// 线路重过载
        }};
        return hashMap.get(type);
    }

    /**
     * 异步生产方案，同时存入该次方案的状态实例对象
     */
    public String analysisGeneratePlan(GeneratePlanBo generatePlanBo) {

        TaskInfo taskInfo = new TaskInfo();

        //先判断关于此次的问题有没有相关实列正在执行中，如果有直接返回实列id，如果没有正在运行的实列则进行下一步
        LambdaQueryWrapper<PlanAnalysisState> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PlanAnalysisState::getProblemId, generatePlanBo.getProblemId());
        lambdaQueryWrapper.eq(PlanAnalysisState::getState, STATE_ANALYSIS_IN_PROGRESS);
        PlanAnalysisState initialization = planAnalysisStateMapper.selectOne(lambdaQueryWrapper);
        if (initialization != null) {
            //检查方案状态正在执行中的持续时间如果超过五分钟，如果没有超过5分钟的则返回实例id，如果超过5分钟则重新开始
            if (planAnalysisStateMapper.countOverdueRecords(initialization.getId()) == 0) {
                return initialization.getId();
            }
        }

        planAnalysisStateMapper.deleteByProblemId(generatePlanBo.getProblemId());

        //创建并且存入该次方案的状态实例对象
        PlanAnalysisState planAnalysisState = new PlanAnalysisState();
        planAnalysisState.setState(STATE_ANALYSIS_IN_PROGRESS);
        planAnalysisState.setProblemId(generatePlanBo.getProblemId());
        planAnalysisState.setStartTime(new Date());
        planAnalysisState.setVersion(generatePlanBo.getVersion());
        planAnalysisStateMapper.insert(planAnalysisState);

        taskMap.put(planAnalysisState.getId(), taskInfo);
        //开启异步生成方案
        Future<?> future = executorService.submit(() -> {
            try {
                if (!taskInfo.isPaused()) {
                    //生成方案列表，保存方案列表
                    Long problemId = generatePlanBo.getProblemId();
                    // 先删除
                    planProcessService.deleteByProblemId(problemId);
                    planMapper.deleteByProblemId(problemId);
                    List<Plan> plans = generatePlan(generatePlanBo);
                    //新增方案到数据库
                    planMapper.insertBatch(plans);
                    //修改实例的状态完成
                    update(planAnalysisState.getId(), STATE_ANALYSIS_COMPLETED, "完成");
                    return null; // 返回null表示任务正常结束
                } else {
                    synchronized (taskInfo.getPauseLock()) {
                        taskInfo.getPauseLock().wait();
                    }
                }
                return null; // 显式返回null
            } catch (PlanGenerationException e) {
                // 方案生成阶段的业务异常
                update(planAnalysisState.getId(), STATE_ANALYSIS_ERROR, e.getMessage());
                return false;
            } catch (InterruptedException e) {
                // 任务被中断
                update(planAnalysisState.getId(), STATE_ANALYSIS_ERROR, "任务被中断");
                return false;
            } catch (Exception e) {
                // 其他未知异常
                log.error("方案生成过程中发生未知异常", e);
                update(planAnalysisState.getId(), STATE_ANALYSIS_ERROR, "未知异常");
                return false;
            }

        });


        taskInfo.setFuture(future);
        return planAnalysisState.getId();
    }

    /**
     * 生成方案
     *
     * @param generatePlanBo
     * @return
     */
    private List<Plan> generatePlan(GeneratePlanBo generatePlanBo) {
        Long problemId = generatePlanBo.getProblemId();
        List<Plan> plans = new ArrayList<>(); // 初始化列表避免NPE

        try {
            planProcessService.pushLoadingProcess(problemId, "开始生成方案");

            // 1. 方案生成阶段
            try {
                plans = generateGridPlan(generatePlanBo);
                if (CollectionUtils.isEmpty(plans)) {
                    throw new PlanGenerationException("生成的方案列表为空");
                }
            } catch (PlanGenerationException e) {
                log.error("方案生成失败：{}", e.getMessage(), e);
               // throw e; // 返回空列表或根据需求处理
            }

            // 2. 造价计算阶段
            try {
                for (Plan plan : plans) {
                    PlanCost planCost = costService.computeCost(plan);
                    if (planCost == null || planCost.getTotalCost() == null) {
                        throw new PlanGenerationException("方案 " + plan.getId() + " 造价计算失败");
                    }
                    plan.setBudget(BigDecimal.valueOf(planCost.getTotalCost()));
                }
            } catch (PlanGenerationException e) {
                log.error("造价计算失败：{}", e.getMessage(), e);
               // throw e; // 或根据需求处理部分失败的方案
            }

            // 所有步骤成功完成
            planProcessService.pushProcess(
                    new ProblemSchemeAnalysis(problemId, null, ProblemSchemeAnalysis.END_TYPE, null, "完成")
            );
            return plans;

        } catch (Exception e) {
            // 未知异常处理
            log.error("方案生成过程中发生未知异常：{}", e.getMessage(), e);
            throw new PlanGenerationException("方案生成失败：未知错误", e);
        }
    }


    // =========================== 相关方案任务 =======================


//    interface NodeTypeStrategy {
//        boolean isMatch(Node node);
//    }

    /**
     * 暂停任务
     *
     * @param taskId
     * @return
     */
    @Override
    public Boolean pauseTask(Long taskId) {
        TaskInfo taskInfo = taskMap.get(taskId);
        if (taskInfo == null) {
            return false;
        }
        taskInfo.setPaused(true);

        return true;
    }

    /**
     * 恢复任务
     *
     * @param taskId
     * @return
     */
    @Override
    public Boolean resumeTask(Long taskId) {
        TaskInfo taskInfo = taskMap.get(taskId);
        if (taskInfo == null) {
            return false;
        }

        if (taskInfo.isPaused()) {
            taskInfo.setPaused(false);
            synchronized (taskInfo.getPauseLock()) {
                taskInfo.getPauseLock().notify();
            }
        }

        return true;
    }

    /**
     * 停止任务
     *
     * @param taskId
     * @return
     */
    @Override
    public Boolean stopTask(String taskId) {
        TaskInfo taskInfo = taskMap.get(taskId);
        if (taskInfo == null) {
            return false;
        }

        taskInfo.setCancelled(true);
        taskInfo.getFuture().cancel(true);
        taskMap.remove(taskId);
        update(taskId, STATE_ANALYSIS_STOPPED, "任务暂停");

        return true;
    }

    private void update(String taskId, int state, String message) {
        LambdaQueryWrapper<PlanAnalysisState> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PlanAnalysisState::getId, taskId);
        PlanAnalysisState update = new PlanAnalysisState();
        update.setState(state);
        update.setEndTime(new Date());
        update.setMessage(message);
        planAnalysisStateMapper.update(update, lambdaQueryWrapper);
    }

    /**
     * 根据问题id查询状态实例
     */
    @Override
    public PlanAnalysisState selectState(String id) {


        return planAnalysisStateMapper.selectById(id);
    }

    /**
     * 根据方案id查询所有相关方案造价
     *
     * @param id
     * @return
     */
    @Override
    public PlanCost planCost(Long id) {
        Plan plan = planMapper.selectById(id);
        if (plan != null) {

            return costService.computeCost(plan);
        }
        return null;
    }


}
