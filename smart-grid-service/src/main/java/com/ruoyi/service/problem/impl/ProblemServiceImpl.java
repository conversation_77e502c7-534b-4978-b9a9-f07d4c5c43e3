package com.ruoyi.service.problem.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.SelectExternalUtil;
import com.ruoyi.common.utils.util.StringByDate;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.external.JoinDevices;
import com.ruoyi.entity.external.JoinPsrUri;
import com.ruoyi.entity.map.NearFeeder;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.problem.*;
import com.ruoyi.entity.problem.bo.ProblemBo;
import com.ruoyi.entity.problem.vo.NearProblemVo;
import com.ruoyi.entity.problem.vo.ProblemPullDownMenu;
import com.ruoyi.entity.problem.vo.ProblemStatistics;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.entity.sysDict.SysDictData;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.map.NearFeederMapper;
import com.ruoyi.mapper.problem.ProblemMapper;
import com.ruoyi.service.map.impl.MapServiceImpl;
import com.ruoyi.service.problem.IProblemIdentifyService;
import com.ruoyi.service.problem.IProblemService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.beanutils.BeanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 故障Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@RequiredArgsConstructor
@Service
public class ProblemServiceImpl implements IProblemService {

    private final ProblemMapper problemMapper;
    @Autowired
    NearFeederMapper nearFeederMapper;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    MapServiceImpl mapService;

    @Autowired

    IProblemIdentifyService iProblemIdentifyService;

    /**
     * 查询故障
     */
    @Override
    public ProblemVo queryById(Long problemId) {
        return problemMapper.selectVoById(problemId);
    }

    /**
     * 查询故障列表
     */
    @Override
    public TableDataInfo<ProblemVo> queryPageList(ProblemBo bo) throws ParseException {
        LambdaQueryWrapper<Problem> countLqw = buildQueryWrapper(bo);
        Long total = problemMapper.selectCount(countLqw);

        LambdaQueryWrapper<Problem> dataLqw = buildQueryWrapper(bo);
        // 指定排序优先级的 ID 列表
        List<Long> priorityIds = Arrays.asList(17953176L, 17918157L, 47387202L, 46075645L, 47143944L);
        // 拼接 CASE WHEN SQL
        StringBuilder orderSql = new StringBuilder("CASE problem_id ");
        for (int i = 0; i < priorityIds.size(); i++) {
            orderSql.append("WHEN ").append(priorityIds.get(i)).append(" THEN ").append(i + 1).append(" ");
        }
        orderSql.append("ELSE 9999 END");
        // 添加原有过滤 + 自定义排序
        dataLqw.last("ORDER BY " + orderSql);

        Page<Problem> page = new Page<>(bo.getPageNum(), bo.getPageSize(), total);
        Page<ProblemVo> result = problemMapper.selectVoPage(page, dataLqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询故障列表
     */
    @Override
    public List<ProblemVo> queryList(ProblemBo bo) throws ParseException {
        LambdaQueryWrapper<Problem> lqw = buildQueryWrapper(bo);
        return problemMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Problem> buildQueryWrapper(ProblemBo bo) throws ParseException {
        LambdaQueryWrapper<Problem> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProblemStatus() != null, Problem::getProblemStatus, bo.getProblemStatus());
        lqw.eq(bo.getGradeName() != null, Problem::getGradeName, bo.getGradeName());
        lqw.like(StringUtils.isNotBlank(bo.getName()), Problem::getName, bo.getName());
        lqw.eq(bo.getDataSource() != null, Problem::getDataSource, bo.getDataSource());
        lqw.eq(bo.getAttrName() != null, Problem::getAttrName, bo.getAttrName());
        lqw.eq(bo.getCategoryLevel1Code() != null, Problem::getCategoryLevel1Code, bo.getCategoryLevel1Code());
        lqw.eq(bo.getCategoryLevel2Code() != null, Problem::getCategoryLevel2Code, bo.getCategoryLevel2Code());
        lqw.eq(bo.getProblemMyRule() != null, Problem::getProblemMyRule, bo.getProblemMyRule());
        lqw.eq(StringUtils.isNotBlank(bo.getCityComId()), Problem::getCityComId, bo.getCityComId());
        lqw.eq(StringUtils.isNotBlank(bo.getCountyComId()), Problem::getCountyComId, bo.getCountyComId());
        lqw.like(StringUtils.isNotBlank(bo.getInGridName()), Problem::getInGridName, bo.getInGridName());
        lqw.like(StringUtils.isNotBlank(bo.getRunGridName()), Problem::getRunGridName, bo.getRunGridName());
        lqw.eq(StringUtils.isNotBlank(bo.getSubstationId()), Problem::getSubstationId, bo.getSubstationId());
        lqw.eq(StringUtils.isNotBlank(bo.getFeederId()), Problem::getFeederId, bo.getFeederId());
        lqw.like(StringUtils.isNotBlank(bo.getFeederName()), Problem::getFeederName, bo.getFeederName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceId()), Problem::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getMerge()), Problem::getMerge, bo.getMerge());
//        lqw.orderByDesc(Problem::getDefectTime);
        if (StringUtils.isNotBlank(bo.getStrTime())) {
            lqw.ge(Problem::getDefectTime, StringByDate.stringByDate(bo.getStrTime()));
        }
        if (StringUtils.isNotBlank(bo.getEndTime())) {
            lqw.le(Problem::getDefectTime, StringByDate.stringByDate(bo.getEndTime()));
        }
        return lqw;
    }

    /**
     * 新增故障
     */
    @Override
    public Boolean insertByBo(ProblemBo bo) {
        Problem add = BeanUtil.toBean(bo, Problem.class);
        validEntityBeforeSave(add);
        boolean flag = problemMapper.insert(add) > 0;
        if (flag) {
            bo.setProblemId(add.getProblemId());
        }
        return flag;
    }

    /**
     * 修改故障
     */
    @Override
    public Boolean updateByBo(ProblemBo bo) throws ParseException {
        Problem update = BeanUtil.toBean(bo, Problem.class);

        if (StringUtils.isNotBlank(bo.getDefectTime())) {
            update.setDefectTime(StringByDate.stringByDate(bo.getDefectTime()));
        }
        if (problemMapper.selectVoById(bo.getProblemId()) == null) {
            return false;
        }
        validEntityBeforeSave(update);
        return problemMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Problem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除故障
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return problemMapper.deleteBatchIds(ids) > 0;
    }


    /**
     * 根据故障设备id查询，已经半径参数，查询范围内所有的故障设备信息
     *
     * @param
     * @param radius 半径
     */
    @Override
    public List<NearProblemVo> byDevices(Long problemId, Integer radius) throws JsonProcessingException {
        //查询出该问题的基础信息
        LambdaQueryWrapper<Problem> targetQueryWrapper = new LambdaQueryWrapper<>();
        targetQueryWrapper.eq(Problem::getProblemId, problemId);
        Problem targetProblem = problemMapper.selectOne(targetQueryWrapper);
        if (targetProblem == null) {
            return null;
        }

        //查询该问题所在线路的附近4公里线
        LambdaQueryWrapper<NearFeeder> nearFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        nearFeederLambdaQueryWrapper.eq(NearFeeder::getFeederId, targetProblem.getFeederId());
        NearFeeder nearFeeder = nearFeederMapper.selectOne(nearFeederLambdaQueryWrapper);

        List<String> idList = Arrays.stream(nearFeeder.getNearFeederId().split(","))
                .filter(s -> !s.isEmpty()) // 过滤空字符串
                .map(String::trim) // 去除首尾空格
                .collect(Collectors.toList());

        LambdaQueryWrapper<DeviceFeeder> deviceFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceFeederLambdaQueryWrapper.in(DeviceFeeder::getPsrId, idList);
        List<DeviceFeeder> deviceFeederList = feederDeviceMapper.selectList(deviceFeederLambdaQueryWrapper);

        FeederRangeQueryBo feederRangeQueryBo = new FeederRangeQueryBo();
        feederRangeQueryBo.setPsrId(targetProblem.getFeederId());
        feederRangeQueryBo.setRange(Double.longBitsToDouble(radius / 1000));
        feederRangeQueryBo.setNum(3);
        List<DeviceFeeder> nearFeederList = mapService.selectNeedFeeder(feederRangeQueryBo, deviceFeederList);
//        ObjectMapper mapper = new ObjectMapper();
//        //查询到基础来源的坐标
//        String result = getString(targetProblem, mapper);
//        List<String> targetCoordinate = JsonToCoordinateSingle(result);
//        Coordinate coordinate = MercatorLineCircleIntersection.wgs84ToMercator(Double.parseDouble(targetCoordinate.get(0)), Double.parseDouble(targetCoordinate.get(1)));
//        double centerLon = coordinate.x;
//        double centerLat = coordinate.y;
//
//
//        //查询所有故障问题list
//        LambdaQueryWrapper<Problem> feederProblem = new LambdaQueryWrapper<>();
//        feederProblem.in(Problem::getFeederId, deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));
//        List<Problem> list = problemMapper.selectList(feederProblem);
//
//        //过滤掉设备为空的id
//        List<Problem> filterList = list.stream().filter(problem -> (!problem.getDeviceId().isEmpty())).collect(Collectors.toList());
//
//        //通过外部的api，用idList查询所有的设备信息
//        String targetString = getString(filterList, mapper);
//        //计算出所有的设备经纬度
//        //解析好targetString的所有作表再用工具类筛选出在范围内的
//        List<String> selectIdList = JsonToCoordinate.JsonToCoordinate(targetString, centerLon, centerLat, radius);
//        //设备id去重
//        List<Problem> filteredUsers = list.stream()
//                .filter(problem -> selectIdList.contains(problem.getDeviceId()))
//                .collect(Collectors.toList());
//
        LambdaQueryWrapper<Problem> myFeederProblem = new LambdaQueryWrapper<>();
        myFeederProblem.in(Problem::getFeederId, nearFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));
        List<Problem> filteredUsers = problemMapper.selectList(myFeederProblem);
        List<NearProblemVo> problemVoList = filteredUsers.stream()
                .map(problem -> {
                    NearProblemVo nearProblemVo = new NearProblemVo();
                    try {
                        BeanUtils.copyProperties(nearProblemVo, problem);
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        throw new RuntimeException(e);
                    }
                    return nearProblemVo;
                })
                .collect(Collectors.toList());
        return problemVoList;
    }

    /**
     * 调用外部接口
     *
     * @param filterList
     * @param mapper
     * @return
     * @throws JsonProcessingException
     */
    private static String getString(List<Problem> filterList, ObjectMapper mapper) throws JsonProcessingException {
        JoinDevices joinDevices = new JoinDevices();
        List<JoinPsrUri> joinPsrUriList = filterList.parallelStream()
                .map(problem1 -> new JoinPsrUri(problem1.getDeviceId(), problem1.getDeviceType()))
                .collect(Collectors.toList());
        joinDevices.setPsrUriList(joinPsrUriList);
        String targetString = SelectExternalUtil.SelectExternalUtil("http://pms.kjyzt.js.sgcc.com.cn:32080/amap-gateway-service/amap-sdk-service/query/disSearch/queryDeviceById", mapper.writeValueAsString(joinDevices));
        return targetString;
    }


    /**
     * 查询下拉列表
     *
     * @return
     * @throws ParseException
     */
    @Override
    public ProblemPullDownMenu pullDownMenu() {
        ProblemPullDownMenu problemPullDownMenu = new ProblemPullDownMenu();
        List<Problem> list = problemMapper.selectList();

        //地市
        List<PullDownMenuStringSon> city = new ArrayList<>(list.stream()
                .filter(Objects::nonNull) // 过滤掉原列表中null元素
                .filter(o -> o.getCityComId() != null) // 确保去重字段不为null
                .collect(Collectors.toMap(
                        Problem::getCityComId, // 去重依据的字段
                        o -> new PullDownMenuStringSon(o.getCityComId(), o.getCityComName()), // 创建新对象
                        (o1, o2) -> o1 // 重复时保留第一个
                ))
                .values());
        problemPullDownMenu.setCity(city);

        //区县
        List<PullDownMenuStringSon> county = new ArrayList<>(list.stream()
                .filter(Objects::nonNull) // 过滤掉原列表中null元素
                .filter(o -> o.getCountyComId() != null) // 确保去重字段不为null
                .collect(Collectors.toMap(
                        Problem::getCountyComId, // 去重依据的字段
                        o -> new PullDownMenuStringSon(o.getCountyComId(), o.getCountyComName()), // 创建新对象
                        (o1, o2) -> o1 // 重复时保留第一个
                ))
                .values());
        problemPullDownMenu.setCounty(county);

        //数据来源
        List<SysDictData> dateSource = problemMapper.selectDateSource();
        List<PullDownMenuIntSon> dateSourcePullDownMenuSon = getPullDownMenuSons(dateSource);
        problemPullDownMenu.setDateSource(dateSourcePullDownMenuSon);

        //问题分类
        List<SysDictData> attrName = problemMapper.selectAttrName();
        List<PullDownMenuIntSon> attrNamePullDownMenuSon = getPullDownMenuSons(attrName);
        problemPullDownMenu.setAttrName(attrNamePullDownMenuSon);

        //严重等级
        List<SysDictData> gradeName = problemMapper.selectGradeName();
        List<PullDownMenuIntSon> gradeNamePullDownMenuSon = getPullDownMenuSons(gradeName);
        problemPullDownMenu.setGradeName(gradeNamePullDownMenuSon);

        //问题状态
        List<SysDictData> problemStatus = problemMapper.selectProblemStatus();
        List<PullDownMenuIntSon> problemStatusPullDownMenuSon = getPullDownMenuSons(problemStatus);
        problemPullDownMenu.setProblemStatus(problemStatusPullDownMenuSon);

        //一级分类
        List<SysDictData> level1 = problemMapper.selectLevel1();
        List<PullDownMenuIntSon> level1PullDownMenuSon = getPullDownMenuSons(level1);
        problemPullDownMenu.setLevel1(level1PullDownMenuSon);

        //二级分类
        List<SysDictData> level12 = problemMapper.selectLevel2();
        List<PullDownMenuIntSon> level2PullDownMenuSon = getPullDownMenuSons(level12);
        problemPullDownMenu.setLevel2(level2PullDownMenuSon);

        return problemPullDownMenu;
    }

    /**
     * 统计每种类型的数量
     *
     * @return
     */
    @Override
    public ProblemStatistics statistics() {
        ProblemStatistics problemStatistics = new ProblemStatistics();
        List<TypeStatistics> typeStatisticsList = new ArrayList<>();
        //总数
        List<Problem> list = problemMapper.selectList();
        problemStatistics.setTotalNum(list.size());


        //数据来源的所有类型
        List<SysDictData> dateSource = problemMapper.selectDateSource();
        List<Statistics> dateSourceNum = dateSource.stream()
                .map(data -> {
                    long count = list.stream()
                            .filter(o -> o.getDataSource() != null)
                            .filter(problem -> data.getDictValue().equals(problem.getDataSource().toString()))
                            .count();
                    return new Statistics(data.getDictLabel(), String.valueOf(count));
                })
                .collect(Collectors.toList());
        typeStatisticsList.add(new TypeStatistics("数据来源", dateSourceNum));

        //严重等级
        List<SysDictData> gradeName = problemMapper.selectGradeName();
        List<Statistics> gradeNameNum = gradeName.stream()
                .map(data -> {
                    long count = list.stream()
                            .filter(o -> o.getDataSource() != null)
                            .filter(problem -> data.getDictValue().equals(problem.getGradeName().toString()))
                            .count();
                    return new Statistics(data.getDictLabel(), String.valueOf(count));
                })
                .collect(Collectors.toList());
        typeStatisticsList.add(new TypeStatistics("严重等级", gradeNameNum));

        //问题分类的所有类型
        List<SysDictData> attrName = problemMapper.selectAttrName();
        List<Statistics> attrNameNum = attrName.stream()
                .map(data -> {
                    long count = list.stream()
                            .filter(o -> o.getDataSource() != null)
                            .filter(problem -> data.getDictValue().equals(problem.getAttrName().toString()))
                            .count();
                    return new Statistics(data.getDictLabel(), String.valueOf(count));
                })
                .collect(Collectors.toList());
        typeStatisticsList.add(new TypeStatistics("问题分类", attrNameNum));

        //一级分类的所有类型
        List<SysDictData> level1 = problemMapper.selectLevel1();
        List<Statistics> level1Num = level1.stream()
                .map(data -> {
                    long count = list.stream()
                            .filter(o -> o.getDataSource() != null)
                            .filter(problem -> data.getDictValue().equals(problem.getCategoryLevel1Code().toString()))
                            .count();
                    return new Statistics(data.getDictLabel(), String.valueOf(count));
                })
                .collect(Collectors.toList());
        typeStatisticsList.add(new TypeStatistics("一级分类", level1Num));

        //二级分类的所有类型
        List<SysDictData> level12 = problemMapper.selectLevel2();
        List<Statistics> level2Num = level12.stream()
                .map(data -> {
                    long count = list.stream()
                            .filter(o -> o.getDataSource() != null)
                            .filter(problem -> data.getDictValue().equals(problem.getCategoryLevel2Code().toString()))
                            .count();
                    return new Statistics(data.getDictLabel(), String.valueOf(count));
                })
                .collect(Collectors.toList());
        typeStatisticsList.add(new TypeStatistics("二级分类", level2Num));

        //问题状态的所有类型
        List<SysDictData> problemStatus = problemMapper.selectProblemStatus();
        List<Statistics> problemStatusNum = problemStatus.stream()
                .map(data -> {
                    long count = list.stream()
                            .filter(o -> o.getDataSource() != null)
                            .filter(problem -> data.getDictValue().equals(problem.getProblemStatus().toString()))
                            .count();
                    return new Statistics(data.getDictLabel(), String.valueOf(count));
                })
                .collect(Collectors.toList());
        typeStatisticsList.add(new TypeStatistics("问题状态", problemStatusNum));

        //线路统计
        Map<String, Long> countByField = list.stream()
                .collect(Collectors.groupingBy(
                        Problem::getFeederName, // 替换为你的字段get方法
                        Collectors.counting()
                ));

        List<Statistics> entityList = countByField.entrySet().stream()
                .map(entry -> {
                    Statistics entity = new Statistics();
                    entity.setTypeName(entry.getKey()); // 假设key是ID
                    entity.setTypeNum(entry.getValue().toString()); // 假设value是名称
                    return entity;
                })
                .collect(Collectors.toList());

        TypeStatistics typeStatistics = new TypeStatistics();
        typeStatistics.setType("线路统计");
        typeStatistics.setStatisticsList(entityList);
        typeStatisticsList.add(typeStatistics);
        problemStatistics.setStatistics(typeStatisticsList);
        return problemStatistics;
    }

    /**
     * 数据来源下拉列表
     *
     * @return
     */
    @Override
    public List<PullDownMenuIntSon> pullDownMenuDateSource() {
        List<SysDictData> dateSource = problemMapper.selectDateSource();
        return getPullDownMenuSons(dateSource);

    }

    /**
     * 问题分类下拉列表
     *
     * @return
     */
    @Override
    public List<PullDownMenuTree> pullDownMenuAttrNameTree() {

        //问题分类
        List<SysDictData> attrName = problemMapper.selectAttrName();

        //一级分类
        List<SysDictData> level1 = problemMapper.selectLevel1();


        //二级分类
        List<SysDictData> level2 = problemMapper.selectLevel2();

        List<PullDownMenuTree> menuTree = buildThreeLevelMenuTree(attrName, level1, level2);

        return menuTree;
    }

    public List<PullDownMenuTree> buildThreeLevelMenuTree(
            List<SysDictData> attrNames,
            List<SysDictData> level1,
            List<SysDictData> level2) {

        // 1. 将二级分类按父ID分组
        Map<String, List<PullDownMenuTree>> level2Map = level2.stream()
                .filter(item -> item.getPid() != null)
                .map(this::convertToMenuTree)
                .collect(Collectors.groupingBy(PullDownMenuTree::getPid));

        // 2. 构建一级分类并关联二级分类
        Map<String, List<PullDownMenuTree>> level1Map = level1.stream()
                .map(item -> {
                    PullDownMenuTree menuTree = convertToMenuTree(item);
                    menuTree.setTreeList(level2Map.getOrDefault(item.getDictLabel(), Collections.emptyList()));
                    return menuTree;
                })
                .collect(Collectors.groupingBy(PullDownMenuTree::getPid));

        // 3. 构建顶级问题分类并关联一级分类
        List<PullDownMenuTree> attrTree = attrNames.stream()
                .map(item -> {
                    PullDownMenuTree menuTree = convertToMenuTree(item);
                    menuTree.setTreeList(level1Map.getOrDefault(item.getDictLabel(), Collections.emptyList()));
                    return menuTree;
                })
                .collect(Collectors.toList());
        return attrTree;
    }

    private PullDownMenuTree convertToMenuTree(SysDictData dictData) {
        PullDownMenuTree menuTree = new PullDownMenuTree();
        menuTree.setPid(dictData.getPid()); // 父级ID
        menuTree.setTypeName(dictData.getDictLabel()); // 分类名称
        menuTree.setCode(Integer.valueOf(dictData.getDictValue())); // 分类编码
        menuTree.setTreeList(new ArrayList<>()); // 初始化子列表
        return menuTree;
    }


    /**
     * 地市下拉列表
     *
     * @return
     */
    @Override
    public List<PullDownMenuStringSon> pullDownMenuCity() {
        List<Problem> list = problemMapper.selectList();
        List<PullDownMenuStringSon> county = new ArrayList<>(list.stream()
                .filter(Objects::nonNull) // 过滤掉原列表中null元素
                .filter(o -> o.getCityComName() != null) // 确保去重字段不为null
                .collect(Collectors.toMap(
                        Problem::getCityComName, // 去重依据的字段
                        o -> new PullDownMenuStringSon(o.getCountyComId(), o.getCityComName()), // 创建新对象
                        (o1, o2) -> o1 // 重复时保留第一个
                ))
                .values());


        return county;
    }

    /**
     * 区县下拉列表
     *
     * @return
     */
    @Override
    public List<PullDownMenuStringSon> pullDownMenuCounty() {
        List<Problem> list = problemMapper.selectList();
        List<PullDownMenuStringSon> city = new ArrayList<>(list.stream()
                .filter(Objects::nonNull) // 过滤掉原列表中null元素
                .filter(o -> o.getCountyComName() != null) // 确保去重字段不为null
                .collect(Collectors.toMap(
                        Problem::getCountyComName, // 去重依据的字段
                        o -> new PullDownMenuStringSon(o.getCountyComId(), o.getCountyComName()), // 创建新对象
                        (o1, o2) -> o1 // 重复时保留第一个
                ))
                .values());
        return city;
    }


    /**
     * 问题状态下拉列表
     */
    @Override
    public List<PullDownMenuIntSon> pullDownMenuProblemStatus() {
        List<SysDictData> problemStatus = problemMapper.selectProblemStatus();
        return getPullDownMenuSons(problemStatus);
    }

    /**
     * 严重等级下拉列表
     */
    @Override
    public List<PullDownMenuIntSon> pullDownMenuGradeName() {
        List<SysDictData> gradeName = problemMapper.selectGradeName();
        return getPullDownMenuSons(gradeName);

    }

    /**
     * 问题类型下拉列表
     */
    @Override
    public List<PullDownMenuIntSon> pullDownMenuAttrName() {
        List<SysDictData> attrName = problemMapper.selectAttrName();
        return getPullDownMenuSons(attrName);

    }

    @Override
    public List<PullDownMenuIntSon> pullDownMenuLevel1(Integer problemId) {
        List<SysDictData> level1 = problemMapper.selectLevel1();
        List<SysDictData> filteredList = level1.stream()
                .filter(person -> person.getPid().equals(problemId.toString()) && person.getDictType().equals("category_level1"))
                .collect(Collectors.toList());

        return getPullDownMenuSons(filteredList);

    }

    @Override
    public List<PullDownMenuIntSon> pullDownMenuLevel2(Integer problemId) {
        List<SysDictData> level2 = problemMapper.selectLevel2();
        List<SysDictData> filteredList = level2.stream()
                .filter(person -> person.getPid().equals(problemId.toString()) && person.getDictType().equals("category_level2"))
                .collect(Collectors.toList());
        return getPullDownMenuSons(filteredList);
    }

    /**
     * 查找附近线的相关问题
     */
    @Override
    public List<ProblemVo> nearFeederProblem(String feederId, Double radius) {
        //查找附近线路
        List<DeviceFeeder> deviceFeederList = mapService.selectNearFeeder(feederId, radius);
        List<String> feederIds = new ArrayList<>();
        //提取线路id
        if (CollectionUtils.isNotEmpty(deviceFeederList)) {
            feederIds = deviceFeederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList());
        }
        //加上本身的线路问题
        feederIds.add(feederId);

        LambdaQueryWrapper<Problem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Problem::getFeederId, feederIds);
        List<ProblemVo> problemList = problemMapper.selectVoList(lambdaQueryWrapper);
        return problemList;
    }


    /**
     * 封装子集菜单
     *
     * @param dateSource
     * @return
     */
    @NotNull
    private static List<PullDownMenuIntSon> getPullDownMenuSons(List<SysDictData> dateSource) {
        List<PullDownMenuIntSon> pullDownMenuSon = new ArrayList<>(dateSource.stream()
                .filter(Objects::nonNull) // 过滤掉原列表中null元素
                .filter(o -> o.getDictLabel() != null) // 确保去重字段不为null
                .collect(Collectors.toMap(
                        SysDictData::getDictLabel, // 去重依据的字段
                        o -> new PullDownMenuIntSon(Integer.valueOf(o.getDictValue()), o.getDictLabel()), // 创建新对象
                        (o1, o2) -> o1 // 重复时保留第一个
                ))
                .values());
        return pullDownMenuSon;
    }


}
