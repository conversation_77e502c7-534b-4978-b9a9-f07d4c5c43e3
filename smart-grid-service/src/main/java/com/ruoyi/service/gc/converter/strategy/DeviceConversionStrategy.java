package com.ruoyi.service.gc.converter.strategy;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.graph.Node;

import java.util.Map;

/**
 * 设备转换策略接口
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface DeviceConversionStrategy {

    /**
     * 判断是否支持该设备类型
     * 
     * @param tableNo 表号
     * @return 是否支持
     */
    boolean supports(Long tableNo);

    /**
     * 转换设备
     * 
     * @param node 节点
     * @param versionId 版本ID
     * @param znapIdMap ID映射
     * @return 设备实体
     */
    GcDev convertDevice(Node node, Long versionId, Map<String, Long> znapIdMap);

    /**
     * 获取设备类型
     * 
     * @return 设备类型
     */
    Integer getDeviceType();
}
