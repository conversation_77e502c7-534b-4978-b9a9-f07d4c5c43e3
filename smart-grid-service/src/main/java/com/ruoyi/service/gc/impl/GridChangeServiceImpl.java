package com.ruoyi.service.gc.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.entity.gc.*;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.mapper.gc.*;
import com.ruoyi.service.gc.IGridChangeService;
import com.ruoyi.service.gc.converter.TopologyConverter;
import com.ruoyi.service.gc.validator.GridChangeValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 网架变更服务实现类
 */
@Slf4j
@Service
public class GridChangeServiceImpl implements IGridChangeService {

    @Autowired
    private GcSchemaMapper gcSchemaMapper;

    @Autowired
    private GcVersionMapper gcVersionMapper;

    @Autowired
    private GcConMapper gcConMapper;

    @Autowired
    private GcDevMapper gcDevMapper;

    @Autowired
    private GcPowerMapper gcPowerMapper;

    @Autowired
    private GcDevParaCbMapper gcDevParaCbMapper;

    @Autowired
    private TopologyConverter topologyConverter;

    @Autowired
    private GridChangeValidator gridChangeValidator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("slave")
    public Long saveGridChangeTopology(Long problemId, SingAnalysis singAnalysis, String schemaName, String description) {
        try {
            log.info("开始保存网架变更拓扑结构，problemId: {}, schemaName: {}", problemId, schemaName);

            // 1. 验证输入数据
            /*GridChangeValidator.ValidationResult validationResult = gridChangeValidator.validateSingAnalysis(singAnalysis);
            if (validationResult.hasErrors()) {
                throw new RuntimeException("数据验证失败: " + validationResult.getErrorMessage());
            }
            if (validationResult.hasWarnings()) {
                log.warn("数据验证警告: {}", validationResult.getWarningMessage());
            }*/

            // 2. 创建方案
            GcSchema schema = createOrGetSchema(problemId, schemaName, description);

            // 3. 创建版本
            GcVersion version = createVersion(schema.getId(), problemId);

            // 4. 转换并保存拓扑数据
            saveTopologyData(version.getId(), singAnalysis);

            log.info("网架变更拓扑结构保存成功，versionId: {}", version.getId());
            return version.getId();
            
        } catch (Exception e) {
            log.error("保存网架变更拓扑结构失败", e);
            throw new RuntimeException("保存网架变更拓扑结构失败: " + e.getMessage(), e);
        }
    }

    @Override
    public SingAnalysis getGridChangeTopology(Long versionId) {
        try {
            log.info("查询网架变更拓扑结构，versionId: {}", versionId);
            
            // 查询版本信息
            GcVersion version = gcVersionMapper.selectById(versionId);
            if (version == null) {
                throw new RuntimeException("版本不存在: " + versionId);
            }
            
            // 查询容器数据
            List<GcCon> containers = gcConMapper.selectList(
                new LambdaQueryWrapper<GcCon>().eq(GcCon::getVersionId, versionId)
            );
            
            // 查询设备数据
            List<GcDev> devices = gcDevMapper.selectList(
                new LambdaQueryWrapper<GcDev>().eq(GcDev::getVersionId, versionId)
            );
            
            // 查询电源点数据
            List<GcPower> powers = gcPowerMapper.selectList(
                new LambdaQueryWrapper<GcPower>().eq(GcPower::getVersionId, versionId)
            );
            
            // 转换为拓扑结构
            return topologyConverter.convertToSingAnalysis(containers, devices, powers);
            
        } catch (Exception e) {
            log.error("查询网架变更拓扑结构失败", e);
            throw new RuntimeException("查询网架变更拓扑结构失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteGridChangeTopology(Long versionId) {
        try {
            log.info("删除网架变更拓扑结构，versionId: {}", versionId);
            
            // 删除设备参数
            gcDevParaCbMapper.delete(
                new LambdaQueryWrapper<GcDevParaCb>().eq(GcDevParaCb::getVersionId, versionId)
            );
            
            // 删除电源点
            gcPowerMapper.delete(
                new LambdaQueryWrapper<GcPower>().eq(GcPower::getVersionId, versionId)
            );
            
            // 删除设备
            gcDevMapper.delete(
                new LambdaQueryWrapper<GcDev>().eq(GcDev::getVersionId, versionId)
            );
            
            // 删除容器
            gcConMapper.delete(
                new LambdaQueryWrapper<GcCon>().eq(GcCon::getVersionId, versionId)
            );
            
            // 删除版本
            gcVersionMapper.deleteById(versionId);
            
            log.info("网架变更拓扑结构删除成功，versionId: {}", versionId);
            return true;
            
        } catch (Exception e) {
            log.error("删除网架变更拓扑结构失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setCurrentVersion(Long versionId, Long problemId) {
        try {
            log.info("设置当前版本，versionId: {}, problemId: {}", versionId, problemId);
            
            // 将同一问题下的所有版本设为非当前版本
            gcVersionMapper.update(null, 
                new LambdaUpdateWrapper<GcVersion>()
                    .eq(GcVersion::getProblemId, problemId)
                    .set(GcVersion::getIsCurrent, 0)
            );
            
            // 设置指定版本为当前版本
            gcVersionMapper.update(null,
                new LambdaUpdateWrapper<GcVersion>()
                    .eq(GcVersion::getId, versionId)
                    .set(GcVersion::getIsCurrent, 1)
            );
            
            log.info("设置当前版本成功");
            return true;
            
        } catch (Exception e) {
            log.error("设置当前版本失败", e);
            return false;
        }
    }

    /**
     * 创建方案
     */
    private GcSchema createOrGetSchema(Long problemId, String schemaName, String description) {
        // 创建新方案
        GcSchema schema = new GcSchema();
        schema.setName(schemaName);
        schema.setDescription(description);
        schema.setProblemId(problemId);
        schema.setStatus(1);
        schema.setCreateUser("Admin");
        schema.setCreateDt(new Date());
        schema.setUpdateDt(new Date());
        gcSchemaMapper.insert(schema);
        return schema;
    }

    /**
     * 创建版本
     */
    private GcVersion createVersion(Long schemaId, Long problemId) {
        // 生成版本号
        String versionNum = generateVersionNum(problemId);
        
        GcVersion version = new GcVersion();
        version.setName("版本_" + versionNum);
        version.setVersionNum(versionNum);
        version.setDescription("自动生成的网架变更版本");
        version.setSchemaId(schemaId);
        version.setProblemId(problemId);
        version.setIsCurrent(1);
        version.setCreateUser("Admin");
        version.setCreateDt(new Date());
        version.setUpdateDt(new Date());
        
        gcVersionMapper.insert(version);
        return version;
    }

    /**
     * 生成版本号
     */
    private String generateVersionNum(Long problemId) {
        Long count = gcVersionMapper.selectCount(
            new LambdaQueryWrapper<GcVersion>().eq(GcVersion::getProblemId, problemId)
        );
        return "v" + (count + 1);
    }

    /**
     * 保存拓扑数据
     */
    private void saveTopologyData(Long versionId, SingAnalysis singAnalysis) {
        ZnapTopology topology = singAnalysis.getTopologyMap();
        
        // 转换并保存容器数据
        List<GcCon> containers = topologyConverter.convertToContainers(topology, versionId);
        if (!containers.isEmpty()) {
            containers.forEach(gcConMapper::insert);
        }
        
        // 转换并保存设备数据
        List<GcDev> devices = topologyConverter.convertToDevices(topology, versionId);
        if (!devices.isEmpty()) {
            devices.forEach(gcDevMapper::insert);
        }
        
        // 转换并保存电源点数据
        List<GcPower> powers = topologyConverter.convertToPowers(topology, versionId);
        if (!powers.isEmpty()) {
            powers.forEach(gcPowerMapper::insert);
        }
        
        // 保存设备参数（开关状态）
        List<GcDevParaCb> deviceParams = topologyConverter.convertToDeviceParams(topology, versionId);
        if (!deviceParams.isEmpty()) {
            deviceParams.forEach(gcDevParaCbMapper::insert);
        }
    }
}
