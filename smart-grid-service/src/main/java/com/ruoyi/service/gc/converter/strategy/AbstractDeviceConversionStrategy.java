package com.ruoyi.service.gc.converter.strategy;

import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.graph.Node;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 设备转换策略抽象基类
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Slf4j
public abstract class AbstractDeviceConversionStrategy implements DeviceConversionStrategy {

    /**
     * 支持的表号
     */
    protected abstract Long getSupportedTableNo();

    @Override
    public boolean supports(Long tableNo) {
        return getSupportedTableNo().equals(tableNo);
    }

    @Override
    public GcDev convertDevice(Node node, Long versionId, Map<String, Long> znapIdMap) {
        String nodeId = node.getId();
        if (!StringUtils.hasText(nodeId)) {
            log.warn("节点ID为空，跳过转换");
            return null;
        }

        Long znapId = znapIdMap.get(nodeId);
        if (znapId == null) {
            log.warn("未找到节点[{}]对应的znapId", nodeId);
            return null;
        }

        try {
            GcDev device = createBaseDevice(node, versionId, znapId);
            fillDeviceSpecificInfo(device, znapId);
            return device;
        } catch (Exception e) {
            log.error("转换设备失败，nodeId: {}, znapId: {}", nodeId, znapId, e);
            return null;
        }
    }

    /**
     * 创建基础设备信息
     */
    private GcDev createBaseDevice(Node node, Long versionId, Long znapId) {
        GcDev device = new GcDev();
        device.setName(node.getPsrName());
        device.setAliasName(node.getPsrName());
        device.setPsrid(node.getPsrId());
        device.setPsrtype(node.getPsrType());
        device.setType(getDeviceType());
        device.setVersionId(versionId);
        device.setObjId(znapId);
        device.setObjTableno(getSupportedTableNo());
        return device;
    }

    /**
     * 填充设备特定信息
     * 子类需要实现此方法来填充特定的设备信息
     * 
     * @param device 设备实体
     * @param znapId znap ID
     */
    protected abstract void fillDeviceSpecificInfo(GcDev device, Long znapId);

    /**
     * 设置单节点设备信息
     */
    protected void setSingleNodeDevice(GcDev device, Long nd, Long bvId, String rdfid, String mrid, Long containerId) {
        device.setInd(nd);
        device.setJnd(-1L);
        device.setBvId(bvId);
        device.setRdfid(rdfid);
        device.setMrid(mrid);
        device.setContainerId(containerId);
    }

    /**
     * 设置双节点设备信息
     */
    protected void setDoubleNodeDevice(GcDev device, Long ind, Long jnd, Long bvId, String rdfid, String mrid, Long containerId) {
        device.setInd(ind);
        device.setJnd(jnd);
        device.setBvId(bvId);
        device.setRdfid(rdfid);
        device.setMrid(mrid);
        device.setContainerId(containerId);
    }
}
