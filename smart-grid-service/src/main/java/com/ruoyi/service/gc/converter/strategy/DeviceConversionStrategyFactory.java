package com.ruoyi.service.gc.converter.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备转换策略工厂
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Slf4j
@Component
public class DeviceConversionStrategyFactory {

    @Autowired
    private List<DeviceConversionStrategy> strategies;

    private Map<Long, DeviceConversionStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (DeviceConversionStrategy strategy : strategies) {
            // 通过反射或其他方式获取支持的表号
            for (long tableNo = 100L; tableNo <= 220L; tableNo++) {
                if (strategy.supports(tableNo)) {
                    strategyMap.put(tableNo, strategy);
                    log.info("注册设备转换策略: 表号[{}] -> {}", tableNo, strategy.getClass().getSimpleName());
                }
            }
        }
        log.info("设备转换策略工厂初始化完成，共注册{}个策略", strategyMap.size());
    }

    /**
     * 获取设备转换策略
     * 
     * @param tableNo 表号
     * @return 转换策略
     */
    public DeviceConversionStrategy getStrategy(Long tableNo) {
        return strategyMap.get(tableNo);
    }

    /**
     * 判断是否支持该表号
     * 
     * @param tableNo 表号
     * @return 是否支持
     */
    public boolean isSupported(Long tableNo) {
        return strategyMap.containsKey(tableNo);
    }
}
