package com.ruoyi.service.gc;

import com.ruoyi.entity.map.SingAnalysis;

/**
 * 网架变更服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IGridChangeService {

    /**
     * 保存网架变更拓扑结构
     * 
     * @param problemId 问题ID
     * @param singAnalysis 拓扑分析结果
     * @param schemaName 方案名称
     * @param description 方案描述
     * @return 版本ID
     */
    Long saveGridChangeTopology(Long problemId, SingAnalysis singAnalysis, String schemaName, String description);

    /**
     * 根据版本ID查询网架变更数据
     * 
     * @param versionId 版本ID
     * @return 拓扑分析结果
     */
    SingAnalysis getGridChangeTopology(Long versionId);

    /**
     * 删除网架变更数据
     * 
     * @param versionId 版本ID
     * @return 是否删除成功
     */
    boolean deleteGridChangeTopology(Long versionId);

    /**
     * 更新版本为当前版本
     * 
     * @param versionId 版本ID
     * @param problemId 问题ID
     * @return 是否更新成功
     */
    boolean setCurrentVersion(Long versionId, Long problemId);
}
