package com.ruoyi.service.gc.converter;

import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.constant.GridChangeConstants;
import com.ruoyi.entity.gc.GcCon;
import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.gc.GcDevParaCb;
import com.ruoyi.entity.gc.GcPower;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.*;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.mapper.znap.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 拓扑结构转换器
 * 负责将ZnapTopology转换为网架变更数据库实体
 */
@Slf4j
@Component
public class TopologyConverter {

    @Resource
    private ConSubstationMapper substationMapper;

    @Resource
    private ConDmsFeederMapper conDmsFeederMapper;

    @Resource
    private ConDmsCabinetMapper conDmsCabinetMapper;

    @Resource
    private ConDmsCombinedMapper conDmsCombinedMapper;

    @Resource
    private DevEmsBreakerMapper emsBreakerMapper;

    @Resource
    private DevDmsBreakerMapper devDmsBreakerMapper;

    @Resource
    private DevDmsBusbarMapper devDmsBusbarMapper;
    @Resource
    private DevDmsFuseMapper devDmsFuseMapper;
    @Resource
    private DevDmsDisconnectorMapper devDmsDisconnectorMapper;
    @Resource
    private DevDmsSegmentMapper devDmsSegmentMapper;
    @Resource
    private DevDmsLdMapper devDmsLdMapper;
    @Resource
    private DevDmsPoleMapper devDmsPoleMapper;
    @Resource
    private DevDmsTrMapper devDmsTrMapper;
    @Resource
    private DevDmsWindingMapper devDmsWindingMapper;
    @Resource
    private DevEmsBreakerMapper devEmsBreakerMapper;

    @Resource
    private DevDmsJunctionMapper devDmsJunctionMapper;

    /**
     * 转换为容器数据
     */
    public List<GcCon> convertToContainers(ZnapTopology topology, Long versionId) {
        List<GcCon> containers = new ArrayList<>();
        Map<String, Long> znapIdMap = topology.getZnapIdMap();
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            return containers;
        }

        // 提取容器节点（变电站、馈线、环网柜、组合开关）
        Set<String> processedContainers = new HashSet<>();

        for (Node node : topology.getNodeList()) {
            if (node == null || node.getPsrType() == null) {
                continue;
            }

            // 判断是否为容器类型
            if (isContainerType(node, znapIdMap)) {
                String containerKey = node.getPsrId() + "_" + node.getPsrType();
                if (!processedContainers.contains(containerKey)) {
                    GcCon container = convertNodeToContainer(node, versionId, znapIdMap);
                    if (container != null) {
                        containers.add(container);
                        processedContainers.add(containerKey);
                    }
                }
            }
        }
        // TODO 变电站、大馈线需要手动补充，新增的设备要符合id生成标准

        log.info("转换容器数据完成，共{}个容器", containers.size());
        return containers;
    }

    /**
     * 转换为设备数据
     */
    public List<GcDev> convertToDevices(ZnapTopology topology, Long versionId) {
        List<GcDev> devices = new ArrayList<>();
        Map<String, Long> znapIdMap = topology.getZnapIdMap();
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            return devices;
        }

        for (Node node : topology.getNodeList()) {
            if (node == null || node.getPsrType() == null) {
                continue;
            }

            // 判断是否为设备类型
            if (isDeviceType(node, znapIdMap)) {
                GcDev device = convertNodeToDevice(node, versionId, znapIdMap);
                if (device != null) {
                    devices.add(device);
                }
            }
        }

        log.info("转换设备数据完成，共{}个设备", devices.size());
        return devices;
    }

    /**
     * 转换为电源点数据
     */
    public List<GcPower> convertToPowers(ZnapTopology topology, Long versionId) {
        List<GcPower> powers = new ArrayList<>();

        if (topology == null || topology.getStartNode() == null) {
            return powers;
        }

        Node startNode = topology.getStartNode();

        // 查找出线开关
        List<Node> outletSwitches = findOutletSwitches(startNode);

        for (Node outletSwitch : outletSwitches) {
            GcPower power = new GcPower();
            power.setVersionId(versionId);
            power.setStationId(extractStationId(startNode));
            power.setCbId(extractNodeId(outletSwitch));
            power.setHeadNd(extractHeadNode(outletSwitch));

            powers.add(power);
        }

        log.info("转换电源点数据完成，共{}个电源点", powers.size());
        return powers;
    }

    /**
     * 转换为设备参数数据（开关状态等）
     */
    public List<GcDevParaCb> convertToDeviceParams(ZnapTopology topology, Long versionId) {
        List<GcDevParaCb> params = new ArrayList<>();

        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            return params;
        }

        for (Node node : topology.getNodeList()) {
            if (node == null || node.getPsrType() == null) {
                continue;
            }

            // 只处理开关类型设备
            if (isSwitchType(node.getPsrType())) {
                GcDevParaCb param = new GcDevParaCb();
                param.setId(extractNodeId(node));
                param.setVersionId(versionId);
                param.setPosValue(extractSwitchPosition(node));

                params.add(param);
            }
        }

        log.info("转换设备参数数据完成，共{}个参数", params.size());
        return params;
    }

    /**
     * 转换为拓扑分析结果
     */
    public SingAnalysis convertToSingAnalysis(List<GcCon> containers, List<GcDev> devices, List<GcPower> powers) {
        // 重建拓扑结构
        ZnapTopology topology = rebuildTopology(containers, devices, powers);

        // 创建节点路径分析
        NodePath nodePath = new NodePath();
        if (topology.getStartNode() != null) {
            nodePath.analysisPath(topology.getStartNode(), topology.getKgContactNodes());
            nodePath.setNodeList(topology.getNodeList());
            nodePath.setNodeMap(topology.getNodeMap());
        }

        return new SingAnalysis(topology, nodePath);
    }

    /**
     * 判断是否为容器类型
     * 变电站、馈线、环网柜、组合开关
     * 变电站、馈线nodeList中没有存，需要手动加
     */
    private boolean isContainerType(Node node, Map<String, Long> znapIdMap) {
        Long id = znapIdMap.get(node.getId());
        if (id == null) {
            // 判断psrType
            boolean isHwg = DeviceConstants.HWG_TYPES.contains(node.getPsrType());
            boolean isKG = DeviceConstants.KG_IN_TYPES.contains(node.getPsrType());
            return isHwg || isKG;
        }
        // 将id右移48位获取表号
        Long tableNo = id >> 48;
        return GridChangeConstants.isContainerType(tableNo);
    }

    /**
     * 判断是否为设备类型
     */
    private boolean isDeviceType(Node node, Map<String, Long> znapIdMap) {
        // 绕组没有psrType 需要单独判断标号是不是210
        String id = node.getId();
        if (id != null) {
            Long znapId = znapIdMap.get(id);
            if (znapId != null) {
                Long tableNo = znapId >> 48;
                return tableNo.equals(210L);
            }
        }
        return GridChangeConstants.isDeviceType(node.getPsrType());
    }

    /**
     * 判断是否为开关类型
     */
    private boolean isSwitchType(String psrType) {
        return GridChangeConstants.isSwitchType(psrType);
    }

    /**
     *变电站的所属容器id为空
     *馈线的所属容器id为变电站
     *环网柜的所属容器id为馈线
     *组合开关的所属容器id为环网柜
     */
    private GcCon convertNodeToContainer(Node node, Long versionId, Map<String, Long> znapIdMap) {
        GcCon container = new GcCon();
        container.setName(node.getPsrName());
        container.setAliasName(node.getPsrName());
        Long id = znapIdMap.get(node.getId());
        if (id != null) {
            container.setObjId(id);
            container.setObjTableno(id >> 48);
        }
        Integer type = GridChangeConstants.CONTAINER_TYPE_MAP.get(node.getPsrType());
        // 1 变电站
        Long containerId = -1l;
        if (id != null) {
            if (type == 2) {
                // 馈线
                ConDmsFeeder conDmsFeeder = conDmsFeederMapper.selectById(id);
                containerId = conDmsFeeder.getSubId();
                container.setRdfid(conDmsFeeder.getRdfid());
                container.setMrid(conDmsFeeder.getMrid());
            } else if (type == 3) {
                // 环网柜
                ConDmsCabinet conDmsCabinet = conDmsCabinetMapper.selectById(id);
                containerId = conDmsCabinet.getFeederId();
                container.setRdfid(conDmsCabinet.getRdfid());
                container.setMrid(conDmsCabinet.getMrid());
            } else if (type == 4) {
                // 组合开关
                ConDmsCombined conDmsCombined = conDmsCombinedMapper.selectById(id);
                containerId = conDmsCombined.getCabinetId();
                container.setRdfid(conDmsCombined.getRdfid());
                container.setMrid(conDmsCombined.getMrid());
            }
        }
        container.setContainerId(containerId);

        container.setPsrid(node.getPsrId());
        container.setPsrtype(node.getPsrType());
        container.setType(type);
        container.setVersionId(versionId);
        return container;
    }

    /**
     * 转换节点为设备
     *  DEVICE_TYPE_MAP.put("100", 0);  // 主网开关
     *         DEVICE_TYPE_MAP.put("204", 1);  // 配网开关
     *         DEVICE_TYPE_MAP.put("205", 2);  // 刀闸
     *         DEVICE_TYPE_MAP.put("214", 3);  // 熔断器
     *         DEVICE_TYPE_MAP.put("211", 4);  // 母线
     *         DEVICE_TYPE_MAP.put("207", 5);  // 地刀
     *         DEVICE_TYPE_MAP.put("215", 6);  // 电缆头
     *         DEVICE_TYPE_MAP.put("209", 7);  // 负荷
     *         DEVICE_TYPE_MAP.put("212", 8);  // 杆塔
     *         DEVICE_TYPE_MAP.put("206", 9);  // 馈线段
     *         DEVICE_TYPE_MAP.put("208", 10); // 配变
     *         DEVICE_TYPE_MAP.put("210", 11); // 绕组
     */
    private GcDev convertNodeToDevice(Node node, Long versionId, Map<String, Long> znapIdMap) {
        GcDev device = new GcDev();
        device.setName(node.getPsrName());
        device.setAliasName(node.getPsrName());


        device.setPsrid(node.getPsrId());
        device.setPsrtype(node.getPsrType());

        String id = node.getId();
        if (id != null) {
            Long znapId = znapIdMap.get(id);
            Long tableNo = znapId >> 48;
            Integer type = GridChangeConstants.DEVICE_TYPE_MAP.get(tableNo);
            device.setType(type);
            device.setObjId(znapId);
            device.setObjTableno(tableNo);
            if (tableNo.equals(100L)) {
                //查询主网开关
                DevEmsBreaker devEmsBreaker = devEmsBreakerMapper.selectById(znapId);
                device.setInd(devEmsBreaker.getInd());
                device.setJnd(devEmsBreaker.getJnd());
                device.setBvId(devEmsBreaker.getBvId());
                // 设置Mrid
                device.setRdfid(devEmsBreaker.getRdfid());
                device.setMrid(devEmsBreaker.getMrid());
                device.setContainerId(devEmsBreaker.getSubId());
            } else if (tableNo.equals(204L)) {
                //查询配网开关
                DevDmsBreaker devDmsBreaker = devDmsBreakerMapper.selectById(znapId);
                device.setInd(devDmsBreaker.getInd());
                device.setJnd(devDmsBreaker.getJnd());
                device.setBvId(devDmsBreaker.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsBreaker.getRdfid());
                device.setMrid(devDmsBreaker.getMrid());
                device.setContainerId(devDmsBreaker.getFeederId());
            } else if (tableNo.equals(205L)) {
                //查询刀闸
                DevDmsDisconnector devDmsDisconnector = devDmsDisconnectorMapper.selectById(znapId);
                device.setInd(devDmsDisconnector.getInd());
                device.setJnd(devDmsDisconnector.getJnd());
                device.setBvId(devDmsDisconnector.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsDisconnector.getRdfid());
                device.setMrid(devDmsDisconnector.getMrid());
                device.setContainerId(devDmsDisconnector.getCabinetId());
            } else if (tableNo.equals(214L)) {
                //查询熔断器
                DevDmsFuse devDmsFuse = devDmsFuseMapper.selectById(znapId);
                device.setInd(devDmsFuse.getInd());
                device.setJnd(devDmsFuse.getJnd());
                device.setBvId(devDmsFuse.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsFuse.getRdfid());
                device.setMrid(devDmsFuse.getMrid());
                device.setContainerId(devDmsFuse.getCabinetId());
            } else if (tableNo.equals(211L)) {
                //查询母线
                DevDmsBusbar devDmsBusbar = devDmsBusbarMapper.selectById(znapId);
                device.setInd(devDmsBusbar.getNd());
                device.setJnd(devDmsBusbar.getNd());
                device.setBvId(devDmsBusbar.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsBusbar.getRdfid());
                device.setMrid(devDmsBusbar.getMrid());
                device.setContainerId(devDmsBusbar.getCabinetId());
            } else if (tableNo.equals(207L)) {
                //查询地刀
                DevDmsDisconnector devDmsDisconnector = devDmsDisconnectorMapper.selectById(znapId);
                device.setInd(devDmsDisconnector.getInd());
                device.setJnd(devDmsDisconnector.getJnd());
                device.setBvId(devDmsDisconnector.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsDisconnector.getRdfid());
                device.setMrid(devDmsDisconnector.getMrid());
                device.setContainerId(devDmsDisconnector.getCabinetId());
            } else if (tableNo.equals(215L)) {
                //查询电缆头
                DevDmsJunction devDmsJunction = devDmsJunctionMapper.selectById(znapId);
                device.setInd(devDmsJunction.getNd());
                device.setJnd(devDmsJunction.getNd());
                device.setBvId(devDmsJunction.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsJunction.getRdfid());
                device.setMrid(devDmsJunction.getMrid());
                device.setContainerId(devDmsJunction.getFeederId());
            } else if (tableNo.equals(209L)) {
                //查询负荷
                DevDmsLd devDmsLd = devDmsLdMapper.selectById(znapId);
                device.setInd(devDmsLd.getNd());
                device.setJnd(devDmsLd.getNd());
                device.setBvId(devDmsLd.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsLd.getRdfid());
                device.setMrid(devDmsLd.getMrid());
                device.setContainerId(devDmsLd.getCabinetId());
            } else if (tableNo.equals(212L)) {
                //查询杆塔
                DevDmsPole devDmsPole = devDmsPoleMapper.selectById(znapId);
                device.setInd(devDmsPole.getNd());
                device.setJnd(devDmsPole.getNd());
                device.setBvId(-1l);
                // 设置Mrid
                device.setRdfid(devDmsPole.getRdfid());
                device.setMrid(devDmsPole.getMrid());
                device.setContainerId(devDmsPole.getFeederId());
            } else if (tableNo.equals(206L)) {
                //查询馈线段
                DevDmsSegment devDmsSegment = devDmsSegmentMapper.selectById(znapId);
                device.setInd(devDmsSegment.getInd());
                device.setJnd(devDmsSegment.getJnd());
                device.setBvId(devDmsSegment.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsSegment.getRdfid());
                device.setMrid(devDmsSegment.getMrid());
                device.setContainerId(devDmsSegment.getFeederId());
            } else if (tableNo.equals(208L)) {
                //查询配变
                DevDmsTr devDmsTr = devDmsTrMapper.selectById(znapId);
                device.setInd(devDmsTr.getNd());
                device.setJnd(devDmsTr.getJnd());
                device.setBvId(devDmsTr.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsTr.getRdfid());
                device.setMrid(devDmsTr.getMrid());
                device.setContainerId(devDmsTr.getCabinetId());
            } else if (tableNo.equals(210L)) {
                //查询绕组
                DevDmsWinding devDmsWinding = devDmsWindingMapper.selectById(znapId);
                device.setInd(devDmsWinding.getNd());
                device.setJnd(devDmsWinding.getNd());
                device.setBvId(devDmsWinding.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsWinding.getRdfid());
                device.setMrid(devDmsWinding.getMrid());
                device.setContainerId(-1l);
            }
        }

        device.setVersionId(versionId);
        return device;
    }

    /**
     * 查找出线开关
     */
    private List<Node> findOutletSwitches(Node startNode) {
        List<Node> outletSwitches = new ArrayList<>();

        if (startNode != null && !CollectionUtils.isEmpty(startNode.getChildren())) {
            for (Node child : startNode.getChildren()) {
                if (child != null && isSwitchType(child.getPsrType())) {
                    outletSwitches.add(child);
                }
            }
        }

        return outletSwitches;
    }

    /**
     * 重建拓扑结构
     */
    private ZnapTopology rebuildTopology(List<GcCon> containers, List<GcDev> devices, List<GcPower> powers) {
        ZnapTopology topology = new ZnapTopology();

        // 创建节点映射
        Map<String, Node> nodeMap = new HashMap<>();
        ArrayList<Node> nodeList = new ArrayList<>();

        // 从容器创建节点
        for (GcCon container : containers) {
            Node node = createNodeFromContainer(container);
            nodeMap.put(node.getId(), node);
            nodeList.add(node);
        }

        // 从设备创建节点
        for (GcDev device : devices) {
            Node node = createNodeFromDevice(device);
            nodeMap.put(node.getId(), node);
            nodeList.add(node);
        }

        topology.setNodeMap(nodeMap);
        topology.setNodeList(nodeList);

        // 设置起始节点（从电源点推断）
        if (!powers.isEmpty()) {
            GcPower firstPower = powers.get(0);
            Node startNode = nodeMap.get(String.valueOf(firstPower.getStationId()));
            topology.setStartNode(startNode);
        }

        return topology;
    }

    /**
     * 从容器创建节点
     */
    private Node createNodeFromContainer(GcCon container) {
        Node node = new Node(container.getRdfid(), container.getPsrid(), container.getPsrtype());
        node.setPsrName(container.getName());
        return node;
    }

    /**
     * 从设备创建节点
     */
    private Node createNodeFromDevice(GcDev device) {
        Node node = new Node(device.getRdfid(), device.getPsrid(), device.getPsrtype());
        node.setPsrName(device.getName());
        return node;
    }

    // 以下为辅助方法，用于提取节点的各种属性
    private Long extractNodeId(Node node) {
        try {
            return Long.parseLong(node.getPsrId());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     *变电站的所属容器id为空
     *馈线的所属容器id为变电站
     *环网柜的所属容器id为馈线
     *组合开关的所属容器id为环网柜
     * @param node
     * @return
     */
    private Long extractContainerId(Node node) {

        return null;
    }

    private Long extractStationId(Node node) {
        return extractNodeId(node);
    }

    private Long extractHeadNode(Node node) {
        return extractNodeId(node);
    }

    private Long extractInd(Node node) {
        return extractNodeId(node);
    }

    private Long extractJnd(Node node) {
        return null; // 单节点设备jnd不处理
    }

    private Long extractBvId(Node node) {
        // 从节点属性中提取电压等级
        if (node.getProperties() != null && node.getProperties().containsKey("bvId")) {
            Object bvId = node.getProperties().get("bvId");
            if (bvId instanceof Number) {
                return ((Number) bvId).longValue();
            }
        }
        return null;
    }

    private Integer extractSwitchPosition(Node node) {
        // 从节点属性中提取开关位置，默认为合位
        if (node.getProperties() != null && node.getProperties().containsKey("position")) {
            Object position = node.getProperties().get("position");
            if (position instanceof Number) {
                return ((Number) position).intValue();
            }
        }
        return GridChangeConstants.SwitchPosition.CLOSE; // 默认合位
    }
}
