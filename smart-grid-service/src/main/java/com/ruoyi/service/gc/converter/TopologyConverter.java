package com.ruoyi.service.gc.converter;

import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.constant.GridChangeConstants;
import com.ruoyi.entity.gc.GcCon;
import com.ruoyi.entity.gc.GcDev;
import com.ruoyi.entity.gc.GcDevParaCb;
import com.ruoyi.entity.gc.GcPower;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.*;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.mapper.znap.ConDmsCabinetMapper;
import com.ruoyi.mapper.znap.ConDmsCombinedMapper;
import com.ruoyi.mapper.znap.ConDmsFeederMapper;
import com.ruoyi.service.gc.converter.strategy.DeviceConversionStrategy;
import com.ruoyi.service.gc.converter.strategy.DeviceConversionStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 拓扑结构转换器
 * 负责将ZnapTopology转换为网架变更数据库实体
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Slf4j
@Component
public class TopologyConverter {

    @Resource
    private ConDmsFeederMapper conDmsFeederMapper;

    @Resource
    private ConDmsCabinetMapper conDmsCabinetMapper;

    @Resource
    private ConDmsCombinedMapper conDmsCombinedMapper;

    @Resource
    private DeviceConversionStrategyFactory deviceConversionStrategyFactory;

    /**
     * 转换为容器数据
     */
    public List<GcCon> convertToContainers(ZnapTopology topology, Long versionId) {
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            log.warn("拓扑结构为空，跳过容器转换");
            return new ArrayList<>();
        }

        Map<String, Long> znapIdMap = topology.getZnapIdMap();
        if (znapIdMap == null) {
            log.warn("znapIdMap为空，跳过容器转换");
            return new ArrayList<>();
        }

        // 使用Stream API优化处理逻辑
        List<GcCon> containers = topology.getNodeList().stream()
            .filter(node -> node != null && StringUtils.hasText(node.getPsrType()))
            .filter(node -> isContainerType(node, znapIdMap))
            .collect(Collectors.toMap(
                node -> node.getPsrId() + "_" + node.getPsrType(), // 去重key
                Function.identity(),
                (existing, replacement) -> existing // 保留第一个
            ))
            .values()
            .stream()
            .map(node -> convertNodeToContainer(node, versionId, znapIdMap))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        log.info("转换容器数据完成，共{}个容器", containers.size());
        return containers;
    }

    /**
     * 转换为设备数据
     */
    public List<GcDev> convertToDevices(ZnapTopology topology, Long versionId) {
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            log.warn("拓扑结构为空，跳过设备转换");
            return new ArrayList<>();
        }

        Map<String, Long> znapIdMap = topology.getZnapIdMap();
        if (znapIdMap == null) {
            log.warn("znapIdMap为空，跳过设备转换");
            return new ArrayList<>();
        }

        // 使用Stream API和策略模式优化设备转换
        List<GcDev> devices = topology.getNodeList().stream()
            .filter(node -> node != null && isDeviceType(node, znapIdMap))
            .map(node -> convertNodeToDeviceWithStrategy(node, versionId, znapIdMap))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        log.info("转换设备数据完成，共{}个设备", devices.size());
        return devices;
    }

    /**
     * 使用策略模式转换设备
     */
    private GcDev convertNodeToDeviceWithStrategy(Node node, Long versionId, Map<String, Long> znapIdMap) {
        String nodeId = node.getId();
        if (!StringUtils.hasText(nodeId)) {
            log.warn("节点ID为空，跳过转换");
            return null;
        }

        Long znapId = znapIdMap.get(nodeId);
        if (znapId == null) {
            log.warn("未找到节点[{}]对应的znapId", nodeId);
            return null;
        }

        Long tableNo = znapId >> 48;
        DeviceConversionStrategy strategy = deviceConversionStrategyFactory.getStrategy(tableNo);

        if (strategy != null) {
            return strategy.convertDevice(node, versionId, znapIdMap);
        } else {
            log.warn("未找到表号[{}]对应的转换策略，使用默认转换", tableNo);
            return convertNodeToDeviceDefault(node, versionId, znapId, tableNo);
        }
    }

    /**
     * 默认设备转换方法（兼容旧逻辑）
     */
    private GcDev convertNodeToDeviceDefault(Node node, Long versionId, Long znapId, Long tableNo) {
        GcDev device = new GcDev();
        device.setName(node.getPsrName());
        device.setAliasName(node.getPsrName());
        device.setPsrid(node.getPsrId());
        device.setPsrtype(node.getPsrType());
        device.setVersionId(versionId);
        device.setObjId(znapId);
        device.setObjTableno(tableNo.intValue());

        // 设置默认类型
        Integer deviceType = GridChangeConstants.DEVICE_TYPE_MAP.get(tableNo.toString());
        device.setType(deviceType);

        return device;
    }

    /**
     * 转换为电源点数据
     */
    public List<GcPower> convertToPowers(ZnapTopology topology, Long versionId) {
        if (topology == null || topology.getStartNode() == null) {
            log.warn("拓扑结构或起始节点为空，跳过电源点转换");
            return new ArrayList<>();
        }

        Node startNode = topology.getStartNode();
        List<Node> outletSwitches = findOutletSwitches(startNode);

        if (outletSwitches.isEmpty()) {
            log.warn("未找到出线开关，跳过电源点转换");
            return new ArrayList<>();
        }

        // 使用Stream API优化处理
        List<GcPower> powers = outletSwitches.stream()
            .map(outletSwitch -> createPowerFromSwitch(outletSwitch, startNode, versionId))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        log.info("转换电源点数据完成，共{}个电源点", powers.size());
        return powers;
    }

    /**
     * 从开关创建电源点
     */
    private GcPower createPowerFromSwitch(Node outletSwitch, Node startNode, Long versionId) {
        try {
            GcPower power = new GcPower();
            power.setVersionId(versionId);
            power.setStationId(extractStationId(startNode));
            power.setCbId(extractNodeId(outletSwitch));
            power.setHeadNd(extractHeadNode(outletSwitch));
            return power;
        } catch (Exception e) {
            log.error("创建电源点失败，开关节点: {}", outletSwitch.getId(), e);
            return null;
        }
    }

    /**
     * 转换为设备参数数据（开关状态等）
     */
    public List<GcDevParaCb> convertToDeviceParams(ZnapTopology topology, Long versionId) {
        if (topology == null || CollectionUtils.isEmpty(topology.getNodeList())) {
            log.warn("拓扑结构为空，跳过设备参数转换");
            return new ArrayList<>();
        }

        // 使用Stream API优化处理，只处理开关类型设备
        List<GcDevParaCb> params = topology.getNodeList().stream()
            .filter(node -> node != null && StringUtils.hasText(node.getPsrType()))
            .filter(node -> isSwitchType(node.getPsrType()))
            .map(node -> createDeviceParam(node, versionId))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        log.info("转换设备参数数据完成，共{}个参数", params.size());
        return params;
    }

    /**
     * 创建设备参数
     */
    private GcDevParaCb createDeviceParam(Node node, Long versionId) {
        try {
            Long nodeId = extractNodeId(node);
            if (nodeId == null) {
                log.warn("无法提取节点ID，跳过参数创建，节点: {}", node.getId());
                return null;
            }

            GcDevParaCb param = new GcDevParaCb();
            param.setId(nodeId);
            param.setVersionId(versionId);
            param.setPosValue(extractSwitchPosition(node));
            return param;
        } catch (Exception e) {
            log.error("创建设备参数失败，节点: {}", node.getId(), e);
            return null;
        }
    }

    /**
     * 转换为拓扑分析结果
     */
    public SingAnalysis convertToSingAnalysis(List<GcCon> containers, List<GcDev> devices, List<GcPower> powers) {
        // 重建拓扑结构
        ZnapTopology topology = rebuildTopology(containers, devices, powers);

        // 创建节点路径分析
        NodePath nodePath = new NodePath();
        if (topology.getStartNode() != null) {
            nodePath.analysisPath(topology.getStartNode(), topology.getKgContactNodes());
            nodePath.setNodeList(topology.getNodeList());
            nodePath.setNodeMap(topology.getNodeMap());
        }

        return new SingAnalysis(topology, nodePath);
    }

    /**
     * 判断是否为容器类型
     * 变电站、馈线、环网柜、组合开关
     * 变电站、馈线nodeList中没有存，需要手动加
     */
    private boolean isContainerType(Node node, Map<String, Long> znapIdMap) {
        Long id = znapIdMap.get(node.getId());
        if (id == null) {
            // 判断psrType
            boolean isHwg = DeviceConstants.HWG_TYPES.contains(node.getPsrType());
            boolean isKG = DeviceConstants.KG_IN_TYPES.contains(node.getPsrType());
            return isHwg || isKG;
        }
        // 将id右移48位获取表号
        Long tableNo = id >> 48;
        return GridChangeConstants.isContainerType(tableNo);
    }

    /**
     * 判断是否为设备类型
     */
    private boolean isDeviceType(Node node, Map<String, Long> znapIdMap) {
        // 绕组没有psrType 需要单独判断标号是不是210
        String id = node.getId();
        if (id != null) {
            Long znapId = znapIdMap.get(id);
            if (znapId != null) {
                Long tableNo = znapId >> 48;
                return tableNo.equals(210L);
            }
        }
        return GridChangeConstants.isDeviceType(node.getPsrType());
    }

    /**
     * 判断是否为开关类型
     */
    private boolean isSwitchType(String psrType) {
        return GridChangeConstants.isSwitchType(psrType);
    }

    /**
     * 转换节点为容器
     * 容器层级关系：变电站 -> 馈线 -> 环网柜 -> 组合开关
     */
    private GcCon convertNodeToContainer(Node node, Long versionId, Map<String, Long> znapIdMap) {
        try {
            GcCon container = createBaseContainer(node, versionId);

            Long znapId = znapIdMap.get(node.getId());
            if (znapId != null) {
                container.setObjId(znapId);
                container.setObjTableno((znapId >> 48).intValue());

                // 根据容器类型填充特定信息
                fillContainerSpecificInfo(container, znapId);
            }

            return container;
        } catch (Exception e) {
            log.error("转换容器失败，节点: {}", node.getId(), e);
            return null;
        }
    }

    /**
     * 创建基础容器信息
     */
    private GcCon createBaseContainer(Node node, Long versionId) {
        GcCon container = new GcCon();
        container.setName(node.getPsrName());
        container.setAliasName(node.getPsrName());
        container.setPsrid(node.getPsrId());
        container.setPsrtype(node.getPsrType());
        container.setVersionId(versionId);

        // 设置容器类型
        Integer type = GridChangeConstants.CONTAINER_TYPE_MAP.get(node.getPsrType());
        container.setType(type);

        return container;
    }

    /**
     * 填充容器特定信息
     */
    private void fillContainerSpecificInfo(GcCon container, Long znapId) {
        Integer type = container.getType();
        if (type == null) {
            return;
        }

        switch (type) {
            case 1: // 变电站
                container.setContainerId(null); // 变电站没有上级容器
                break;
            case 2: // 馈线
                fillFeederInfo(container, znapId);
                break;
            case 3: // 环网柜
                fillCabinetInfo(container, znapId);
                break;
            case 4: // 组合开关
                fillCombinedInfo(container, znapId);
                break;
            default:
                log.warn("未知容器类型: {}", type);
        }
    }

    /**
     * 填充馈线信息
     */
    private void fillFeederInfo(GcCon container, Long znapId) {
        ConDmsFeeder feeder = conDmsFeederMapper.selectById(znapId);
        if (feeder != null) {
            container.setContainerId(feeder.getSubId());
            container.setRdfid(feeder.getRdfid());
            container.setMrid(feeder.getMrid());
        }
    }

    /**
     * 填充环网柜信息
     */
    private void fillCabinetInfo(GcCon container, Long znapId) {
        ConDmsCabinet cabinet = conDmsCabinetMapper.selectById(znapId);
        if (cabinet != null) {
            container.setContainerId(cabinet.getFeederId());
            container.setRdfid(cabinet.getRdfid());
            container.setMrid(cabinet.getMrid());
        }
    }

    /**
     * 填充组合开关信息
     */
    private void fillCombinedInfo(GcCon container, Long znapId) {
        ConDmsCombined combined = conDmsCombinedMapper.selectById(znapId);
        if (combined != null) {
            container.setContainerId(combined.getCabinetId());
            container.setRdfid(combined.getRdfid());
            container.setMrid(combined.getMrid());
        }
    }

    /**
     * 旧的设备转换方法（已废弃，保留用于兼容性）
     * @deprecated 使用策略模式替代
     */
    @Deprecated
    private GcDev convertNodeToDevice(Node node, Long versionId, Map<String, Long> znapIdMap) {
        GcDev device = new GcDev();
        device.setName(node.getPsrName());
        device.setAliasName(node.getPsrName());

        device.setPsrid(node.getPsrId());
        device.setPsrtype(node.getPsrType());

        String id = node.getId();
        if (id != null) {
            Long znapId = znapIdMap.get(id);
            Long tableNo = znapId >> 48;
            Integer type = GridChangeConstants.DEVICE_TYPE_MAP.get(tableNo);
            device.setType(type);
            device.setObjId(znapId);
            device.setObjTableno(tableNo);
            if (tableNo.equals(100L)) {
                //查询主网开关
                DevEmsBreaker devEmsBreaker = devEmsBreakerMapper.selectById(znapId);
                device.setInd(devEmsBreaker.getInd());
                device.setJnd(devEmsBreaker.getJnd());
                device.setBvId(devEmsBreaker.getBvId());
                // 设置Mrid
                device.setRdfid(devEmsBreaker.getRdfid());
                device.setMrid(devEmsBreaker.getMrid());
                device.setContainerId(devEmsBreaker.getSubId());
            } else if (tableNo.equals(204L)) {
                //查询配网开关
                DevDmsBreaker devDmsBreaker = devDmsBreakerMapper.selectById(znapId);
                device.setInd(devDmsBreaker.getInd());
                device.setJnd(devDmsBreaker.getJnd());
                device.setBvId(devDmsBreaker.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsBreaker.getRdfid());
                device.setMrid(devDmsBreaker.getMrid());
                device.setContainerId(devDmsBreaker.getFeederId());
            } else if (tableNo.equals(205L)) {
                //查询刀闸
                DevDmsDisconnector devDmsDisconnector = devDmsDisconnectorMapper.selectById(znapId);
                device.setInd(devDmsDisconnector.getInd());
                device.setJnd(devDmsDisconnector.getJnd());
                device.setBvId(devDmsDisconnector.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsDisconnector.getRdfid());
                device.setMrid(devDmsDisconnector.getMrid());
                device.setContainerId(devDmsDisconnector.getCabinetId());
            } else if (tableNo.equals(214L)) {
                //查询熔断器
                DevDmsFuse devDmsFuse = devDmsFuseMapper.selectById(znapId);
                device.setInd(devDmsFuse.getInd());
                device.setJnd(devDmsFuse.getJnd());
                device.setBvId(devDmsFuse.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsFuse.getRdfid());
                device.setMrid(devDmsFuse.getMrid());
                device.setContainerId(devDmsFuse.getCabinetId());
            } else if (tableNo.equals(211L)) {
                //查询母线
                DevDmsBusbar devDmsBusbar = devDmsBusbarMapper.selectById(znapId);
                device.setInd(devDmsBusbar.getNd());
                device.setJnd(-1l);
                device.setBvId(devDmsBusbar.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsBusbar.getRdfid());
                device.setMrid(devDmsBusbar.getMrid());
                device.setContainerId(devDmsBusbar.getCabinetId());
            } else if (tableNo.equals(207L)) {
                //查询地刀
                DevDmsDisconnector devDmsDisconnector = devDmsDisconnectorMapper.selectById(znapId);
                device.setInd(devDmsDisconnector.getInd());
                device.setJnd(devDmsDisconnector.getJnd());
                device.setBvId(devDmsDisconnector.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsDisconnector.getRdfid());
                device.setMrid(devDmsDisconnector.getMrid());
                device.setContainerId(devDmsDisconnector.getCabinetId());
            } else if (tableNo.equals(215L)) {
                //查询电缆头
                DevDmsJunction devDmsJunction = devDmsJunctionMapper.selectById(znapId);
                device.setInd(devDmsJunction.getNd());
                device.setJnd(-1l);
                device.setBvId(devDmsJunction.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsJunction.getRdfid());
                device.setMrid(devDmsJunction.getMrid());
                device.setContainerId(devDmsJunction.getFeederId());
            } else if (tableNo.equals(209L)) {
                //查询负荷
                DevDmsLd devDmsLd = devDmsLdMapper.selectById(znapId);
                device.setInd(devDmsLd.getNd());
                device.setJnd(-1l);
                device.setBvId(devDmsLd.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsLd.getRdfid());
                device.setMrid(devDmsLd.getMrid());
                device.setContainerId(devDmsLd.getCabinetId());
            } else if (tableNo.equals(212L)) {
                //查询杆塔
                DevDmsPole devDmsPole = devDmsPoleMapper.selectById(znapId);
                device.setInd(devDmsPole.getNd());
                device.setJnd(-1l);
                device.setBvId(-1l);
                // 设置Mrid
                device.setRdfid(devDmsPole.getRdfid());
                device.setMrid(devDmsPole.getMrid());
                device.setContainerId(devDmsPole.getFeederId());
            } else if (tableNo.equals(206L)) {
                //查询馈线段
                DevDmsSegment devDmsSegment = devDmsSegmentMapper.selectById(znapId);
                device.setInd(devDmsSegment.getInd());
                device.setJnd(devDmsSegment.getJnd());
                device.setBvId(devDmsSegment.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsSegment.getRdfid());
                device.setMrid(devDmsSegment.getMrid());
                device.setContainerId(devDmsSegment.getFeederId());
            } else if (tableNo.equals(208L)) {
                //查询配变
                DevDmsTr devDmsTr = devDmsTrMapper.selectById(znapId);
                device.setInd(devDmsTr.getNd());
                device.setJnd(-1l);
                device.setBvId(devDmsTr.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsTr.getRdfid());
                device.setMrid(devDmsTr.getMrid());
                device.setContainerId(devDmsTr.getCabinetId());
            } else if (tableNo.equals(210L)) {
                //查询绕组
                DevDmsWinding devDmsWinding = devDmsWindingMapper.selectById(znapId);
                device.setInd(devDmsWinding.getNd());
                device.setJnd(-1l);
                device.setBvId(devDmsWinding.getBvId());
                // 设置Mrid
                device.setRdfid(devDmsWinding.getRdfid());
                device.setMrid(devDmsWinding.getMrid());
                device.setContainerId(devDmsWinding.getTrId());
            }
        }

        device.setVersionId(versionId);
        return device;
    }

    /**
     * 查找出线开关
     */
    private List<Node> findOutletSwitches(Node startNode) {
        List<Node> outletSwitches = new ArrayList<>();

        if (startNode != null && !CollectionUtils.isEmpty(startNode.getChildren())) {
            for (Node child : startNode.getChildren()) {
                if (child != null && isSwitchType(child.getPsrType())) {
                    outletSwitches.add(child);
                }
            }
        }

        return outletSwitches;
    }

    /**
     * 重建拓扑结构
     */
    private ZnapTopology rebuildTopology(List<GcCon> containers, List<GcDev> devices, List<GcPower> powers) {
        ZnapTopology topology = new ZnapTopology();

        // 创建节点映射
        Map<String, Node> nodeMap = new HashMap<>();
        ArrayList<Node> nodeList = new ArrayList<>();

        // 从容器创建节点
        for (GcCon container : containers) {
            Node node = createNodeFromContainer(container);
            nodeMap.put(node.getId(), node);
            nodeList.add(node);
        }

        // 从设备创建节点
        for (GcDev device : devices) {
            Node node = createNodeFromDevice(device);
            nodeMap.put(node.getId(), node);
            nodeList.add(node);
        }

        topology.setNodeMap(nodeMap);
        topology.setNodeList(nodeList);

        // 设置起始节点（从电源点推断）
        if (!powers.isEmpty()) {
            GcPower firstPower = powers.get(0);
            Node startNode = nodeMap.get(String.valueOf(firstPower.getStationId()));
            topology.setStartNode(startNode);
        }

        return topology;
    }

    /**
     * 从容器创建节点
     */
    private Node createNodeFromContainer(GcCon container) {
        Node node = new Node(container.getRdfid(), container.getPsrid(), container.getPsrtype());
        node.setPsrName(container.getName());
        return node;
    }

    /**
     * 从设备创建节点
     */
    private Node createNodeFromDevice(GcDev device) {
        Node node = new Node(device.getRdfid(), device.getPsrid(), device.getPsrtype());
        node.setPsrName(device.getName());
        return node;
    }

    // ==================== 辅助方法 ====================

    /**
     * 提取节点ID
     */
    private Long extractNodeId(Node node) {
        if (node == null || !StringUtils.hasText(node.getPsrId())) {
            return null;
        }

        try {
            return Long.parseLong(node.getPsrId());
        } catch (NumberFormatException e) {
            log.warn("无法解析节点ID: {}", node.getPsrId());
            return null;
        }
    }

    /**
     * 提取变电站ID
     */
    private Long extractStationId(Node node) {
        return extractNodeId(node);
    }

    /**
     * 提取首节点
     */
    private Long extractHeadNode(Node node) {
        return extractNodeId(node);
    }

    /**
     * 从节点属性中提取电压等级
     */
    private Long extractBvId(Node node) {
        if (node == null || node.getProperties() == null) {
            return null;
        }

        Object bvId = node.getProperties().get("bvId");
        if (bvId instanceof Number) {
            return ((Number) bvId).longValue();
        }

        return null;
    }

    /**
     * 提取开关位置状态
     */
    private Integer extractSwitchPosition(Node node) {
        if (node == null || node.getProperties() == null) {
            return GridChangeConstants.SwitchPosition.CLOSE; // 默认合位
        }

        Object position = node.getProperties().get("position");
        if (position instanceof Number) {
            int pos = ((Number) position).intValue();
            // 验证位置值的有效性
            if (pos == GridChangeConstants.SwitchPosition.OPEN ||
                pos == GridChangeConstants.SwitchPosition.CLOSE) {
                return pos;
            }
        }

        return GridChangeConstants.SwitchPosition.CLOSE; // 默认合位
    }
}
