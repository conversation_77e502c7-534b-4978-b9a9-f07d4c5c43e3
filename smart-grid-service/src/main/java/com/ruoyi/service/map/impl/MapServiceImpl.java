package com.ruoyi.service.map.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.graphhopper.util.shapes.GHPoint;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.map.LineString;
import com.ruoyi.entity.map.NearFeeder;
import com.ruoyi.entity.map.PathSegment;
import com.ruoyi.entity.map.bo.FeederRangeQueryBo;
import com.ruoyi.entity.map.vo.CalcRouteVo;
import com.ruoyi.entity.map.vo.NeedFeederVo;
import com.ruoyi.entity.map.vo.RouteVo;
import com.ruoyi.entity.map.vo.WireEndVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.mapper.device.DeviceFeederCableMapper;
import com.ruoyi.mapper.device.DeviceFeederJkMapper;
import com.ruoyi.mapper.device.DeviceRunTowerMapper;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.map.NearFeederMapper;
import com.ruoyi.service.map.IMapService;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.util.map.GeoLineFinder;
import com.ruoyi.util.map.GisUtils;
import com.ruoyi.util.map.PathSegmentationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.entity.cost.DeviceType.*;
import static com.ruoyi.util.map.DeviceFeederConverter.convertToLineString;
import static com.ruoyi.util.map.GeoLineFinder.findTop3NearestLines;
import static com.ruoyi.util.map.LineDistanceQuery.selectLine;
import static com.ruoyi.util.map.MapAPICall.extractPolylines;
import static com.ruoyi.util.map.MapAPICall.sendGetRequest;
import static com.ruoyi.util.map.NearestPointFinder.findNearestPoint;
import static com.ruoyi.util.map.TowerCoordinateGenerator.generateTowerCoordinates;
import static java.lang.Thread.sleep;

@Service
@Slf4j
public class MapServiceImpl implements IMapService {
    @Value("${mapPath.path1}")
    private String path1;

    @Value("${mapPath.path2}")
    private String path2;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    DeviceRunTowerMapper deviceRunTowerMapper;

    @Autowired
    DeviceFeederJkMapper deviceFeederJkMapper;

    @Autowired
    DeviceFeederCableMapper deviceFeederCableMapper;

    @Autowired
    NearFeederMapper nearFeederMapper;

    @Value("${spring.profiles.default}")
    private String activeProfile = "";

    @Autowired
    private GisUtils gisUtils;

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ExecutorService executorService; // 注入自定义线程池

    private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());


    /**
     * 计算两点最优路劲
     *
     * @param start 起点坐标集合
     * @param end   结束坐标点集合
     * @param token 思级地图token
     * @return
     */
    public List<CalcRouteVo> calculateRouteAsync(List<double[]> start,
                                                 List<double[]> end,
                                                 String token) {
        //判断入参是否为空
        if (start == null || end == null || token == null) {
            return null;
        }

        List<CompletableFuture<CalcRouteVo>> futures = new ArrayList<>();
        for (int i = 0; i < start.size(); i++) {
            final int index = i; // 捕获循环索引供 lambda 使用
            // TODO 担心同时调用多个接口会封掉IP 我们这里加延时一下
            try {
                sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            futures.add(CompletableFuture.supplyAsync(() -> mapRoute(start.get(index), end.get(index), token)));
        }
        // 等待所有future完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );

        // 获取所有结果
        List<CalcRouteVo> results = allFutures.thenApply(v ->
                futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList())
        ).join();
        return results;
    }

    /***
     * 计算路劲方法
     * @param strPoint 起点坐标
     * @param endPoint 重点坐标
     * @param token   思级地图token
     * @return
     */

    public CalcRouteVo mapRoute(double[] strPoint, double[] endPoint, String token) {
        //判断入参是否为空
        if (strPoint == null || strPoint.length < 2 || endPoint == null || endPoint.length < 2 || StringUtils.isEmpty(token)) {
            throw new IllegalArgumentException("起点或终点坐标格式错误或者token异常");
        }

        String str = Arrays.toString(strPoint);  // 输出："[1.1, 2.2]"
        String end = Arrays.toString(endPoint);  // 输出："[1.1, 2.2]"
        // 如果需要去掉方括号和空格：
        str = str.replaceAll("[\\[\\]\\s]", "");
        end = end.replaceAll("[\\[\\]\\s]", "");
        String apiUrl = path1 + str + "&" + path2 + end;

        List<Node> nodeList = new ArrayList<>();
        Double totalLength = 0.0;

        try {
            // TODO 查询路径规划接口
            RouteVo polylines = new RouteVo();
            if ("dev".equals(activeProfile)) {
                //获取spring激活的配置文件
                String response = sendGetRequest(apiUrl, token);
                JSONObject root = JSON.parseObject(response);
                Object codeObj = root.get("code");
                // 如果出现思级地图访问异常，则直接将两个点变成一个路径生成杆塔
                if (codeObj != null && !codeObj.equals("200")) {
                    List<GHPoint> ghPoints = new ArrayList<>();
                    ghPoints.add(new GHPoint(strPoint[1], strPoint[0]));
                    ghPoints.add(new GHPoint(endPoint[1], endPoint[0]));
                    polylines.setRouteList(ghPoints);
                } else {
                    //解析路径坐标集合
                    log.warn("地图API响应为空，生成默认两点一线路径");
                    polylines = extractPolylines(response);
                }
            } else {
                Map<String, String> parm = new HashMap<>();
                parm.put("origin", str);
                parm.put("destination", end);
                // 解析路径坐标集合
                log.warn("地图API响应为空，生成默认两点一线路径");
                String commonGis = gisUtils.commonGis(parm);
                if (StringUtils.isEmpty(commonGis)) {
                    List<GHPoint> ghPoints = new ArrayList<>();
                    ghPoints.add(new GHPoint(strPoint[1], strPoint[0]));
                    ghPoints.add(new GHPoint(endPoint[1], endPoint[0]));
                    polylines.setRouteList(ghPoints);
                } else {
                    polylines = extractPolylines(commonGis);
                }
            }

            //计算长度
            totalLength = countLength(polylines.getRouteList());
            //分析杆塔
            List<GHPoint> gtList = generateTowerCoordinates(polylines.getRouteList());

            //判断起点和终点是否和生产的路径对应上，如果对不上则手动添加起始点和终点
            double[] routeStrPoint = {polylines.getRouteList().get(0).getLon(), polylines.getRouteList().get(0).getLat()};
            double[] routeEndPoint = {polylines.getRouteList().get(polylines.getRouteList().size() - 1).getLon(), polylines.getRouteList().get(polylines.getRouteList().size() - 1).getLat()};
            if (routeStrPoint[0] != strPoint[0] || routeStrPoint[1] != strPoint[1]) {
                GHPoint point = new GHPoint(strPoint[1], strPoint[0]);
                polylines.getRouteList().add(0, point);
                gtList.add(0, point);
            }
            if (routeEndPoint[0] != endPoint[0] || routeEndPoint[1] != endPoint[1]) {
                GHPoint point = new GHPoint(endPoint[1], endPoint[0]);
                polylines.getRouteList().add(point);
                gtList.add(point);
            }

            // 将路径和杆塔转化成后续node需要的list
            PathSegmentationUtil pathSegmentationUtil = new PathSegmentationUtil();
            List<PathSegment> list = pathSegmentationUtil.segmentRouteByTowers(polylines.getRouteList(), gtList);


            // 生产nodeList
            int count = 0;
            for (PathSegment pathSegment : list) {
                if (count == 0) {
                    Node node1 = createDevice(pathSegment.getStartTower(), "wlgt", "杆塔");
                    nodeList.add(node1);
                    Node node2 = createEdge(pathSegment, "dxd", "架空线");
                    nodeList.add(node2);
                    Node node3 = createDevice(pathSegment.getEndTower(), "wlgt", "杆塔");
                    nodeList.add(node3);
                } else {
                    Node node2 = createEdge(pathSegment, "dxd", "架空线");
                    nodeList.add(node2);
                    Node node3 = createDevice(pathSegment.getEndTower(), "wlgt", "杆塔");
                    nodeList.add(node3);
                }
                count++;
            }
            int nodeCount = 0;
            for (Node node : nodeList) {
                if (node.getPsrType().equals("wlgt")) {
                    if (nodeCount == 0) {
                        node.addEdge(nodeList.get(nodeCount + 1), true);
                        nodeCount = nodeCount + 1;
                        continue;
                    }
                    if (nodeCount == nodeList.size() - 1) {
                        node.addEdge(nodeList.get(nodeCount - 1), false);
                    } else {
                        node.addEdge(nodeList.get(nodeCount + 1), true);
                        node.addEdge(nodeList.get(nodeCount - 1), false);
                    }
                }
                nodeCount = nodeCount + 1;
            }
        } catch (Exception e) {
            return null;
        }
        HashMap<String, Object> objectHashMap = new HashMap<>();

        objectHashMap.put("length", totalLength);
        objectHashMap.put("contactNodeList", nodeList);
        return new CalcRouteVo(totalLength, nodeList);
    }


    /**
     * 查询一条线附近的线,根据num判断，如果是-1则返回所有附近的线（和数据库所有的线路都比对，效率低）
     *
     * @param feederRangeQueryBo
     * @return
     */
    @Override
    public List<NeedFeederVo> feederRangeQuery(FeederRangeQueryBo feederRangeQueryBo) {
        // 目标线路

        List<List<DeviceFeeder>> deviceFeederList = getAllPageDeviceFeedersAsync();

        if (CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }
        List<CompletableFuture<List<DeviceFeeder>>> futures = new ArrayList<>();

        if (CollectionUtils.isEmpty(feederRangeQueryBo.getCoordinateList())) {
            LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederRangeQueryBo.getPsrId());

            String geoList = feederDeviceMapper.selectOne(lambdaQueryWrapper).getGeoList();
            if (StringUtils.isBlank(geoList)) {
                return null;
            }

            feederRangeQueryBo.setCoordinateList(CoordinateConverter.split(geoList));
        }
        List<NeedFeederVo> NeedFeederVo = new ArrayList<>();
        for (int i = 0; i < deviceFeederList.size(); i++) {
            int finalI = i;

            futures.add(CompletableFuture.supplyAsync(() -> selectLine(feederRangeQueryBo, deviceFeederList.get(finalI),NeedFeederVo)));
        }
        List<DeviceFeeder> feederList = futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        if (feederRangeQueryBo.getNum()==-1) {
            selectLine(feederRangeQueryBo, distinctByPsrId(feederList),NeedFeederVo);
            return distinctByPsrIdVo(NeedFeederVo);
        }

        return distinctByPsrIdVo(NeedFeederVo);
    }

    /**
     * 根据杆塔id查询两端导线
     */
    @Override
    public List<WireEndVo> selectWireEnd(String psrId) {
        List<WireEndVo> wireEndVoList = getWireEndVos(psrId);

        if (CollectionUtils.isEmpty(wireEndVoList)) {
            LambdaQueryWrapper<DeviceRunTower> runTowerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            runTowerLambdaQueryWrapper.eq(DeviceRunTower::getAstId, psrId);
            DeviceRunTower deviceRunTower = deviceRunTowerMapper.selectOne(runTowerLambdaQueryWrapper);
            if (deviceRunTower != null) {
                return getWireEndVos(deviceRunTower.getPsrId());
            }
        }
        return wireEndVoList;
    }

    /**
     * 根据杆塔id查询两端导线
     */
    @Override
    public String selectPsrId(String psrId, String psrType) {
        if (psrType.equals(WLGT)) {
            LambdaQueryWrapper<DeviceRunTower> runTowerLambdaQueryWrapper = new LambdaQueryWrapper<>();
            runTowerLambdaQueryWrapper.eq(DeviceRunTower::getAstId, psrId);
            DeviceRunTower deviceRunTower = deviceRunTowerMapper.selectOne(runTowerLambdaQueryWrapper);
            if (deviceRunTower != null) {
                return deviceRunTower.getPsrId();
            }
            return psrId;
        }
        return null;
    }

    @NotNull
    private List<WireEndVo> getWireEndVos(String psrId) {
        //查询杆塔相关联的架空线
        LambdaQueryWrapper<DeviceFeederJk> jkLambdaQueryWrapper = new LambdaQueryWrapper<>();
        jkLambdaQueryWrapper.eq(DeviceFeederJk::getStartPole, psrId);
        jkLambdaQueryWrapper.or();
        jkLambdaQueryWrapper.eq(DeviceFeederJk::getStopPole, psrId);
        List<DeviceFeederJk> deviceFeederJkList = deviceFeederJkMapper.selectList(jkLambdaQueryWrapper);

        List<WireEndVo> wireEndVoList = new ArrayList<>();
        //重新封装返回公共实体
        if (CollectionUtils.isNotEmpty(deviceFeederJkList)) {
            for (DeviceFeederJk deviceFeederJk : deviceFeederJkList) {
                wireEndVoList.add(new WireEndVo(deviceFeederJk.getPsrId(), deviceFeederJk.getName(), JK));
            }
        }

        //查询杆塔相关联的导线段
        LambdaQueryWrapper<DeviceFeederCable> cableLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cableLambdaQueryWrapper.eq(DeviceFeederCable::getStartPosition, psrId);
        cableLambdaQueryWrapper.or();
        cableLambdaQueryWrapper.eq(DeviceFeederCable::getEndPosition, psrId);
        List<DeviceFeederCable> deviceFeederCableList = deviceFeederCableMapper.selectList(cableLambdaQueryWrapper);
        //重新封装返回公共实体
        if (CollectionUtils.isNotEmpty(deviceFeederCableList)) {
            for (DeviceFeederCable deviceFeederCable : deviceFeederCableList) {
                wireEndVoList.add(new WireEndVo(deviceFeederCable.getPsrId(), deviceFeederCable.getName(), CABLE));
            }
        }
        return wireEndVoList;
    }

    /**
     * 快捷查询附近线路（从是数据库最近的线路表查询的）
     *
     * @param feederId
     * @return
     */
    public List<DeviceFeeder> selectNearFeeder(String feederId, Double radius) {
        LambdaQueryWrapper<NearFeeder> nearFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        nearFeederLambdaQueryWrapper.eq(NearFeeder::getFeederId, feederId);
        NearFeeder nearFeeder = nearFeederMapper.selectOne(nearFeederLambdaQueryWrapper);

        if (nearFeeder == null) {
            return null;
        }

        List<String> idList = Arrays.stream(nearFeeder.getNearFeederId().split(","))
                .filter(s -> !s.isEmpty()) // 过滤空字符串
                .map(String::trim) // 去除首尾空格
                .collect(Collectors.toList());

        LambdaQueryWrapper<DeviceFeeder> deviceFeederLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deviceFeederLambdaQueryWrapper.in(DeviceFeeder::getPsrId, idList);
//        List<DeviceFeeder> deviceFeederList = feederDeviceMapper.selectList(deviceFeederLambdaQueryWrapper);
        List<DeviceFeeder> deviceFeederList = feederDeviceMapper.selectList(deviceFeederLambdaQueryWrapper);

        FeederRangeQueryBo feederRangeQueryBo = new FeederRangeQueryBo();
        feederRangeQueryBo.setPsrId(feederId);
        feederRangeQueryBo.setRange(radius);
        feederRangeQueryBo.setNum(-1);

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }

        return selectNeedFeeder(feederRangeQueryBo, deviceFeederList);
    }


    /**
     * 根据已有的线路，和线路集合查询最近的
     *
     * @param feederRangeQueryBo
     * @return
     */

    public List<DeviceFeeder> selectNeedFeeder(FeederRangeQueryBo feederRangeQueryBo, List<DeviceFeeder> deviceFeederList) {


        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederRangeQueryBo.getPsrId());
        feederRangeQueryBo.setCoordinateList(CoordinateConverter.split(feederDeviceMapper.selectOne(lambdaQueryWrapper).getGeoList()));
        List<NeedFeederVo> needFeederVos =new ArrayList<>();
        return selectLine(feederRangeQueryBo, deviceFeederList,needFeederVos);
    }

    /**
     * 附近线vo转成DeviceFeederList
     */
    public List<DeviceFeeder> toDeviceFeederList(List<NeedFeederVo> list){
        List<DeviceFeeder> deviceFeederList =new ArrayList<>();
        for (NeedFeederVo needFeederVo : list) {
            DeviceFeeder deviceFeeder = new DeviceFeeder();
            deviceFeeder.setPsrId(needFeederVo.getPsrId());
            deviceFeeder.setName(needFeederVo.getFeedName());
            deviceFeeder.setVoltageLevel(needFeederVo.getVoltageLevel());
            deviceFeeder.setLength(needFeederVo.getLength());
            deviceFeeder.setStartStation(needFeederVo.getStartStation());
            deviceFeeder.setSupplyArea(needFeederVo.getSupplyArea());
            deviceFeeder.setSupplyRadius(needFeederVo.getSupplyRadius());
            deviceFeeder.setFeederRateCapacity(needFeederVo.getFeederRateCapacity());
            deviceFeederList.add(deviceFeeder);
        }
        return deviceFeederList;
    }



    /**
     * 线找线
     *
     * @param doubleArrays  目标点坐标集合
     * @param num           最近几条的数量
     * @param filteredList  线路集合
     * @param extraFeederId 需要过滤的线路id
     * @return
     */
    public List<List<NeedFeederVo>> pointByParallel(List<double[]> doubleArrays, Integer num, List<DeviceFeeder> filteredList, String extraFeederId, List<String> ids) {
        // 为每个 double [] 数组创建一个任务
        if (filteredList == null) {
            filteredList = feederDeviceMapper.selectList();
        }
        if (StringUtils.isNotBlank(extraFeederId)) {
            filteredList = filteredList.stream().filter(device -> !extraFeederId.equals(device.getPsrId())).collect(Collectors.toList());
        }

        List<CompletableFuture<List<NeedFeederVo>>> futures = new ArrayList<>();

        for (int i = 0; i < doubleArrays.size(); i++) {
            final int index = i; // 捕获循环索引供 lambda 使用

            // 提交任务到线程池
            List<DeviceFeeder> finalFilteredList = filteredList;
            futures.add(CompletableFuture.supplyAsync(() -> {
                double[] array = doubleArrays.get(index);
                return pointBy(array, num, finalFilteredList, ids);
            }, executor));
        }

        // 保持结果的嵌套结构
        return futures.stream().map(future -> {
            try {
                return future.get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("任务执行失败", e);
            }
        }).collect(Collectors.toList());
    }


    /**
     * 点找线，根据一个点去查寻最近的线路的最近杆塔
     *
     * @param doubles
     * @param num
     * @param deviceFeederList
     * @return
     */

    public List<NeedFeederVo> pointBy(double[] doubles, Integer num, List<DeviceFeeder> deviceFeederList, List<String> ids) {

        if (CollectionUtils.isEmpty(deviceFeederList)) {
            return null;
        }
        List<LineString> stringList = new ArrayList<>();
        for (DeviceFeeder deviceFeeder : deviceFeederList) {
            LineString lineString = convertToLineString(deviceFeeder);
            if (lineString == null) {
                System.out.println("问题id" + deviceFeeder.getPsrId());
                continue;
            }
            stringList.add(lineString);
        }
        // 最近的三条线
        List<GeoLineFinder.NearestPointResult> nearestLines = findTop3NearestLines(doubles, stringList, num);

        List<NeedFeederVo> result = new ArrayList<>();

        for (GeoLineFinder.NearestPointResult nearestLine : nearestLines) {

            List<DeviceRunTower> runTowers = feederDeviceMapper.selectRunTowers(nearestLine.getLineId());
            if (CollectionUtils.isEmpty(runTowers)) {
                continue;
            }
            List<DeviceRunTower> filter = runTowers.stream().collect(Collectors.toMap(DeviceRunTower::getPsrId,  // 根据id去重
                    Function.identity(), (existing, replacement) -> existing  // 遇到重复时保留已存在的
            )).values().stream().collect(Collectors.toList());

            List<DeviceWlgt> preliminary = feederDeviceMapper.selectwlgtList(filter.stream().map(DeviceRunTower::getAstId).collect(Collectors.toList()));

            List<DeviceWlgt> wlgtList = preliminary.stream().filter(entity -> ids == null || !ids.contains(entity.getAstId())) // 过滤条件：实体ID不在idList中
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(wlgtList)) {
                continue;
            }

            Map<String, String> astIdToGeoPositionMap = wlgtList.stream().filter(wlgt -> wlgt.getGeoPositon() != null) // 过滤掉 geoPositon 为 null 的元素
                    .collect(Collectors.toMap(DeviceWlgt::getAstId, DeviceWlgt::getGeoPositon, (existing, replacement) -> existing));

            Map.Entry<String, String> nearest = findNearestPoint(nearestLine.getPoint(), astIdToGeoPositionMap, true);

            NeedFeederVo feeder = new NeedFeederVo();
            feeder.setPsrId(nearestLine.getLineId());
            feeder.setFeedName(nearestLine.getLineName());
            String[] parts = nearest.getValue().split(",");
            feeder.setRecentlyPoint(new double[]{Double.parseDouble(parts[0].trim()),  // 经度
                    Double.parseDouble(parts[1].trim())   // 纬度
            });
            feeder.setDeviceId(nearest.getKey());
            result.add(feeder);
        }

        return result;
    }


    /**
     * 计算路径长度
     *
     * @param ghPoints
     * @return
     */
    public static Double countLength(List<GHPoint> ghPoints) {
        // 创建GeometryFactory实例（使用默认精度）
        GeometryFactory factory = new GeometryFactory();

        // 方式2: 使用ArrayList动态批量添加
        List<Coordinate> coordList = new ArrayList<>();
        for (GHPoint ghPoint : ghPoints) {
            Coordinate coordinate = new Coordinate();
            coordinate.setY(ghPoint.getLat());
            coordinate.setX(ghPoint.getLon());
            coordList.add(coordinate);
        }

        Coordinate[] coords = coordList.toArray(new Coordinate[0]);

        // 创建LineString对象
        org.locationtech.jts.geom.LineString lineString = factory.createLineString(coords);
        lineString.setSRID(4326);
        // 计算长度（单位取决于坐标系，例如经纬度坐标系下单位是度，需转换为米）
        double length = haversineLength(lineString);
        return length;
    }

    public static double haversineLength(org.locationtech.jts.geom.LineString line) {
        final double R = 6371000; // 地球半径（米）
        double totalDistance = 0.0;

        for (int i = 0; i < line.getNumPoints() - 1; i++) {
            Coordinate c1 = line.getCoordinateN(i);
            Coordinate c2 = line.getCoordinateN(i + 1);

            double dLat = Math.toRadians(c2.y - c1.y);
            double dLon = Math.toRadians(c2.x - c1.x);
            double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                    Math.cos(Math.toRadians(c1.y)) * Math.cos(Math.toRadians(c2.y)) *
                            Math.sin(dLon / 2) * Math.sin(dLon / 2);
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            totalDistance += R * c;
        }
        return totalDistance;
    }

    /**
     * 计算杆塔node
     *
     * @param ghPoint
     * @param type
     * @param name
     * @return
     */
    private static Node createDevice(GHPoint ghPoint, String type, String name) {
        NodeFactory nodeFactory = new NodeFactory();
        Node node = new Node(UUID.randomUUID().toString());
        node.setGeometry(nodeFactory.createDevice(node.getId(), ghPoint.getLon(), ghPoint.getLat()).getGeometry());
        node.setPsrType(type);
        node.setPsrName(name);
        node.setType(Node.TYPE_SELF);
        node.setShapeKey(NodeConstants.SHAPE_KEY_WLGT);

        node.setProperties(new HashMap<String, Object>() {{
            put("type", NodeConstants.SHAPE_KEY_WLGT);
            put("name", name);
        }});
        return node;
    }

    /**
     * 计算线路node
     *
     * @param pathSegment
     * @param type
     * @param name
     * @return
     */
    private static Node createEdge(PathSegment pathSegment, String type, String name) {
        NodeFactory nodeFactory = new NodeFactory();
        Node node = new Node(UUID.randomUUID().toString());
        node.setEdge(true);
        double[][] coords = {{pathSegment.getStartTower().getLon(), pathSegment.getStartTower().getLat()}, {pathSegment.getEndTower().getLon(), pathSegment.getEndTower().getLat()}};
        node.setGeometry(nodeFactory.createEdge(node.getId(), coords).getGeometry());
        node.setPsrType(type);
        node.setPsrName(name);
        node.setType(Node.TYPE_SELF);
        node.setLineType(NodeConstants.LINE_TYPE_LINEAR);
        node.setShapeKey(NodeConstants.SHAPE_KEY_FEEDER_JK);
        node.setProperties(new HashMap<String, Object>() {{
            put("type", NodeConstants.SHAPE_KEY_FEEDER_JK);
            put("name", name);
        }});
        return node;
    }


    /**
     * 动态分段异步查询所有线路
     *
     * @return
     */
    public List<List<DeviceFeeder>> getAllPageDeviceFeedersAsync() {
        // 1. 先查询总记录数
        int totalCount = getTotalCount();

        // 2. 根据总记录数和每段大小计算分段数
        int batchSize = 1000; // 每批查询数量
        int batches = (int) Math.ceil((double) totalCount / batchSize);

        // 3. 创建异步任务列表
        List<CompletableFuture<List<DeviceFeeder>>> futures = new ArrayList<>();
        for (int i = 0; i < batches; i++) {
            final int offset = i * batchSize;
            futures.add(CompletableFuture.supplyAsync(() -> queryRange(offset, batchSize), executorService));
        }

        return futures.stream().map(CompletableFuture::join)  // 将每个 future 转换为 List<DeviceFeeder>
                .collect(Collectors.toList());
    }

    /**
     * 配合动态分段异步查询所有线路——查询总记录数
     *
     * @return
     */
    private int getTotalCount() {
        return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM device_feeder", Integer.class);
    }

    /**
     * 配合动态分段异步查询所有线路——分页查询方法
     *
     * @param offset
     * @param limit
     * @return
     */
    private List<DeviceFeeder> queryRange(int offset, int limit) {
        String sql = "SELECT * FROM device_feeder LIMIT ? OFFSET ?";
        return jdbcTemplate.query(sql, new Object[]{limit, offset}, new BeanPropertyRowMapper<>(DeviceFeeder.class));
    }


//    /**
//     * 将查询的最近线路重新封装成NeedFeederVo格式
//     *
//     * @param feederList
//     * @return
//     */
//    public List<NeedFeederVo> setFeeder(List<DeviceFeeder> feederList) {
//        List<NeedFeederVo> feederVoList = new ArrayList<>();
//        for (DeviceFeeder deviceFeeder : feederList) {
//            NeedFeederVo feederVo = new NeedFeederVo();
//            feederVo.setPsrId(deviceFeeder.getPsrId());
//            feederVo.setDeviceId(deviceFeeder.getDeviceId());
//            feederVo.setPoint1(deviceFeeder.getPoint1());
//            feederVo.setPoint2(deviceFeeder.getPoint2());
//            feederVo.setRecentlyPoint(deviceFeeder.getRecentlyPoint());
//            feederVo.setGeoCoordinateList(deviceFeeder.getGeoCoordinateList());
//        }
//        return feederVoList;
//    }

    /**
     * 按照线路id对线路list去重
     *
     * @param feederList
     * @return
     */
    public List<NeedFeederVo> distinctByPsrIdVo(List<NeedFeederVo> feederList) {
        if (feederList == null || feederList.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> seenPsrIds = new HashSet<>();
        List<NeedFeederVo> list = feederList.stream().filter(feeder -> seenPsrIds.add(feeder.getPsrId())).collect(Collectors.toList());
        return list;
    }

    /**
     * 按照线路id对线路list去重
     *
     * @param feederList
     * @return
     */
    public List<DeviceFeeder> distinctByPsrId(List<DeviceFeeder> feederList) {
        if (feederList == null || feederList.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> seenPsrIds = new HashSet<>();
        List<DeviceFeeder> list = feederList.stream().filter(feeder -> seenPsrIds.add(feeder.getPsrId())).collect(Collectors.toList());
        return list;
    }
}


