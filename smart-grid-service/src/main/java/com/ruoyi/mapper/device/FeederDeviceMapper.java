package com.ruoyi.mapper.device;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.vo.FeederNtVo;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.DoubleTypeHandler;

import java.util.List;

public interface FeederDeviceMapper extends BaseMapperPlus<FeederDeviceMapper, DeviceFeeder, DeviceFeeder> {

    @Select("SELECT * FROM device_feeder where grid_code = #{code} ")
    List<DeviceFeeder> selectFeederByCode(String code);

    @Select("SELECT * FROM device_feeder where psr_id = #{id} ")
   DeviceFeeder selectById(String id);

    @Select("<script>" +
            "SELECT * FROM device_pole_transformer WHERE feeder IN " +
            "<foreach item='item' collection='feeders' open='(' separator=',' close=')'>" +
            "   #{item}" +
            "</foreach>" +
            "</script>")
    List<DevicePoleTransformer> selectPoleTransformerByFeederIds(@Param("feeders")List<String> ids);
    @Select("<script>" +
            "SELECT * FROM device_station_transformer WHERE feeder IN " +
            "<foreach item='item' collection='feeders' open='(' separator=',' close=')'>" +
            "   #{item}" +
            "</foreach>" +
            "</script>")
    List<DeviceStationTransformer> selectStationTransformerByFeederIds(@Param("feeders")List<String> ids);

    @Select("select * FROM device_run_tower where feeder = #{psrId}")
    List<DeviceRunTower> selectRunTowers(String psrId);

    @Select("<script>" +
            "SELECT * FROM device_wlgt WHERE ast_id IN " +
            "<foreach item='item' collection='feeders' open='(' separator=',' close=')'>" +
            "   #{item}" +
            "</foreach>" +
            "</script>")
    List<DeviceWlgt> selectwlgtList(@Param("feeders")List<String> astId);
    @Select("select * FROM device_wlgt where geo_positon = #{str}")
    DeviceWlgt selectWlgt(String str);

    @Insert({
            "<script>",
            "INSERT INTO near_feeder (feeder_id, near_feeder_id) VALUES ",
            "<foreach collection='itemIds' item='itemId' separator=','>",
            "(#{feederId}, #{itemId})",
            "</foreach>",
            "</script>"
    })
    void insertNear(@Param("feederId") String feederId,
                     @Param("itemIds") List<String> idList);

    @Select("select dict_label from sys_dict_data where dict_type = 'voltage_level' AND dict_value = #{voltageLevel}")

    String feederVoltage(String voltageLevel);
 @Select("<script>" +
         "select * from device_substation where psr_id IN " +
         "<foreach item='item' collection='itemIds' open='(' separator=',' close=')'>" +
         "   #{item}" +
         "</foreach>" +
         "</script>")
    List<DeviceSubstation> selectDeviceSubstations(  @Param("itemIds") List<String> idList);

    @Select("<script>" +
            "SELECT * FROM device_feeder where psr_id IN " +
            "<foreach item='item' collection='itemIds' open='(' separator=',' close=')'>" +
            "   #{item}" +
            "</foreach>" +
            "</script>")
    List<DeviceFeeder> selectByIds( @Param("itemIds") List<String> idList);

    @Results({
            @Result(property = "hisMaxLoadRate", column = "his_max_load_rate", typeHandler = DoubleTypeHandler.class)
    })
    @Select("select his_max_load_rate from nt_feeder where psr_id  = #{feederId}")
    Double selectLoad(String feederId);

    /**
     * 根据线路ID集合查询线路一些相关运行值
     */
    List<FeederNtVo> selectFeederNtsByFeederIds(@Param("feederIds") List<String> feederIds);

    /**
     * 根据线路ID集合查询线路一些相关运行值
     */
    FeederNtVo selectFeederNtsByFeederId(@Param("feederId") String feederId);
}
