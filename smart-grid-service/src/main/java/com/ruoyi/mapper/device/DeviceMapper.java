package com.ruoyi.mapper.device;

import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.bo.DeviceAccessPointBo;
import com.ruoyi.entity.device.vo.Breaker;
import com.ruoyi.entity.device.vo.BusBar;
import com.ruoyi.entity.device.vo.FeederVo;
import com.ruoyi.entity.device.vo.Middle;
import org.apache.ibatis.annotations.*;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface DeviceMapper {
    /**
     * 根据feederId查询设备线路mrid和变电站id
     * @param feederId
     * @return
     */
    @Select("SELECT tln.mrid, tln.substation " +
            "FROM ems_ld_link ell " +
            "LEFT JOIN topo_load_nj tln ON ell.id = tln.mrid::bigint " +
            "WHERE ell.psrid = CONCAT('PD_dkx_', #{feederId})")
    @Results({
            @Result(property = "id1", column = "mrid"),
            @Result(property = "id2", column = "substation")
    })
    Middle selectFeederMrIdAndSubstationIdInfo(String feederId);

    /**
     * 根据feederId查询母线id和间隔id
     * @param psrIdSuffix
     * @return
     */
    @Select("SELECT ttr.start_break,ttr.busbar " +
            "FROM topo_trace_real ttr " +
            "LEFT JOIN ems_ld_link ell ON ttr.load_mrid::bigint = ell.id " +
            "WHERE ell.psrid = CONCAT('PD_dkx_', #{psrIdSuffix})")
    @Results({
            @Result(property = "id1", column = "start_break"),
            @Result(property = "id2", column = "busbar")
    })
    Middle selectBusBarIdAndBreakerIdInfo(String psrIdSuffix);

    /**
     * 查询变电站名称
     * @param id2
     * @return
     */
    @Select("SELECT name " +
            "FROM topo_substation_nj " +
            "WHERE mrid = #{id2}  ")
    String selectSubstationName(String id2);

    /**
     * 查询间隔信息
     * @param id1
     * @return
     */
    @Select("SELECT mrid, name   \n" +
            "FROM topo_breaker_nj  \n" +
            "WHERE mrid = #{id1}")
    @Results({
            @Result(property = "breakerId", column = "mrid"),
            @Result(property = "breakerName", column = "name")
    })
    Breaker seletBreaker(String id1);

    /**
     * 查询母线信息
     * @param id2
     * @return
     */
    @Select("SELECT mrid,name " +
            "FROM topo_busbar_section_nj  " +
            "WHERE mrid = #{id2}")
    @Results({
            @Result(property = "busBarId", column = "mrid"),
            @Result(property = "busBarName", column = "name")
    })
    BusBar seletBusBar(String id2);

    /**
     * 根据变电站名称查询其id
     * @param substationName
     * @return
     */
    @Select("SELECT mrid ,  " +
            "       name " +
            "FROM topo_substation_nj  " +
            "WHERE name LIKE CONCAT('%', #{substationName}, '%')")

    String selectSubstationId(String substationName);

    /**
     * 查询变电站下的所有线路
     * @param substationId
     * @return
     */
    @Select("SELECT mrid ," +
            "       name  " +
            "FROM topo_load_nj " +
            "WHERE substation = #{substationId}")
    @Results({
            @Result(property = "feederId", column = "mrid"),
            @Result(property = "feederName", column = "name")
    })
    List<FeederVo> selectFeederVoList(String substationId);

    /**
     * 查询变电站下的所有间隔
     * @param substationId
     * @return
     */
    @Select("SELECT mrid  ," +
            "       name   " +
            "FROM topo_breaker_nj " +
            "WHERE substation =#{substationId}")
    @Results({
            @Result(property = "breakerId", column = "mrid"),
            @Result(property = "breakerName", column = "name")
    })
    List<Breaker> selectBreakerList(String substationId);

    /**
     * 查询线路相关的母线
     * @param idList
     * @return
     */
    @Select({
            "<script>",
            "SELECT busbar, busbar_name ",
            "FROM topo_trace_real ",
            "WHERE load_mrid IN ",
            "<foreach item='item' collection='idList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(property = "busBarId", column = "busbar"),
            @Result(property = "busBarName", column = "busbar_name")
    })
    List<BusBar> selectBusBarListByFeeder(@Param("idList") List<String> idList);

    /**
     * 查询间隔相关的母线
     * @param idList
     * @return
     */
    @Select({
            "<script>",
            "SELECT busbar , busbar_name ",
            "FROM topo_trace_real",
            "WHERE start_break IN",
            "<foreach item='item' collection='idList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(property = "busBarId", column = "busbar"),
            @Result(property = "busBarName", column = "busbar_name")
    })
    List<BusBar> selectBusBarListByBreak(@Param("idList")List<String> idList);

    /**
     * 批量查询柱上变压器设备信息（PostgreSQL兼容版本）
     *
     * @param ids 设备ID列表
     * @return 设备信息列表，按照输入ID的顺序返回
     *
     * @apiNote 使用PostgreSQL的CASE WHEN语法实现结果排序，确保返回顺序与输入ID列表顺序一致
     * @warning 当ID列表很大时（超过1000个），应考虑分批查询以避免性能问题
     */
    @Select({
            "<script>",
            /* 基础查询语句 */
            "SELECT * FROM device_pole_transformer",

            /* IN条件查询 - 使用foreach动态生成IN条件 */
            "WHERE psr_id IN",
            "  <foreach collection='ids' item='id' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",

            /* PostgreSQL排序方案 - 使用CASE WHEN实现自定义排序 */
            "ORDER BY CASE psr_id",
            /* 为每个ID指定一个序号，保持原始顺序 */
            "  <foreach collection='ids' item='id' index='index'>",
            "    WHEN #{id} THEN #{index}",  // 匹配到的ID使用其在列表中的索引作为排序值
            "  </foreach>",
            "  ELSE 999999 END",  // 未匹配到的记录放在最后
            "</script>"
    })
    List<DevicePoleTransformer> selectDevicePoleTransformer(@Param("ids") List<String> ids);

    /**
     * 批量查询站内变压器设备信息（PostgreSQL兼容版本）
     *
     * @param ids 设备ID列表
     * @return 设备信息列表，按照输入ID的顺序返回
     *
     * @see #selectDevicePoleTransformer 方法说明相同
     */
    @Select({
            "<script>",
            "SELECT * FROM device_station_transformer",
            "WHERE psr_id IN",
            "  <foreach collection='ids' item='id' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",
            "ORDER BY CASE psr_id",
            "  <foreach collection='ids' item='id' index='index'>",
            "    WHEN #{id} THEN #{index}",
            "  </foreach>",
            "  ELSE 999999 END",
            "</script>"
    })
    List<DeviceStationTransformer> selectDeviceStationTransformer(@Param("ids") List<String> ids);

    /**
     * 批量查询所用变压器设备信息（PostgreSQL兼容版本）
     *
     * @param ids 设备ID列表
     * @return 设备信息列表，按照输入ID的顺序返回
     *
     * @see #selectDevicePoleTransformer 方法说明相同
     */
    @Select({
            "<script>",
            "SELECT * FROM station_service_transformer",
            "WHERE psr_id IN",
            "  <foreach collection='ids' item='id' open='(' separator=',' close=')'>",
            "    #{id}",
            "  </foreach>",
            "ORDER BY CASE psr_id",
            "  <foreach collection='ids' item='id' index='index'>",
            "    WHEN #{id} THEN #{index}",
            "  </foreach>",
            "  ELSE 999999 END",
            "</script>"
    })
    List<StationServiceTransformer> selectStationServiceTransformer(@Param("ids") List<String> ids);
    /**
     * 查询中压用户接入点的容量
     * @param
     * @return
     */
    @Select({
            "<script>",
            "SELECT",
            "    psr_id,",
            "    COALESCE(capacity, '0') AS capacity",
            "FROM device_access_point",
            "WHERE psr_id IN",
            "<foreach item='item' collection='psrIds' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<DeviceAccessPointBo> selectJoinEcCapacity(@Param("psrIds") List<String> psrIds);


    /**
     * 根据间隔ID查询同一母线上的其他间隔
     * @param breakId 间隔ID
     * @return 同一母线上的其他间隔列表
     */
    @Select({
            "<script>",
            "SELECT",
            "    t.start_break AS intervalId,",
            "    t.start_break_name AS intervalName",
            "FROM",
            "    topo_trace_real t",
            "WHERE",
            "    t.busbar = (",
            "        SELECT",
            "            busbar",
            "        FROM",
            "            topo_trace_real",
            "        WHERE",
            "            start_break = #{breakId}",
            "    )",
            "</script>"

    })
    @Results({
            @Result(property = "breakerId", column = "intervalId"),
            @Result(property = "breakerName", column = "intervalName")
    })
    List<Breaker> selectBreakIds(String breakId);

    /**
     * 根据PSID查询地理位置信息
     * @param psrId 塔杆PSID
     * @return 地理位置字符串
     */
    @Select("SELECT " +
            "   w.geo_positon " +
            "FROM " +
            "   device_run_tower t " +
            "LEFT JOIN " +
            "   device_wlgt w ON t.ast_id = w.ast_id " +
            "WHERE " +
            "   t.psr_id = #{psrId}")
    String selectWLGTCoords(String psrId);

    /**
     * 根据PSID查询地理位置信息
     * @param psrId 终端PSID
     * @return 地理位置字符串
     */
    @Select("SELECT geo_position FROM device_cable_terminal_joint where psr_id = #{psrId}")
    String selectSDDLCoords(String psrId);
}
