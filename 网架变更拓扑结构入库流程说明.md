# 网架变更拓扑结构入库流程说明

## 概述

本文档描述了将网架结构转为军哥算法的拓扑结构入库的完整流程设计，确保代码规范性和可维护性。

## 整体架构

```
Controller层 -> Service层 -> Converter层 -> Validator层 -> Mapper层
```

### 核心组件

1. **ProblemSchemeController**: 网架结构变更控制器
2. **IGridChangeService**: 网架变更服务接口
3. **GridChangeServiceImpl**: 网架变更服务实现
4. **TopologyConverter**: 拓扑结构转换器
5. **GridChangeValidator**: 数据验证器
6. **GridChangeConstants**: 常量定义

## 数据流程

### 1. 数据输入
- 接收问题ID、馈线ID、方案名称等参数
- 获取操作数据JSON字符串
- 生成ZnapTopology拓扑结构

### 2. 数据验证
- 验证拓扑分析结果完整性
- 检查节点数据有效性
- 验证设备类型支持情况

### 3. 数据转换
- 将Node节点转换为GcCon容器实体
- 将Node节点转换为GcDev设备实体
- 生成GcPower电源点数据
- 提取设备参数（开关状态等）

### 4. 数据存储
- 创建或获取方案(gc_schema)
- 创建新版本(gc_version)
- 保存容器数据(gc_con)
- 保存设备数据(gc_dev)
- 保存电源点数据(gc_power)
- 保存设备参数(gc_dev_para_cb等)

## 核心表结构

### gc_schema (方案表)
- id: 方案ID
- name: 方案名
- description: 方案描述
- problem_id: 问题ID
- status: 状态

### gc_version (版本表)
- id: 版本ID
- name: 版本名
- version_num: 版本号
- schema_id: 方案ID
- is_current: 是否当前版本

### gc_con (容器表)
- id: 容器ID
- name: 容器名
- psrid: 设备PSRID
- psrtype: 设备类型
- type: 容器类型标识(1变电站 2馈线 3柜子 4组合开关)
- version_id: 版本ID

### gc_dev (设备表)
- id: 设备ID
- name: 设备名
- psrid: 设备PSRID
- psrtype: 设备类型
- type: 设备类型标识(0主网开关 1配网开关 2刀闸 3熔断器 4母线 5地刀 6电缆头 7负荷 8杆塔 9馈线段 10配变 11绕组)
- version_id: 版本ID
- container_id: 所属容器ID

### gc_power (电源点表)
- id: 馈线ID
- version_id: 版本ID
- station_id: 厂站ID
- cb_id: 出线开关ID
- head_nd: 首节点

## API接口

### 1. 保存网架变更拓扑结构
```http
POST /problem/scheme/saveTopologyChange
Content-Type: application/json

{
    "problemId": 123,
    "feederId": "feeder001",
    "schemaName": "方案1",
    "description": "网架变更方案描述",
    "operateDataJson": "...",
    "setAsCurrent": true
}
```

### 2. 查询网架变更拓扑结构
```http
GET /problem/scheme/getTopologyChange/{versionId}
```

### 3. 删除网架变更拓扑结构
```http
DELETE /problem/scheme/deleteTopologyChange/{versionId}
```

## 关键设计原则

### 1. 分层架构
- **Controller层**: 负责接收请求和参数验证
- **Service层**: 负责业务逻辑处理和事务管理
- **Converter层**: 负责数据转换和映射
- **Validator层**: 负责数据验证和完整性检查
- **Mapper层**: 负责数据库操作

### 2. 数据一致性
- 使用事务确保数据一致性
- 版本管理确保数据可追溯
- 外键关联确保数据完整性

### 3. 错误处理
- 统一异常处理机制
- 详细的错误日志记录
- 友好的错误信息返回

### 4. 性能优化
- 批量插入减少数据库交互
- 合理的索引设计
- 数据验证前置避免无效操作

## 扩展性设计

### 1. 设备类型扩展
- 在GridChangeConstants中添加新的设备类型映射
- 更新TopologyConverter中的转换逻辑
- 添加对应的参数表和Mapper

### 2. 验证规则扩展
- 在GridChangeValidator中添加新的验证规则
- 支持自定义验证器注册
- 支持配置化验证规则

### 3. 数据格式扩展
- 支持多种输入数据格式
- 可插拔的转换器设计
- 版本兼容性处理

## 使用示例

```java
// 1. 构建请求
GridChangeRequest request = new GridChangeRequest();
request.setProblemId(123L);
request.setFeederId("feeder001");
request.setSchemaName("测试方案");
request.setDescription("测试描述");

// 2. 调用服务
R<GridChangeResponse> result = problemSchemeController.saveTopologyChange(request);

// 3. 处理结果
if (result.getCode() == 200) {
    GridChangeResponse response = result.getData();
    Long versionId = response.getVersionId();
    // 处理成功逻辑
} else {
    // 处理失败逻辑
}
```

## 注意事项

1. **数据量大时的处理**: 当节点数量很大时，建议分批处理，避免内存溢出
2. **并发处理**: 同一问题的多个版本创建需要考虑并发控制
3. **数据清理**: 定期清理过期版本数据，避免数据膨胀
4. **监控告警**: 添加关键指标监控，及时发现问题

## 总结

该设计方案具有以下优势：
- **规范性**: 遵循分层架构，职责清晰
- **可维护性**: 模块化设计，易于扩展和维护
- **可靠性**: 完善的验证和错误处理机制
- **性能**: 优化的数据处理流程
- **可追溯性**: 完整的版本管理机制

通过这套流程，可以确保网架结构到拓扑结构的转换和存储过程规范、可靠、高效。
